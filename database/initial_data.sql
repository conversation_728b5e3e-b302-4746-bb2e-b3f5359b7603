-- Trust Plus Financial Management System - Initial Data
-- البيانات الأولية للنظام

USE trust_plus;

-- إدراج الأدوار الأساسية
INSERT INTO roles (role_name, description) VALUES
('admin', 'مدير النظام - صلاحيات كاملة'),
('manager', 'مدير الفرع - إدارة العمليات والموظفين'),
('accountant', 'محاسب - إدارة الحسابات والتقارير المالية'),
('cashier', 'أمين الصندوق - العمليات النقدية والصرافة'),
('operator', 'موظف تشغيلي - العمليات الأساسية');

-- إدراج الصلاحيات الأساسية
INSERT INTO permissions (permission_name, description, module) VALUES
-- صلاحيات إدارة المستخدمين
('users.view', 'عرض المستخدمين', 'users'),
('users.create', 'إضافة مستخدم جديد', 'users'),
('users.edit', 'تعديل بيانات المستخدمين', 'users'),
('users.delete', 'حذف المستخدمين', 'users'),
('users.manage_roles', 'إدارة أدوار المستخدمين', 'users'),

-- صلاحيات إدارة العملاء
('customers.view', 'عرض العملاء', 'customers'),
('customers.create', 'إضافة عميل جديد', 'customers'),
('customers.edit', 'تعديل بيانات العملاء', 'customers'),
('customers.delete', 'حذف العملاء', 'customers'),
('customers.kyc', 'إدارة عمليات التحقق من العملاء', 'customers'),

-- صلاحيات العمليات المالية
('transactions.view', 'عرض المعاملات المالية', 'transactions'),
('transactions.create', 'إنشاء معاملات مالية', 'transactions'),
('transactions.edit', 'تعديل المعاملات المالية', 'transactions'),
('transactions.delete', 'حذف المعاملات المالية', 'transactions'),
('transactions.approve', 'اعتماد المعاملات المالية', 'transactions'),

-- صلاحيات الصرافة
('exchange.view', 'عرض عمليات الصرافة', 'exchange'),
('exchange.create', 'إنشاء عمليات صرافة', 'exchange'),
('exchange.rates', 'إدارة أسعار الصرف', 'exchange'),
('exchange.history', 'عرض تاريخ عمليات الصرافة', 'exchange'),
('exchange.profits', 'عرض تقارير أرباح الصرافة', 'exchange'),

-- صلاحيات التحويلات
('transfers.view', 'عرض التحويلات المالية', 'transfers'),
('transfers.create', 'إنشاء تحويلات مالية', 'transfers'),
('transfers.edit', 'تعديل وتحديث حالة التحويلات', 'transfers'),
('transfers.approve', 'اعتماد التحويلات المالية', 'transfers'),
('transfers.history', 'عرض تاريخ التحويلات', 'transfers'),

-- صلاحيات التقارير
('reports.financial', 'التقارير المالية', 'reports'),
('reports.accounting', 'التقارير المحاسبية', 'reports'),
('reports.compliance', 'تقارير الامتثال', 'reports'),
('reports.audit', 'تقارير التدقيق', 'reports'),

-- صلاحيات الإعدادات
('settings.view', 'عرض إعدادات النظام', 'settings'),
('settings.edit', 'تعديل إعدادات النظام', 'settings'),
('settings.accounts', 'إدارة دليل الحسابات', 'settings'),
('settings.currencies', 'إدارة العملات', 'settings'),

-- صلاحيات إدارة الصناديق والبنوك
('cash.view', 'عرض الصناديق والبنوك', 'cash'),
('cash.manage', 'إدارة الصناديق والبنوك', 'cash'),
('cash.reconcile', 'تسوية الحسابات', 'cash');

-- ربط الأدوار بالصلاحيات
-- مدير النظام - جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- مدير الفرع - معظم الصلاحيات عدا إدارة النظام
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions 
WHERE permission_name NOT IN ('users.delete', 'settings.edit');

-- المحاسب - الصلاحيات المحاسبية والمالية
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions 
WHERE module IN ('transactions', 'reports', 'cash', 'settings') 
   OR permission_name IN ('customers.view', 'exchange.view');

-- أمين الصندوق - العمليات النقدية والصرافة
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions 
WHERE module IN ('exchange', 'cash') 
   OR permission_name IN ('customers.view', 'customers.create', 'transactions.view', 'transactions.create');

-- الموظف التشغيلي - العمليات الأساسية فقط
INSERT INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions 
WHERE permission_name IN ('customers.view', 'transactions.view', 'exchange.view', 'transfers.view');

-- إدراج العملات الأساسية
INSERT INTO currencies (name, code, symbol, is_base_currency, decimal_places) VALUES
('الدولار الأمريكي', 'USD', '$', TRUE, 2),
('اليورو', 'EUR', '€', FALSE, 2),
('الجنيه الإسترليني', 'GBP', '£', FALSE, 2),
('الريال السعودي', 'SAR', 'ر.س', FALSE, 2),
('الدرهم الإماراتي', 'AED', 'د.إ', FALSE, 2),
('الدينار الكويتي', 'KWD', 'د.ك', FALSE, 3),
('الريال القطري', 'QAR', 'ر.ق', FALSE, 2),
('الدينار البحريني', 'BHD', 'د.ب', FALSE, 3);

-- إدراج الفرع الرئيسي
INSERT INTO branches (name, address, phone, email) VALUES
('الفرع الرئيسي', 'العنوان الرئيسي للشركة', '+966123456789', '<EMAIL>');

-- إنشاء المستخدم الإداري الأول
-- كلمة المرور: admin123 (يجب تغييرها فور تسجيل الدخول الأول)
INSERT INTO users (username, email, password_hash, full_name, role_id, branch_id) VALUES
('admin', '<EMAIL>', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'مدير النظام', 1, 1);

-- إعدادات النظام الأساسية
INSERT INTO settings (setting_key, setting_value, description) VALUES
('company_name', 'Trust Plus', 'اسم الشركة'),
('company_address', 'العنوان الرئيسي للشركة', 'عنوان الشركة'),
('company_phone', '+966123456789', 'هاتف الشركة'),
('company_email', '<EMAIL>', 'بريد الشركة الإلكتروني'),
('base_currency', 'USD', 'العملة الأساسية للنظام'),
('financial_year_start', '01-01', 'بداية السنة المالية (شهر-يوم)'),
('max_daily_exchange_limit', '100000', 'الحد الأقصى للصرافة اليومية'),
('kyc_required_amount', '10000', 'المبلغ المطلوب للتحقق من الهوية'),
('session_timeout', '3600', 'مهلة انتهاء الجلسة بالثواني'),
('backup_frequency', 'daily', 'تكرار النسخ الاحتياطي'),
('default_exchange_spread', '0.0050', 'هامش الربح الافتراضي للصرافة (0.5%)'),
('default_commission_rate', '0.0025', 'نسبة العمولة الافتراضية (0.25%)'),
('min_exchange_amount', '10', 'الحد الأدنى لعمليات الصرافة'),
('max_exchange_amount', '50000', 'الحد الأقصى لعمليات الصرافة'),
('exchange_receipt_template', 'default', 'قالب إيصال الصرافة'),
('auto_calculate_rates', '1', 'حساب أسعار الصرف تلقائياً');

-- إدراج أسعار الصرف الأولية (أسعار تجريبية)
INSERT INTO exchange_rates (from_currency_id, to_currency_id, buy_rate, sell_rate, effective_date, user_id) VALUES
-- USD إلى العملات الأخرى
(1, 2, 0.8500, 0.8600, CURDATE(), 1), -- USD to EUR
(1, 3, 0.7300, 0.7400, CURDATE(), 1), -- USD to GBP
(1, 4, 3.7500, 3.7600, CURDATE(), 1), -- USD to SAR
(1, 5, 3.6700, 3.6800, CURDATE(), 1), -- USD to AED
(1, 6, 0.3000, 0.3010, CURDATE(), 1), -- USD to KWD
(1, 7, 3.6400, 3.6500, CURDATE(), 1), -- USD to QAR
(1, 8, 0.3770, 0.3780, CURDATE(), 1), -- USD to BHD

-- العملات الأخرى إلى USD
(2, 1, 1.1600, 1.1700, CURDATE(), 1), -- EUR to USD
(3, 1, 1.3400, 1.3500, CURDATE(), 1), -- GBP to USD
(4, 1, 0.2660, 0.2670, CURDATE(), 1), -- SAR to USD
(5, 1, 0.2720, 0.2730, CURDATE(), 1), -- AED to USD
(6, 1, 3.3200, 3.3300, CURDATE(), 1), -- KWD to USD
(7, 1, 0.2740, 0.2750, CURDATE(), 1), -- QAR to USD
(8, 1, 2.6450, 2.6550, CURDATE(), 1); -- BHD to USD

-- إعدادات الصرافة الأولية
INSERT INTO exchange_settings (currency_pair, from_currency_id, to_currency_id, default_spread_percentage, commission_percentage, daily_limit, min_transaction_amount, max_transaction_amount, branch_id) VALUES
-- أزواج العملات الرئيسية
('USD/EUR', 1, 2, 0.0050, 0.0025, 100000, 10, 50000, 1),
('EUR/USD', 2, 1, 0.0050, 0.0025, 100000, 10, 50000, 1),
('USD/GBP', 1, 3, 0.0060, 0.0030, 80000, 10, 40000, 1),
('GBP/USD', 3, 1, 0.0060, 0.0030, 80000, 10, 40000, 1),
('USD/SAR', 1, 4, 0.0030, 0.0015, 200000, 10, 100000, 1),
('SAR/USD', 4, 1, 0.0030, 0.0015, 200000, 10, 100000, 1),
('USD/AED', 1, 5, 0.0035, 0.0020, 150000, 10, 75000, 1),
('AED/USD', 5, 1, 0.0035, 0.0020, 150000, 10, 75000, 1),
('USD/KWD', 1, 6, 0.0040, 0.0025, 50000, 10, 25000, 1),
('KWD/USD', 6, 1, 0.0040, 0.0025, 50000, 10, 25000, 1),
('USD/QAR', 1, 7, 0.0035, 0.0020, 120000, 10, 60000, 1),
('QAR/USD', 7, 1, 0.0035, 0.0020, 120000, 10, 60000, 1),
('USD/BHD', 1, 8, 0.0045, 0.0025, 40000, 10, 20000, 1),
('BHD/USD', 8, 1, 0.0045, 0.0025, 40000, 10, 20000, 1);
