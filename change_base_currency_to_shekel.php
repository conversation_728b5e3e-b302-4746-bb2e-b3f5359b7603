<?php
/**
 * Trust Plus - Change Base Currency to Israeli Shekel
 * تغيير العملة الأساسية إلى الشيكل الإسرائيلي
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🏦 تغيير العملة الأساسية إلى الشيكل الإسرائيلي</h1>";
echo "<hr>";

try {
    // تحميل الملفات المطلوبة
    require_once 'config.php';
    require_once 'includes/database.php';
    require_once 'includes/auth.php';
    
    // بدء الجلسة
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<h2>📋 فحص العملات الحالية</h2>";
    
    // فحص العملة الأساسية الحالية
    $stmt = $db->prepare("SELECT * FROM currencies WHERE is_base_currency = 1");
    $stmt->execute();
    $current_base = $stmt->fetch();
    
    if ($current_base) {
        echo "<p><strong>العملة الأساسية الحالية:</strong> " . htmlspecialchars($current_base['name']) . " (" . htmlspecialchars($current_base['code']) . ") " . htmlspecialchars($current_base['symbol']) . "</p>";
    } else {
        echo "<p>⚠️ لا توجد عملة أساسية محددة</p>";
    }
    
    // فحص وجود الشيكل
    $stmt = $db->prepare("SELECT * FROM currencies WHERE code = 'ILS'");
    $stmt->execute();
    $shekel = $stmt->fetch();
    
    if (!$shekel) {
        throw new Exception('عملة الشيكل الإسرائيلي غير موجودة في النظام');
    }
    
    echo "<p><strong>عملة الشيكل:</strong> " . htmlspecialchars($shekel['name']) . " (" . htmlspecialchars($shekel['code']) . ") " . htmlspecialchars($shekel['symbol']) . "</p>";
    
    if ($shekel['is_base_currency']) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
        echo "<h3>ℹ️ الشيكل هو العملة الأساسية بالفعل</h3>";
        echo "<p>لا حاجة لإجراء أي تغييرات</p>";
        echo "</div>";
    } else {
        echo "<h2>🔄 تغيير العملة الأساسية</h2>";
        
        $db->beginTransaction();
        
        try {
            // إزالة العملة الأساسية من جميع العملات
            $stmt = $db->prepare("UPDATE currencies SET is_base_currency = 0");
            $stmt->execute();
            echo "<p>✅ تم إزالة العملة الأساسية من جميع العملات</p>";
            
            // تعيين الشيكل كعملة أساسية
            $stmt = $db->prepare("UPDATE currencies SET is_base_currency = 1 WHERE code = 'ILS'");
            $stmt->execute();
            echo "<p>✅ تم تعيين الشيكل كعملة أساسية</p>";
            
            // تحديث إعدادات النظام
            $stmt = $db->prepare("UPDATE settings SET setting_value = 'ILS' WHERE setting_key = 'base_currency'");
            $stmt->execute();
            echo "<p>✅ تم تحديث إعدادات النظام</p>";
            
            $db->commit();
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
            echo "<h3>🎉 تم تغيير العملة الأساسية بنجاح!</h3>";
            echo "<p>الشيكل الإسرائيلي (ILS) ₪ هو الآن العملة الأساسية للنظام</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            $db->rollback();
            throw new Exception('فشل في تغيير العملة الأساسية: ' . $e->getMessage());
        }
    }
    
    echo "<hr>";
    echo "<h2>💱 إعادة تنظيم أسعار الصرف</h2>";
    
    // الحصول على جميع العملات
    $stmt = $db->prepare("SELECT * FROM currencies WHERE is_active = 1 AND code != 'ILS'");
    $stmt->execute();
    $other_currencies = $stmt->fetchAll();
    
    echo "<p>إعادة تنظيم أسعار الصرف لتكون بالنسبة للشيكل...</p>";
    
    // أسعار الصرف الجديدة (بالنسبة للشيكل)
    $new_rates = [
        'USD' => ['buy' => 0.270, 'sell' => 0.275], // دولار مقابل شيكل
        'EUR' => ['buy' => 0.230, 'sell' => 0.235], // يورو مقابل شيكل
        'GBP' => ['buy' => 0.197, 'sell' => 0.202], // جنيه مقابل شيكل
        'SAR' => ['buy' => 1.013, 'sell' => 1.018], // ريال سعودي مقابل شيكل
        'AED' => ['buy' => 0.992, 'sell' => 0.997], // درهم مقابل شيكل
        'KWD' => ['buy' => 0.081, 'sell' => 0.083], // دينار كويتي مقابل شيكل
        'QAR' => ['buy' => 0.983, 'sell' => 0.988], // ريال قطري مقابل شيكل
        'BHD' => ['buy' => 0.102, 'sell' => 0.104]  // دينار بحريني مقابل شيكل
    ];
    
    // الحصول على معرف المستخدم الافتراضي
    $stmt = $db->prepare("SELECT id FROM users WHERE username = 'admin' LIMIT 1");
    $stmt->execute();
    $admin_user = $stmt->fetch();
    $user_id = $admin_user['id'] ?? 1;
    
    foreach ($other_currencies as $currency) {
        $code = $currency['code'];
        
        if (isset($new_rates[$code])) {
            // حذف أسعار الصرف القديمة
            $stmt = $db->prepare("
                DELETE FROM exchange_rates 
                WHERE (from_currency_id = :currency_id AND to_currency_id = :shekel_id)
                   OR (from_currency_id = :shekel_id AND to_currency_id = :currency_id)
            ");
            $stmt->bindParam(':currency_id', $currency['id']);
            $stmt->bindParam(':shekel_id', $shekel['id']);
            $stmt->execute();
            
            // إضافة أسعار جديدة: من العملة إلى الشيكل
            $stmt = $db->prepare("
                INSERT INTO exchange_rates (from_currency_id, to_currency_id, buy_rate, sell_rate, effective_date, user_id, is_active)
                VALUES (:from_currency_id, :to_currency_id, :buy_rate, :sell_rate, CURDATE(), :user_id, 1)
            ");
            
            // من العملة الأجنبية إلى الشيكل
            $buy_rate_to_ils = 1 / $new_rates[$code]['sell']; // عكس السعر
            $sell_rate_to_ils = 1 / $new_rates[$code]['buy'];
            
            $stmt->bindParam(':from_currency_id', $currency['id']);
            $stmt->bindParam(':to_currency_id', $shekel['id']);
            $stmt->bindParam(':buy_rate', $buy_rate_to_ils);
            $stmt->bindParam(':sell_rate', $sell_rate_to_ils);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
            // من الشيكل إلى العملة الأجنبية
            $stmt->bindParam(':from_currency_id', $shekel['id']);
            $stmt->bindParam(':to_currency_id', $currency['id']);
            $stmt->bindParam(':buy_rate', $new_rates[$code]['buy']);
            $stmt->bindParam(':sell_rate', $new_rates[$code]['sell']);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
            echo "<p>✅ تم تحديث أسعار صرف {$currency['name']} ({$code})</p>";
            echo "<ul>";
            echo "<li>ILS → $code: شراء " . number_format($new_rates[$code]['buy'], 6) . "، بيع " . number_format($new_rates[$code]['sell'], 6) . "</li>";
            echo "<li>$code → ILS: شراء " . number_format($buy_rate_to_ils, 6) . "، بيع " . number_format($sell_rate_to_ils, 6) . "</li>";
            echo "</ul>";
        }
    }
    
    echo "<hr>";
    echo "<h2>📊 العملات النهائية</h2>";
    
    // عرض جميع العملات بعد التحديث
    $stmt = $db->prepare("SELECT * FROM currencies WHERE is_active = 1 ORDER BY is_base_currency DESC, name ASC");
    $stmt->execute();
    $final_currencies = $stmt->fetchAll();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>💰 العملات المتاحة في النظام:</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;'>";
    foreach ($final_currencies as $currency) {
        echo "<div style='background: white; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; text-align: center;'>";
        echo "<div style='font-size: 2em; margin-bottom: 10px;'>" . htmlspecialchars($currency['symbol']) . "</div>";
        echo "<div style='font-weight: bold; margin-bottom: 5px;'>" . htmlspecialchars($currency['name']) . "</div>";
        echo "<div style='color: #6c757d; margin-bottom: 10px;'>" . htmlspecialchars($currency['code']) . "</div>";
        if ($currency['is_base_currency']) {
            echo "<div style='background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em; display: inline-block;'>العملة الأساسية</div>";
        }
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<hr>";
    echo "<h2>⚙️ تحديث ملف التكوين</h2>";
    
    // قراءة ملف config.php
    $config_file = 'config.php';
    if (file_exists($config_file)) {
        $config_content = file_get_contents($config_file);
        
        // تحديث العملة الافتراضية
        $config_content = preg_replace(
            "/define\('DEFAULT_CURRENCY',\s*'[^']*'\);/",
            "define('DEFAULT_CURRENCY', 'ILS');",
            $config_content
        );
        
        if (file_put_contents($config_file, $config_content)) {
            echo "<p>✅ تم تحديث ملف التكوين</p>";
        } else {
            echo "<p>⚠️ فشل في تحديث ملف التكوين - يرجى التحديث يدوياً</p>";
        }
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #28a745;'>";
    echo "<h2>🎉 تم التحديث بنجاح!</h2>";
    echo "<h3>✅ ما تم إنجازه:</h3>";
    echo "<ul>";
    echo "<li>✅ تم تعيين الشيكل الإسرائيلي (ILS) كعملة أساسية</li>";
    echo "<li>✅ تم تحديث إعدادات النظام</li>";
    echo "<li>✅ تم إعادة تنظيم أسعار الصرف لتكون بالنسبة للشيكل</li>";
    echo "<li>✅ تم تحديث ملف التكوين</li>";
    echo "<li>✅ جميع العمليات ستتم الآن بالشيكل كعملة أساسية</li>";
    echo "</ul>";
    
    echo "<h3>🔗 اختبر النظام الآن:</h3>";
    echo "<p><a href='dashboard/exchange.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>💱 عمليات الصرافة</a></p>";
    echo "<p><a href='dashboard/settings.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>⚙️ إعدادات النظام</a></p>";
    echo "<p><a href='dashboard/exchange_rates.php' target='_blank' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📊 أسعار الصرف</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في العملية</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>📝 ملاحظات مهمة:</h3>";
echo "<ul>";
echo "<li><strong>العملة الأساسية:</strong> الشيكل الإسرائيلي (ILS) ₪</li>";
echo "<li><strong>أسعار الصرف:</strong> جميع الأسعار الآن بالنسبة للشيكل</li>";
echo "<li><strong>العمليات:</strong> جميع العمليات المالية ستتم بالشيكل كعملة أساسية</li>";
echo "<li><strong>التقارير:</strong> التقارير المالية ستعرض بالشيكل</li>";
echo "<li><strong>تحديث الأسعار:</strong> يمكن تحديث أسعار الصرف من صفحة إدارة أسعار الصرف</li>";
echo "</ul>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    line-height: 1.6;
}
</style>
