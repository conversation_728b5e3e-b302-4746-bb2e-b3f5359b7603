# حماية مجلد المستندات المرفوعة
# منع الوصول المباشر للملفات

# منع تنفيذ السكريبت
Options -ExecCGI
AddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi

# السماح فقط بأنواع الملفات المحددة
<FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# منع الوصول لجميع الملفات الأخرى
<FilesMatch "^(?!.*\.(jpg|jpeg|png|gif|pdf|doc|docx)$).*$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع عرض محتويات المجلد
Options -Indexes

# إعدادات الأمان الإضافية
<IfModule mod_headers.c>
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options DENY
</IfModule>
