<?php
/**
 * Trust Plus - Test Navbar Fixes
 * اختبار إصلاحات الشريط العلوي
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 اختبار إصلاحات الشريط العلوي</h1>";
echo "<hr>";

try {
    // تحميل الملفات
    require_once 'config.php';
    require_once 'includes/database.php';
    require_once 'includes/auth.php';
    
    // بدء الجلسة
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    echo "<h2>📋 فحص الملفات والإعدادات</h2>";
    
    // فحص الملفات المطلوبة
    $files_to_check = [
        'includes/navbar.php' => 'ملف الشريط العلوي',
        'includes/footer.php' => 'ملف التذييل',
        'assets/css/main.css' => 'ملف CSS الرئيسي',
        'assets/js/main.js' => 'ملف JavaScript الرئيسي',
        'dashboard/index.php' => 'لوحة التحكم'
    ];
    
    foreach ($files_to_check as $file => $description) {
        if (file_exists($file)) {
            echo "<p>✅ $description: <code>$file</code></p>";
        } else {
            echo "<p>❌ $description: <code>$file</code> - مفقود</p>";
        }
    }
    
    echo "<hr>";
    echo "<h2>🔐 اختبار تسجيل الدخول</h2>";
    
    $auth = new Auth();
    $login_result = $auth->login('admin', 'admin123');
    
    if ($login_result['success']) {
        echo "<p>✅ تسجيل الدخول نجح</p>";
        
        $current_user = $auth->getCurrentUser();
        if ($current_user) {
            echo "<p>✅ تم الحصول على معلومات المستخدم</p>";
            echo "<p>👤 المستخدم: " . htmlspecialchars($current_user['full_name']) . "</p>";
            
            // فحص البيانات المطلوبة للشريط العلوي
            echo "<h3>📊 فحص بيانات المستخدم للشريط العلوي:</h3>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> " . ($current_user['id'] ?? 'مفقود') . "</li>";
            echo "<li><strong>اسم المستخدم:</strong> " . ($current_user['username'] ?? 'مفقود') . "</li>";
            echo "<li><strong>الاسم الكامل:</strong> " . ($current_user['full_name'] ?? 'مفقود') . "</li>";
            echo "<li><strong>الدور:</strong> " . ($current_user['role_name'] ?? $current_user['role'] ?? 'مفقود') . "</li>";
            echo "<li><strong>معرف الدور:</strong> " . ($current_user['role_id'] ?? 'مفقود') . "</li>";
            echo "<li><strong>معرف الفرع:</strong> " . ($current_user['branch_id'] ?? 'مفقود') . "</li>";
            echo "</ul>";
            
        } else {
            echo "<p>❌ فشل في الحصول على معلومات المستخدم</p>";
        }
        
        // تسجيل الخروج
        $auth->logout();
        echo "<p>✅ تم تسجيل الخروج</p>";
        
    } else {
        echo "<p>❌ فشل تسجيل الدخول: " . htmlspecialchars($login_result['message']) . "</p>";
    }
    
    echo "<hr>";
    echo "<h2>🎨 اختبار تحميل الأصول</h2>";
    
    // فحص ملفات CSS
    $css_files = [
        'assets/css/bootstrap.min.css',
        'assets/css/fontawesome.min.css',
        'assets/css/main.css'
    ];
    
    foreach ($css_files as $css_file) {
        if (file_exists($css_file)) {
            $size = filesize($css_file);
            echo "<p>✅ $css_file - الحجم: " . number_format($size / 1024, 2) . " KB</p>";
        } else {
            echo "<p>❌ $css_file - مفقود</p>";
        }
    }
    
    // فحص ملفات JavaScript
    $js_files = [
        'assets/js/bootstrap.bundle.min.js',
        'assets/js/main.js'
    ];
    
    foreach ($js_files as $js_file) {
        if (file_exists($js_file)) {
            $size = filesize($js_file);
            echo "<p>✅ $js_file - الحجم: " . number_format($size / 1024, 2) . " KB</p>";
        } else {
            echo "<p>❌ $js_file - مفقود</p>";
        }
    }
    
    echo "<hr>";
    echo "<h2>🧪 اختبار JavaScript</h2>";
    
    // فحص وجود أخطاء JavaScript في footer.php
    $footer_content = file_get_contents('includes/footer.php');
    if (strpos($footer_content, "role: '<?php echo htmlspecialchars(\$current_user['role_name']") !== false) {
        echo "<p>✅ تم إصلاح مشكلة JavaScript في footer.php</p>";
    } else {
        echo "<p>⚠️ قد تكون هناك مشكلة في footer.php</p>";
    }
    
    // فحص وجود دوال JavaScript المطلوبة
    $navbar_content = file_get_contents('includes/navbar.php');
    if (strpos($navbar_content, 'window.performSearch') !== false) {
        echo "<p>✅ دالة البحث السريع موجودة</p>";
    } else {
        echo "<p>❌ دالة البحث السريع مفقودة</p>";
    }
    
    if (strpos($navbar_content, 'TrustPlus.ui.toggleSidebar') !== false) {
        echo "<p>✅ دالة تبديل الشريط الجانبي موجودة</p>";
    } else {
        echo "<p>❌ دالة تبديل الشريط الجانبي مفقودة</p>";
    }
    
    echo "<hr>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #28a745;'>";
    echo "<h2>🎉 ملخص الإصلاحات</h2>";
    echo "<h3>✅ تم إصلاح المشاكل التالية:</h3>";
    echo "<ul>";
    echo "<li>✅ إصلاح خطأ JavaScript في footer.php (مشكلة \$current_user['role'])</li>";
    echo "<li>✅ تفعيل جميع أيقونات الشريط العلوي</li>";
    echo "<li>✅ إضافة وظائف تفاعلية للبحث السريع</li>";
    echo "<li>✅ إضافة وظائف تفاعلية للإشعارات</li>";
    echo "<li>✅ إضافة وظائف تبديل الشريط الجانبي</li>";
    echo "<li>✅ تحسين CSS للشريط العلوي</li>";
    echo "<li>✅ إضافة تأثيرات متحركة للأيقونات</li>";
    echo "<li>✅ تحسين التصميم المتجاوب</li>";
    echo "</ul>";
    
    echo "<h3>🚀 الميزات الجديدة:</h3>";
    echo "<ul>";
    echo "<li>🔍 بحث سريع تفاعلي</li>";
    echo "<li>🔔 إشعارات متحركة</li>";
    echo "<li>⚙️ إعدادات سريعة</li>";
    echo "<li>👤 معلومات مستخدم محسنة</li>";
    echo "<li>📱 تصميم متجاوب للهواتف</li>";
    echo "</ul>";
    
    echo "<p><strong>🔗 اختبر النظام الآن:</strong></p>";
    echo "<p><a href='auth/login.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔑 تسجيل الدخول</a></p>";
    echo "<p><a href='dashboard/index.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📊 لوحة التحكم</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في الاختبار</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>📝 تعليمات الاستخدام:</h3>";
echo "<ol>";
echo "<li><strong>تسجيل الدخول:</strong> استخدم admin / admin123</li>";
echo "<li><strong>البحث السريع:</strong> انقر على أيقونة البحث في الشريط العلوي</li>";
echo "<li><strong>الإشعارات:</strong> انقر على أيقونة الجرس لعرض الإشعارات</li>";
echo "<li><strong>الإعدادات:</strong> انقر على أيقونة الترس للوصول للإعدادات السريعة</li>";
echo "<li><strong>الملف الشخصي:</strong> انقر على اسم المستخدم لعرض الخيارات</li>";
echo "<li><strong>الشريط الجانبي:</strong> انقر على أيقونة القائمة لتبديل الشريط الجانبي</li>";
echo "</ol>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul, ol {
    line-height: 1.6;
}
</style>
