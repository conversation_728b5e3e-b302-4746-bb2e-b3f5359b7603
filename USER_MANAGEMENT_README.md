# نظام إدارة المستخدمين والأدوار والصلاحيات - Trust Plus

## نظرة عامة

تم إنشاء نظام شامل لإدارة المستخدمين والأدوار والصلاحيات لنظام Trust Plus المالي. يتضمن النظام ثلاث صفحات رئيسية مع واجهات مستخدم متجاوبة وآمنة.

## الملفات المنشأة

### صفحات الواجهة الأمامية:
1. **dashboard/users.php** - صفحة إدارة المستخدمين
2. **dashboard/roles.php** - صفحة إدارة الأدوار
3. **dashboard/permissions.php** - صفحة إدارة الصلاحيات

### ملفات الخلفية (Backend):
1. **includes/user_manager.php** - فئة إدارة المستخدمين
2. **includes/role_manager.php** - فئة إدارة الأدوار
3. **includes/permission_manager.php** - فئة إدارة الصلاحيات

### ملفات التهيئة:
1. **setup_user_management.php** - ملف تهيئة قاعدة البيانات والبيانات الأساسية

## المميزات الرئيسية

### إدارة المستخدمين:
- ✅ عرض قائمة المستخدمين مع معلومات الأدوار والفروع
- ✅ إضافة مستخدمين جدد مع التحقق من صحة البيانات
- ✅ تعديل بيانات المستخدمين
- ✅ تفعيل/إلغاء تفعيل المستخدمين
- ✅ إعادة تعيين كلمات المرور
- ✅ البحث والفلترة حسب الدور والحالة
- ✅ عرض محاولات تسجيل الدخول الفاشلة
- ✅ إدارة قفل الحسابات

### إدارة الأدوار:
- ✅ عرض قائمة الأدوار مع عدد المستخدمين
- ✅ إضافة أدوار جديدة
- ✅ تعديل الأدوار الموجودة
- ✅ تفعيل/إلغاء تفعيل الأدوار
- ✅ إدارة صلاحيات كل دور
- ✅ واجهة تفاعلية لتحديد الصلاحيات
- ✅ حماية دور المدير من التعديل

### إدارة الصلاحيات:
- ✅ عرض الصلاحيات مجمعة حسب الوحدات
- ✅ إضافة صلاحيات جديدة
- ✅ تعديل الصلاحيات الموجودة
- ✅ إنشاء وحدات جديدة
- ✅ البحث والفلترة حسب الوحدة
- ✅ التحقق من صحة أسماء الصلاحيات

## الأمان والحماية

### حماية CSRF:
- ✅ رموز CSRF في جميع النماذج
- ✅ التحقق من صحة الرموز قبل تنفيذ العمليات

### التحقق من الصلاحيات:
- ✅ فحص الصلاحيات قبل الوصول للصفحات
- ✅ فحص الصلاحيات قبل تنفيذ العمليات
- ✅ رسائل خطأ واضحة عند عدم وجود صلاحيات

### تسجيل المراجعة:
- ✅ تسجيل جميع العمليات في جدول audit_logs
- ✅ تسجيل معلومات المستخدم وعنوان IP
- ✅ وصف تفصيلي للعمليات المنفذة

### التحقق من صحة البيانات:
- ✅ التحقق من البيانات في الخلفية والواجهة الأمامية
- ✅ رسائل خطأ واضحة ومفيدة
- ✅ منع إدخال بيانات ضارة

## قاعدة البيانات

### الجداول المنشأة:
1. **users** - جدول المستخدمين
2. **roles** - جدول الأدوار
3. **permissions** - جدول الصلاحيات
4. **role_permissions** - جدول ربط الأدوار بالصلاحيات
5. **branches** - جدول الفروع
6. **audit_logs** - جدول سجل المراجعة

### البيانات الافتراضية:
- **الأدوار**: مدير النظام، مدير الفرع، محاسب، أمين الصندوق، موظف تشغيلي
- **الصلاحيات**: 30+ صلاحية مجمعة في 12 وحدة
- **المستخدم الافتراضي**: admin / admin123
- **الفرع الافتراضي**: الفرع الرئيسي

## التثبيت والتشغيل

### 1. تشغيل ملف التهيئة:
```
http://localhost/Trust%20Plus/setup_user_management.php
```

### 2. تسجيل الدخول:
```
اسم المستخدم: admin
كلمة المرور: admin123
```

### 3. الوصول للصفحات:
```
إدارة المستخدمين: http://localhost/Trust%20Plus/dashboard/users.php
إدارة الأدوار: http://localhost/Trust%20Plus/dashboard/roles.php
إدارة الصلاحيات: http://localhost/Trust%20Plus/dashboard/permissions.php
```

## واجهة المستخدم

### التصميم:
- ✅ تصميم متجاوب يعمل على جميع الأجهزة
- ✅ استخدام Bootstrap 5 للتنسيق
- ✅ أيقونات Font Awesome
- ✅ ألوان متناسقة مع نظام Trust Plus

### التفاعل:
- ✅ نماذج منبثقة للتأكيد
- ✅ رسائل نجاح وخطأ واضحة
- ✅ تحديث فوري للبيانات
- ✅ البحث والفلترة المباشرة

### إمكانية الوصول:
- ✅ دعم اللغة العربية بالكامل
- ✅ تخطيط من اليمين لليسار (RTL)
- ✅ تسميات واضحة للنماذج
- ✅ رسائل مساعدة للمستخدمين

## الصلاحيات المتاحة

### إدارة المستخدمين:
- users.view - عرض المستخدمين
- users.create - إضافة مستخدمين
- users.edit - تعديل المستخدمين
- users.delete - حذف المستخدمين

### إدارة الأدوار:
- roles.view - عرض الأدوار
- roles.create - إضافة أدوار
- roles.edit - تعديل الأدوار
- roles.delete - حذف الأدوار

### إدارة الصلاحيات:
- permissions.view - عرض الصلاحيات
- permissions.create - إضافة صلاحيات
- permissions.edit - تعديل الصلاحيات
- permissions.delete - حذف الصلاحيات

## ملاحظات مهمة

1. **دور المدير**: لا يمكن تعديل أو حذف دور المدير (ID = 1)
2. **المستخدم الحالي**: لا يمكن للمستخدم تعديل حالته الخاصة
3. **كلمات المرور**: يتم تشفيرها باستخدام password_hash()
4. **الجلسات**: تنتهي صلاحيتها بعد ساعة واحدة من عدم النشاط
5. **سجل المراجعة**: يسجل جميع العمليات مع تفاصيل المستخدم والوقت

## الدعم والصيانة

النظام مصمم ليكون قابلاً للتوسع والصيانة:
- كود منظم ومعلق باللغة العربية
- فصل منطق العمل عن واجهة المستخدم
- معالجة شاملة للأخطاء
- تسجيل مفصل للعمليات
- إمكانية إضافة وحدات وصلاحيات جديدة بسهولة
