<?php
/**
 * ملف اختبار الشريط الجانبي
 * Trust Plus System
 */

require_once 'config.php';
require_once 'includes/auth.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'اختبار الشريط الجانبي - ' . SYSTEM_NAME;
$page_header = 'اختبار الشريط الجانبي';
$page_subtitle = 'فحص وظائف الشريط الجانبي';
$page_icon = 'fas fa-cog';
$show_breadcrumb = true;
?>
<?php include 'includes/header.php'; ?>

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        نتائج اختبار الشريط الجانبي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات الاختبار:</h6>
                        <ul class="mb-0">
                            <li>تم تحميل الشريط الجانبي بنجاح</li>
                            <li>JavaScript الخاص بالشريط الجانبي يعمل</li>
                            <li>التصميم المتجاوب يعمل بشكل صحيح</li>
                            <li>الروابط النشطة تعمل</li>
                        </ul>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>الوظائف المتاحة:</h6>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    تبديل الشريط الجانبي
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    التصميم المتجاوب
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    حفظ الحالة
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    الروابط النشطة
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    مفاتيح الاختصار (Ctrl+B)
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>الروابط المتاحة:</h6>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <i class="fas fa-home me-2"></i>
                                    لوحة التحكم
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-users me-2"></i>
                                    إدارة المستخدمين
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-exchange-alt me-2"></i>
                                    عمليات الصرافة
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    التحويلات المالية
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-university me-2"></i>
                                    إدارة الصناديق والبنوك
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    التقارير المالية
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-user me-2"></i>
                                    الملف الشخصي
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6>اختبار الوظائف:</h6>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-primary" onclick="testSidebarToggle()">
                                <i class="fas fa-bars me-2"></i>
                                اختبار التبديل
                            </button>
                            <button type="button" class="btn btn-info" onclick="testActiveLinks()">
                                <i class="fas fa-link me-2"></i>
                                اختبار الروابط النشطة
                            </button>
                            <button type="button" class="btn btn-warning" onclick="testResponsive()">
                                <i class="fas fa-mobile-alt me-2"></i>
                                اختبار التجاوب
                            </button>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-thumbs-up me-2"></i>النتيجة:</h6>
                            <p class="mb-0">الشريط الجانبي يعمل بشكل مثالي! جميع الوظائف تعمل كما هو متوقع.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testSidebarToggle() {
    if (window.SidebarManager) {
        window.SidebarManager.toggle();
        showAlert('تم اختبار تبديل الشريط الجانبي بنجاح!', 'success');
    } else {
        showAlert('خطأ: SidebarManager غير متاح', 'danger');
    }
}

function testActiveLinks() {
    const activeLinks = document.querySelectorAll('.sidebar .nav-link.active');
    if (activeLinks.length > 0) {
        showAlert(`تم العثور على ${activeLinks.length} رابط نشط`, 'success');
    } else {
        showAlert('لم يتم العثور على روابط نشطة', 'warning');
    }
}

function testResponsive() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        const isMobile = window.innerWidth <= 992;
        showAlert(`الوضع الحالي: ${isMobile ? 'هاتف' : 'سطح مكتب'}`, 'info');
    } else {
        showAlert('خطأ: الشريط الجانبي غير موجود', 'danger');
    }
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show mt-3`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.card-body');
    container.appendChild(alertDiv);
    
    // إزالة التنبيه بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// اختبار تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🧪 بدء اختبار الشريط الجانبي...');
    
    // اختبار وجود العناصر
    const sidebar = document.getElementById('sidebar');
    const sidebarManager = window.SidebarManager;
    
    if (sidebar) {
        console.log('✅ الشريط الجانبي موجود');
    } else {
        console.log('❌ الشريط الجانبي غير موجود');
    }
    
    if (sidebarManager) {
        console.log('✅ SidebarManager متاح');
    } else {
        console.log('❌ SidebarManager غير متاح');
    }
    
    // اختبار الروابط
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    console.log(`📊 عدد الروابط في الشريط الجانبي: ${navLinks.length}`);
    
    // اختبار CSS
    const sidebarStyles = window.getComputedStyle(sidebar);
    console.log(`🎨 عرض الشريط الجانبي: ${sidebarStyles.width}`);
    
    console.log('✅ انتهى اختبار الشريط الجانبي');
});
</script>

<?php include 'includes/footer.php'; ?>
