<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة تسجيل الدخول - Trust Plus</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 700px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.2rem;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            width: 100%;
            box-sizing: border-box;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .steps {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .steps h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .steps ol {
            margin: 0;
            padding-right: 20px;
        }
        
        .steps li {
            margin: 8px 0;
            color: #555;
        }
        
        .error-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        
        @media (max-width: 600px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح مشكلة تسجيل الدخول</h1>
            <p>Trust Plus - نظام إدارة الصرافة والتحويلات المالية</p>
        </div>
        
        <div class="alert alert-danger">
            <h4>❌ المشكلة المكتشفة:</h4>
            <p><strong>SQLSTATE[HY093]: Invalid parameter number</strong></p>
            <p>هذا الخطأ يحدث بسبب مشكلة في استعلام SQL في نظام المصادقة.</p>
        </div>
        
        <div class="error-details">
            <strong>تفاصيل الخطأ:</strong><br>
            - خطأ في ربط المعاملات في استعلام SQL<br>
            - مشكلة في دالة تسجيل الدخول<br>
            - قد تكون قاعدة البيانات غير مُعدة بشكل صحيح
        </div>
        
        <div class="alert alert-info">
            <h4>🔍 الحلول المتاحة:</h4>
            <p>تم إنشاء عدة أدوات لإصلاح هذه المشكلة تلقائياً</p>
        </div>
        
        <div class="steps">
            <h3>📝 خطوات الإصلاح:</h3>
            <ol>
                <li><strong>تشغيل إصلاح SQL:</strong> سيقوم بإعادة إنشاء قاعدة البيانات</li>
                <li><strong>اختبار النظام:</strong> للتحقق من عمل تسجيل الدخول</li>
                <li><strong>إعداد قاعدة البيانات:</strong> إذا لم تعمل الخطوات السابقة</li>
                <li><strong>تسجيل الدخول:</strong> باستخدام admin / admin123</li>
            </ol>
        </div>
        
        <div class="grid">
            <a href="fix_sql_error.php" class="btn btn-danger">
                🔧 إصلاح خطأ SQL
            </a>
            
            <a href="test_login_simple.php" class="btn btn-info">
                🧪 اختبار تسجيل الدخول
            </a>
        </div>
        
        <div class="grid">
            <a href="setup_database.php" class="btn btn-primary">
                🗄️ إعداد قاعدة البيانات
            </a>
            
            <a href="debug.php" class="btn btn-success">
                🐛 تشخيص شامل
            </a>
        </div>
        
        <div class="alert alert-success">
            <h4>✅ بعد الإصلاح:</h4>
            <p><strong>اسم المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin123</p>
            <p><strong>رابط تسجيل الدخول:</strong> <a href="auth/login.php" style="color: #155724;">auth/login.php</a></p>
        </div>
        
        <div class="steps">
            <h3>⚠️ إذا لم تعمل الحلول:</h3>
            <ol>
                <li>تأكد من تشغيل خادم MySQL في XAMPP</li>
                <li>تحقق من إعدادات قاعدة البيانات في config.php</li>
                <li>تأكد من وجود قاعدة بيانات باسم "trust_plus"</li>
                <li>تحقق من صلاحيات المستخدم لقاعدة البيانات</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #666; font-size: 0.9rem;">
            <p>Trust Plus v2.0.0 - أداة إصلاح الأخطاء 🛠️</p>
        </div>
    </div>
</body>
</html>
