# دليل البدء السريع - Trust Plus

## خطوات التشغيل السريع

### 1. تشغيل XAMPP
```bash
# تشغيل Apache و MySQL
sudo /opt/lampp/lampp start
```

### 2. إعد<PERSON> قاعدة البيانات
1. افتح المتصفح وانتقل إلى: `http://localhost/Trust Plus/install/setup.php`
2. اضغط على "بدء إعداد قاعدة البيانات"
3. انتظر حتى ظهور رسالة "تم إعداد النظام بنجاح!"

### 3. تسجيل الدخول الأول
1. انتقل إلى: `http://localhost/Trust Plus/`
2. استخدم البيانات التالية:
   - **اسم المستخدم:** `admin`
   - **كلمة المرور:** `admin123`

### 4. تغيير كلمة المرور (مهم!)
1. بعد تسجيل الدخول، اضغط على اسمك في الزاوية العلوية
2. اختر "تغيير كلمة المرور"
3. أدخل كلمة مرور قوية جديدة

## الميزات المتوفرة حالياً

✅ **تم تطويرها:**
- نظام تسجيل الدخول والأمان
- لوحة التحكم الرئيسية
- إدارة الأدوار والصلاحيات
- عرض العملات المتاحة
- إضافة عملات جديدة
- تغيير كلمة المرور

🔄 **قيد التطوير:**
- إدارة العملاء
- عمليات الصرافة
- التحويلات المالية
- التقارير المالية

## الأدوار المتاحة

1. **مدير النظام (admin)** - جميع الصلاحيات
2. **مدير الفرع (manager)** - إدارة العمليات
3. **المحاسب (accountant)** - العمليات المحاسبية
4. **أمين الصندوق (cashier)** - العمليات النقدية
5. **الموظف التشغيلي (operator)** - العمليات الأساسية

## استكشاف الأخطاء

### مشكلة في الاتصال بقاعدة البيانات
```bash
# تأكد من تشغيل MySQL
sudo /opt/lampp/lampp status

# إعادة تشغيل XAMPP
sudo /opt/lampp/lampp restart
```

### صفحة فارغة أو خطأ 500
- تحقق من ملفات السجلات في `/opt/lampp/logs/`
- تأكد من صحة أذونات الملفات

### لا يمكن الوصول للموقع
- تأكد من أن Apache يعمل على المنفذ 80
- تحقق من عنوان URL: `http://localhost/Trust Plus/`

## الخطوات التالية

1. **إضافة المستخدمين:** قم بإنشاء حسابات للموظفين
2. **إعداد العملات:** أضف العملات التي تتعامل بها الشركة
3. **إعداد دليل الحسابات:** قم بتخصيص الحسابات المحاسبية
4. **إضافة العملاء:** ابدأ بتسجيل بيانات العملاء

## الدعم

للحصول على المساعدة:
- راجع ملف `README.md` للتفاصيل الكاملة
- تحقق من سجلات النظام في قاعدة البيانات
- تواصل مع فريق التطوير
