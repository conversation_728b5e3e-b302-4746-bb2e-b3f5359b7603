<?php
/**
 * Trust Plus - Error Fix Script
 * سكريبت إصلاح الأخطاء الشائعة
 */

echo "🔧 بدء إصلاح أخطاء Trust Plus...\n\n";

$fixes_applied = 0;
$errors_found = 0;

// 1. إصلاح مسارات الملفات في assets.php
echo "1. فحص وإصلاح ملف assets.php...\n";

if (file_exists('includes/assets.php')) {
    $content = file_get_contents('includes/assets.php');
    
    // إصلاح مشكلة self::ASSETS_PATH في الثوابت
    if (strpos($content, 'self::ASSETS_PATH') !== false) {
        $content = str_replace(
            "const CSS_PATH = self::ASSETS_PATH . 'css/';",
            "const CSS_PATH = '../assets/css/';",
            $content
        );
        $content = str_replace(
            "const JS_PATH = self::ASSETS_PATH . 'js/';",
            "const JS_PATH = '../assets/js/';",
            $content
        );
        
        file_put_contents('includes/assets.php', $content);
        echo "   ✅ تم إصلاح مسارات الثوابت\n";
        $fixes_applied++;
    } else {
        echo "   ✅ مسارات الثوابت صحيحة\n";
    }
} else {
    echo "   ❌ ملف assets.php غير موجود\n";
    $errors_found++;
}

// 2. إصلاح مسارات include في header.php
echo "\n2. فحص وإصلاح ملف header.php...\n";

if (file_exists('includes/header.php')) {
    $content = file_get_contents('includes/header.php');
    $original_content = $content;
    
    // إصلاح مسارات include
    $content = str_replace("require_once 'assets.php';", "require_once __DIR__ . '/assets.php';", $content);
    $content = str_replace("include 'sidebar.php';", "include __DIR__ . '/sidebar.php';", $content);
    $content = str_replace("include 'navbar.php';", "include __DIR__ . '/navbar.php';", $content);
    $content = str_replace("include 'breadcrumb.php';", "include __DIR__ . '/breadcrumb.php';", $content);
    
    if ($content !== $original_content) {
        file_put_contents('includes/header.php', $content);
        echo "   ✅ تم إصلاح مسارات include\n";
        $fixes_applied++;
    } else {
        echo "   ✅ مسارات include صحيحة\n";
    }
} else {
    echo "   ❌ ملف header.php غير موجود\n";
    $errors_found++;
}

// 3. إنشاء ملفات CSS و JS الأساسية إذا كانت مفقودة
echo "\n3. فحص وإنشاء ملفات الأصول...\n";

$asset_files = [
    'assets/css/bootstrap.min.css' => '/* Bootstrap CSS Placeholder */',
    'assets/css/fontawesome.min.css' => '/* FontAwesome CSS Placeholder */',
    'assets/js/bootstrap.bundle.min.js' => '/* Bootstrap JS Placeholder */',
    'assets/js/chart.min.js' => '/* Chart.js Placeholder */'
];

foreach ($asset_files as $file => $content) {
    if (!file_exists($file)) {
        $dir = dirname($file);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        file_put_contents($file, $content);
        echo "   ✅ تم إنشاء $file\n";
        $fixes_applied++;
    } else {
        echo "   ✅ $file موجود\n";
    }
}

// 4. إصلاح أذونات الملفات
echo "\n4. فحص وإصلاح أذونات الملفات...\n";

$directories = ['assets', 'assets/css', 'assets/js', 'includes'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $current_perms = substr(sprintf('%o', fileperms($dir)), -4);
        if ($current_perms < '0755') {
            chmod($dir, 0755);
            echo "   ✅ تم إصلاح أذونات $dir\n";
            $fixes_applied++;
        } else {
            echo "   ✅ أذونات $dir صحيحة ($current_perms)\n";
        }
    }
}

// 5. إنشاء ملف تكوين بسيط
echo "\n5. إنشاء ملف التكوين...\n";

if (!file_exists('config.php')) {
    $config_content = '<?php
/**
 * Trust Plus - Configuration File
 * ملف التكوين الأساسي
 */

// إعدادات النظام الأساسية
define("SYSTEM_NAME", "Trust Plus");
define("SYSTEM_VERSION", "2.0.0");
define("SYSTEM_LANGUAGE", "ar");
define("BASE_URL", "/");

// إعدادات العملة والتاريخ
define("DEFAULT_CURRENCY", "USD");
define("DATE_FORMAT", "Y-m-d");
define("TIME_FORMAT", "H:i:s");
define("TIMEZONE", "Asia/Riyadh");

// إعدادات قاعدة البيانات (يجب تحديثها)
define("DB_HOST", "localhost");
define("DB_NAME", "trust_plus");
define("DB_USER", "root");
define("DB_PASS", "");

// إعدادات الأمان
define("SESSION_TIMEOUT", 3600); // ساعة واحدة
define("MAX_LOGIN_ATTEMPTS", 5);

// تفعيل عرض الأخطاء في بيئة التطوير
if (!defined("PRODUCTION")) {
    define("PRODUCTION", false);
}

if (!PRODUCTION) {
    error_reporting(E_ALL);
    ini_set("display_errors", 1);
    ini_set("display_startup_errors", 1);
}

// تعيين المنطقة الزمنية
date_default_timezone_set(TIMEZONE);
?>';
    
    file_put_contents('config.php', $config_content);
    echo "   ✅ تم إنشاء ملف config.php\n";
    $fixes_applied++;
} else {
    echo "   ✅ ملف config.php موجود\n";
}

// 6. إنشاء ملف .htaccess للحماية
echo "\n6. إنشاء ملف .htaccess...\n";

if (!file_exists('.htaccess')) {
    $htaccess_content = '# Trust Plus - Apache Configuration

# منع الوصول للملفات الحساسة
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# تفعيل ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تفعيل التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/x-icon "access plus 1 year"
    ExpiresByType application/x-icon "access plus 1 year"
</IfModule>

# إعادة توجيه HTTPS (اختياري)
# RewriteEngine On
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
';
    
    file_put_contents('.htaccess', $htaccess_content);
    echo "   ✅ تم إنشاء ملف .htaccess\n";
    $fixes_applied++;
} else {
    echo "   ✅ ملف .htaccess موجود\n";
}

// 7. إنشاء صفحة اختبار بسيطة
echo "\n7. إنشاء صفحة اختبار...\n";

$test_page_content = '<?php
// تضمين ملف التكوين
if (file_exists("config.php")) {
    require_once "config.php";
}

// إعدادات الصفحة
$page_type = "default";
$page_title = "اختبار النظام - " . (defined("SYSTEM_NAME") ? SYSTEM_NAME : "Trust Plus");
$page_header = "اختبار النظام";
$page_subtitle = "التحقق من عمل النظام الجديد";
$page_icon = "fas fa-check-circle";
$show_breadcrumb = false;

// تضمين الرأس
include "includes/header.php";
?>

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        اختبار النظام ناجح!
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h4 class="alert-heading">مبروك! 🎉</h4>
                        <p>تم تحديث نظام Trust Plus بنجاح إلى الإصدار الجديد.</p>
                        <hr>
                        <p class="mb-0">جميع الملفات والمكونات تعمل بشكل صحيح.</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>معلومات النظام:</h6>
                            <ul class="list-unstyled">
                                <li><strong>اسم النظام:</strong> <?php echo defined("SYSTEM_NAME") ? SYSTEM_NAME : "Trust Plus"; ?></li>
                                <li><strong>الإصدار:</strong> <?php echo defined("SYSTEM_VERSION") ? SYSTEM_VERSION : "2.0.0"; ?></li>
                                <li><strong>إصدار PHP:</strong> <?php echo phpversion(); ?></li>
                                <li><strong>التاريخ:</strong> <?php echo date("Y-m-d H:i:s"); ?></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>الروابط المفيدة:</h6>
                            <div class="d-grid gap-2">
                                <a href="dashboard/index.php" class="btn btn-primary">
                                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                </a>
                                <a href="auth/login.php" class="btn btn-secondary">
                                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                </a>
                                <a href="debug.php" class="btn btn-info">
                                    <i class="fas fa-bug me-2"></i>تشخيص النظام
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include "includes/footer.php"; ?>
';

file_put_contents('test.php', $test_page_content);
echo "   ✅ تم إنشاء صفحة الاختبار: test.php\n";
$fixes_applied++;

// تقرير نهائي
echo "\n" . str_repeat("=", 50) . "\n";
echo "📋 تقرير الإصلاح:\n";
echo str_repeat("=", 50) . "\n";
echo "✅ الإصلاحات المطبقة: $fixes_applied\n";
echo "❌ الأخطاء المتبقية: $errors_found\n";

if ($errors_found === 0) {
    echo "\n🎉 تم إصلاح جميع المشاكل بنجاح!\n";
    echo "\n📝 الخطوات التالية:\n";
    echo "1. افتح test.php في المتصفح للتحقق من عمل النظام\n";
    echo "2. قم بتحديث إعدادات قاعدة البيانات في config.php\n";
    echo "3. اختبر جميع الصفحات للتأكد من عملها\n";
} else {
    echo "\n⚠️ لا تزال هناك بعض المشاكل التي تحتاج إصلاح يدوي.\n";
    echo "يرجى مراجعة الأخطاء أعلاه وإصلاحها.\n";
}

echo "\n🔗 روابط مفيدة:\n";
echo "- صفحة الاختبار: test.php\n";
echo "- تشخيص النظام: debug.php\n";
echo "- دليل البدء السريع: QUICK_START_GUIDE.md\n";

echo "\n✨ انتهى الإصلاح!\n";
?>
