# دليل وحدة عمليات الصرافة - Trust Plus

## نظرة عامة

وحدة عمليات الصرافة في Trust Plus هي نظام متكامل لإدارة عمليات صرف العملات مع حساب الأرباح التلقائي والتحكم في أسعار الصرف والعمولات.

## الميزات الرئيسية

### 1. إدارة عمليات الصرافة

#### إنشاء عملية صرافة جديدة
- **البحث عن العملاء**: بحث سريع بالاسم أو رقم الهوية
- **اختيار العملات**: من وإلى مع عرض الرموز والأسماء
- **حساب تلقائي**: حساب المبالغ والعمولات تلقائياً
- **التحقق من الصلاحيات**: فحص حالة KYC والقائمة السوداء
- **إنشاء الإيصال**: إيصال مفصل قابل للطباعة

#### خصائص متقدمة
- **تبديل العملات**: تبديل سريع بين عملة البيع والشراء
- **حساب الفوارق**: عرض فوري للهوامش والعمولات
- **التحقق من الحدود**: فحص الحدود اليومية والمبالغ المسموحة
- **ربط بـ KYC**: التحقق من حالة العميل قبل المعاملة

### 2. إدارة أسعار الصرف

#### تحديث الأسعار
- **أسعار الشراء والبيع**: إدارة منفصلة لكل سعر
- **حساب الفوارق**: عرض تلقائي للهوامش والنسب المئوية
- **إجراءات سريعة**: تطبيق هوامش موحدة أو تعديل جماعي
- **تتبع التغييرات**: سجل كامل لتحديثات الأسعار

#### أدوات متقدمة
- **هامش موحد**: تطبيق نسبة هامش على جميع الأزواج
- **زيادة/تقليل جماعي**: تعديل جميع الأسعار بنسبة محددة
- **تبديل الأسعار**: تبديل أسعار الشراء والبيع
- **نسخ الأسعار**: نسخ سريع للأسعار

### 3. حساب الأرباح المتقدم

#### مكونات الربح
- **عمولة المعاملة**: نسبة مئوية من مبلغ المعاملة
- **ربح الفارق (Spread)**: الفرق بين سعر الشراء والبيع
- **إجمالي الربح**: مجموع العمولة وربح الفارق

#### تتبع الأرباح
- **أرباح يومية**: تجميع تلقائي للأرباح حسب اليوم
- **أرباح حسب زوج العملة**: تفصيل الأرباح لكل زوج
- **أرباح حسب الفرع**: تقسيم الأرباح على الفروع
- **إحصائيات متقدمة**: متوسط الربح لكل معاملة

### 4. التقارير والإحصائيات

#### تقرير الأرباح
- **فلترة متقدمة**: حسب التاريخ والفرع وزوج العملة
- **رسوم بيانية**: تطور الأرباح اليومية
- **إحصائيات شاملة**: إجمالي المعاملات والأحجام والأرباح
- **تصدير البيانات**: إمكانية التصدير والطباعة

#### إحصائيات فورية
- **عمليات اليوم**: عدد المعاملات وحجم التداول
- **أرباح اليوم**: إجمالي الأرباح والعمولات
- **أداء الأزواج**: ترتيب أزواج العملات حسب الربحية

## الهيكل التقني

### قاعدة البيانات

#### الجداول الرئيسية
```sql
-- عمليات صرف العملات
currency_exchange_transactions (
    id, transaction_id, customer_id,
    buy_currency_id, buy_amount,
    sell_currency_id, sell_amount,
    exchange_rate_used, commission_amount
)

-- أسعار الصرف
exchange_rates (
    id, from_currency_id, to_currency_id,
    buy_rate, sell_rate, effective_date,
    user_id, is_active
)

-- أرباح الصرافة اليومية
daily_exchange_profits (
    id, profit_date, currency_pair,
    total_transactions, total_volume,
    total_commission, total_spread_profit,
    total_profit, branch_id
)

-- إعدادات الصرافة
exchange_settings (
    id, currency_pair, default_spread_percentage,
    commission_percentage, daily_limit,
    min_transaction_amount, max_transaction_amount
)
```

### الفئات البرمجية

#### ExchangeManager
```php
class ExchangeManager {
    // إنشاء عملية صرافة جديدة
    public function createExchangeTransaction($data, $user_id)
    
    // حساب مبالغ الصرافة والأرباح
    public function calculateExchangeAmounts($data, $rates)
    
    // الحصول على أسعار الصرف الحالية
    public function getCurrentExchangeRates($from_currency_id, $to_currency_id)
    
    // تحديث أسعار الصرف
    public function updateExchangeRates($rates_data, $user_id)
    
    // الحصول على إحصائيات الصرافة
    public function getExchangeStatistics($date_from, $date_to, $branch_id)
}
```

### الملفات الرئيسية

#### واجهات المستخدم
- **dashboard/exchange.php** - الصفحة الرئيسية للصرافة
- **dashboard/exchange_rates.php** - إدارة أسعار الصرف
- **dashboard/exchange_receipt.php** - إيصال المعاملة
- **dashboard/exchange_profits.php** - تقرير الأرباح

#### معالجة البيانات
- **dashboard/process_exchange.php** - معالجة عمليات الصرافة
- **dashboard/get_exchange_rate.php** - API أسعار الصرف
- **dashboard/search_customers.php** - البحث عن العملاء

#### الفئات المساعدة
- **includes/exchange_manager.php** - فئة إدارة الصرافة

## الصلاحيات والأدوار

### صلاحيات الصرافة
- **exchange.view** - عرض عمليات الصرافة
- **exchange.create** - إنشاء عمليات صرافة جديدة
- **exchange.rates** - إدارة أسعار الصرف
- **exchange.history** - عرض تاريخ العمليات
- **exchange.profits** - عرض تقارير الأرباح

### توزيع الصلاحيات
- **مدير النظام**: جميع الصلاحيات
- **مدير الفرع**: جميع الصلاحيات عدا إدارة الأسعار
- **المحاسب**: عرض التقارير والأرباح
- **أمين الصندوق**: إنشاء عمليات الصرافة
- **الموظف التشغيلي**: عرض العمليات فقط

## إعدادات النظام

### الثوابت المهمة
```php
define('BASE_CURRENCY_ID', 1); // USD
define('DEFAULT_SPREAD_PERCENTAGE', 0.0050); // 0.5%
define('DEFAULT_COMMISSION_RATE', 0.0025); // 0.25%
define('MIN_EXCHANGE_AMOUNT', 10);
define('MAX_EXCHANGE_AMOUNT', 50000);
define('DAILY_EXCHANGE_LIMIT', 100000);
```

### إعدادات قابلة للتخصيص
- **هامش الربح الافتراضي**: 0.5%
- **نسبة العمولة**: 0.25%
- **الحد الأدنى للمعاملة**: $10
- **الحد الأقصى للمعاملة**: $50,000
- **الحد اليومي**: $100,000

## خوارزميات الحساب

### حساب مبلغ الصرافة
```
إذا كان المبلغ المباع محدد:
    المبلغ المستلم = المبلغ المباع × سعر الصرف

إذا كان المبلغ المستلم محدد:
    المبلغ المباع = المبلغ المستلم ÷ سعر الصرف
```

### حساب العمولة
```
العمولة = max(
    المبلغ المباع × نسبة العمولة,
    الحد الأدنى للعمولة
)

إذا كان هناك حد أقصى:
    العمولة = min(العمولة, الحد الأقصى للعمولة)
```

### حساب ربح الفارق
```
السعر السوقي = (سعر الشراء + سعر البيع) ÷ 2
ربح الفارق = |السعر المستخدم - السعر السوقي| × المبلغ المباع
```

### إجمالي الربح
```
إجمالي الربح = العمولة + ربح الفارق
```

## الأمان والتحقق

### فحوصات الأمان
- **التحقق من حالة العميل**: KYC والقائمة السوداء
- **فحص الحدود**: المبالغ اليومية والحدود المسموحة
- **التحقق من الصلاحيات**: قبل كل عملية
- **تسجيل العمليات**: سجل كامل لجميع المعاملات

### حماية البيانات
- **تشفير المعاملات**: حماية بيانات المعاملات الحساسة
- **سجلات التدقيق**: تتبع جميع التغييرات والعمليات
- **نسخ احتياطية**: حفظ تلقائي للبيانات المهمة

## استكشاف الأخطاء

### مشاكل شائعة
1. **أسعار الصرف غير متوفرة**: تحقق من تحديث الأسعار اليومية
2. **العميل غير مؤهل**: راجع حالة KYC والقائمة السوداء
3. **تجاوز الحدود**: تحقق من الحدود اليومية والمبالغ المسموحة
4. **خطأ في الحساب**: راجع إعدادات العمولات والهوامش

### حلول سريعة
- **إعادة تحديث الأسعار**: من صفحة إدارة أسعار الصرف
- **مراجعة حالة العميل**: من صفحة تفاصيل العميل
- **تحديث الإعدادات**: من صفحة إعدادات النظام

## التطوير المستقبلي

### ميزات مخططة
- **ربط بمصادر أسعار خارجية**: تحديث تلقائي للأسعار
- **تحليلات متقدمة**: ذكاء اصطناعي لتحليل الأرباح
- **تطبيق موبايل**: واجهة محمولة للعمليات السريعة
- **تكامل مع البنوك**: ربط مباشر مع الحسابات البنكية

---

**ملاحظة**: هذا النظام مصمم للعمل كتطبيق مكتبي مستقل مع إمكانيات توسع مستقبلية.
