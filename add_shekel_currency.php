<?php
/**
 * Trust Plus - Add Israeli Shekel Currency
 * إضافة عملة الشيكل الإسرائيلي
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>💰 إضافة عملة الشيكل الإسرائيلي</h1>";
echo "<hr>";

try {
    // تحميل الملفات المطلوبة
    require_once 'config.php';
    require_once 'includes/database.php';
    require_once 'includes/auth.php';
    
    // بدء الجلسة
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<h2>📋 فحص العملات الموجودة</h2>";
    
    // فحص العملات الموجودة
    $stmt = $db->prepare("SELECT * FROM currencies ORDER BY is_base_currency DESC, name ASC");
    $stmt->execute();
    $existing_currencies = $stmt->fetchAll();
    
    echo "<p><strong>العملات الموجودة حالياً:</strong></p>";
    echo "<ul>";
    foreach ($existing_currencies as $currency) {
        echo "<li>" . htmlspecialchars($currency['name']) . " (" . htmlspecialchars($currency['code']) . ") - " . htmlspecialchars($currency['symbol']);
        if ($currency['is_base_currency']) {
            echo " <span class='badge bg-primary'>عملة أساسية</span>";
        }
        echo "</li>";
    }
    echo "</ul>";
    
    // فحص وجود الشيكل
    $stmt = $db->prepare("SELECT * FROM currencies WHERE code = 'ILS'");
    $stmt->execute();
    $shekel_exists = $stmt->fetch();
    
    if ($shekel_exists) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
        echo "<h3>⚠️ الشيكل موجود بالفعل</h3>";
        echo "<p><strong>اسم العملة:</strong> " . htmlspecialchars($shekel_exists['name']) . "</p>";
        echo "<p><strong>الرمز:</strong> " . htmlspecialchars($shekel_exists['code']) . "</p>";
        echo "<p><strong>الرمز المالي:</strong> " . htmlspecialchars($shekel_exists['symbol']) . "</p>";
        echo "<p><strong>الحالة:</strong> " . ($shekel_exists['is_active'] ? 'نشط' : 'غير نشط') . "</p>";
        
        if (!$shekel_exists['is_active']) {
            echo "<h4>🔧 تفعيل الشيكل...</h4>";
            $stmt = $db->prepare("UPDATE currencies SET is_active = 1 WHERE code = 'ILS'");
            if ($stmt->execute()) {
                echo "<p>✅ تم تفعيل عملة الشيكل</p>";
            } else {
                echo "<p>❌ فشل في تفعيل عملة الشيكل</p>";
            }
        }
        echo "</div>";
    } else {
        echo "<h2>➕ إضافة عملة الشيكل الإسرائيلي</h2>";
        
        // إضافة عملة الشيكل
        $stmt = $db->prepare("
            INSERT INTO currencies (name, code, symbol, is_base_currency, is_active, decimal_places) 
            VALUES (:name, :code, :symbol, :is_base_currency, :is_active, :decimal_places)
        ");
        
        $name = 'الشيكل الإسرائيلي';
        $code = 'ILS';
        $symbol = '₪';
        $is_base_currency = 0;
        $is_active = 1;
        $decimal_places = 2;
        
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':code', $code);
        $stmt->bindParam(':symbol', $symbol);
        $stmt->bindParam(':is_base_currency', $is_base_currency);
        $stmt->bindParam(':is_active', $is_active);
        $stmt->bindParam(':decimal_places', $decimal_places);
        
        if ($stmt->execute()) {
            $shekel_id = $db->lastInsertId();
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
            echo "<h3>✅ تم إضافة عملة الشيكل بنجاح!</h3>";
            echo "<p><strong>معرف العملة:</strong> $shekel_id</p>";
            echo "<p><strong>اسم العملة:</strong> $name</p>";
            echo "<p><strong>الرمز:</strong> $code</p>";
            echo "<p><strong>الرمز المالي:</strong> $symbol</p>";
            echo "</div>";
            
        } else {
            throw new Exception('فشل في إضافة عملة الشيكل');
        }
    }
    
    echo "<hr>";
    echo "<h2>💱 إضافة أسعار صرف الشيكل</h2>";
    
    // الحصول على معرف الشيكل
    $stmt = $db->prepare("SELECT id FROM currencies WHERE code = 'ILS'");
    $stmt->execute();
    $shekel_currency = $stmt->fetch();
    $shekel_id = $shekel_currency['id'];
    
    // الحصول على معرف الدولار (العملة الأساسية)
    $stmt = $db->prepare("SELECT id FROM currencies WHERE code = 'USD'");
    $stmt->execute();
    $usd_currency = $stmt->fetch();
    $usd_id = $usd_currency['id'];
    
    if ($usd_id && $shekel_id) {
        // فحص وجود أسعار صرف
        $stmt = $db->prepare("
            SELECT * FROM exchange_rates 
            WHERE (from_currency_id = :usd_id AND to_currency_id = :shekel_id)
               OR (from_currency_id = :shekel_id AND to_currency_id = :usd_id)
        ");
        $stmt->bindParam(':usd_id', $usd_id);
        $stmt->bindParam(':shekel_id', $shekel_id);
        $stmt->execute();
        $existing_rates = $stmt->fetchAll();
        
        if (empty($existing_rates)) {
            echo "<p>إضافة أسعار صرف الشيكل مقابل الدولار...</p>";
            
            // الحصول على معرف المستخدم الافتراضي
            $stmt = $db->prepare("SELECT id FROM users WHERE username = 'admin' LIMIT 1");
            $stmt->execute();
            $admin_user = $stmt->fetch();
            $user_id = $admin_user['id'] ?? 1;
            
            // أسعار الصرف الحالية (تقريبية)
            $usd_to_ils_buy = 3.65;  // شراء الشيكل بالدولار
            $usd_to_ils_sell = 3.70; // بيع الشيكل بالدولار
            
            $ils_to_usd_buy = 0.270;  // شراء الدولار بالشيكل
            $ils_to_usd_sell = 0.275; // بيع الدولار بالشيكل
            
            // إضافة سعر USD إلى ILS
            $stmt = $db->prepare("
                INSERT INTO exchange_rates (from_currency_id, to_currency_id, buy_rate, sell_rate, effective_date, user_id, is_active)
                VALUES (:from_currency_id, :to_currency_id, :buy_rate, :sell_rate, CURDATE(), :user_id, 1)
            ");
            
            $stmt->bindParam(':from_currency_id', $usd_id);
            $stmt->bindParam(':to_currency_id', $shekel_id);
            $stmt->bindParam(':buy_rate', $usd_to_ils_buy);
            $stmt->bindParam(':sell_rate', $usd_to_ils_sell);
            $stmt->bindParam(':user_id', $user_id);
            
            if ($stmt->execute()) {
                echo "<p>✅ تم إضافة سعر صرف USD → ILS (شراء: $usd_to_ils_buy، بيع: $usd_to_ils_sell)</p>";
            }
            
            // إضافة سعر ILS إلى USD
            $stmt->bindParam(':from_currency_id', $shekel_id);
            $stmt->bindParam(':to_currency_id', $usd_id);
            $stmt->bindParam(':buy_rate', $ils_to_usd_buy);
            $stmt->bindParam(':sell_rate', $ils_to_usd_sell);
            $stmt->bindParam(':user_id', $user_id);
            
            if ($stmt->execute()) {
                echo "<p>✅ تم إضافة سعر صرف ILS → USD (شراء: $ils_to_usd_buy، بيع: $ils_to_usd_sell)</p>";
            }
            
        } else {
            echo "<p>⚠️ أسعار صرف الشيكل موجودة بالفعل:</p>";
            echo "<ul>";
            foreach ($existing_rates as $rate) {
                $from_code = ($rate['from_currency_id'] == $usd_id) ? 'USD' : 'ILS';
                $to_code = ($rate['to_currency_id'] == $usd_id) ? 'USD' : 'ILS';
                echo "<li>$from_code → $to_code: شراء {$rate['buy_rate']}، بيع {$rate['sell_rate']}</li>";
            }
            echo "</ul>";
        }
    }
    
    echo "<hr>";
    echo "<h2>📊 العملات النهائية</h2>";
    
    // عرض جميع العملات بعد الإضافة
    $stmt = $db->prepare("SELECT * FROM currencies WHERE is_active = 1 ORDER BY is_base_currency DESC, name ASC");
    $stmt->execute();
    $final_currencies = $stmt->fetchAll();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>💰 العملات المتاحة في النظام:</h3>";
    echo "<div class='row'>";
    foreach ($final_currencies as $currency) {
        echo "<div class='col-md-4 mb-2'>";
        echo "<div class='card'>";
        echo "<div class='card-body text-center'>";
        echo "<h5>" . htmlspecialchars($currency['symbol']) . "</h5>";
        echo "<h6>" . htmlspecialchars($currency['name']) . "</h6>";
        echo "<p class='text-muted'>" . htmlspecialchars($currency['code']) . "</p>";
        if ($currency['is_base_currency']) {
            echo "<span class='badge bg-primary'>عملة أساسية</span>";
        }
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #28a745;'>";
    echo "<h2>🎉 تم بنجاح!</h2>";
    echo "<h3>✅ ما تم إنجازه:</h3>";
    echo "<ul>";
    echo "<li>✅ إضافة عملة الشيكل الإسرائيلي (ILS) ₪</li>";
    echo "<li>✅ إضافة أسعار صرف الشيكل مقابل الدولار</li>";
    echo "<li>✅ تفعيل العملة في النظام</li>";
    echo "<li>✅ العملة متاحة الآن في جميع عمليات الصرافة</li>";
    echo "</ul>";
    
    echo "<h3>🔗 اختبر النظام الآن:</h3>";
    echo "<p><a href='dashboard/exchange.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>💱 عمليات الصرافة</a></p>";
    echo "<p><a href='dashboard/settings.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>⚙️ إدارة العملات</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في العملية</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>📝 ملاحظات مهمة:</h3>";
echo "<ul>";
echo "<li>أسعار الصرف المضافة هي أسعار تجريبية ويجب تحديثها حسب السوق</li>";
echo "<li>يمكن تعديل أسعار الصرف من صفحة إدارة أسعار الصرف</li>";
echo "<li>العملة متاحة الآن في جميع عمليات الصرافة والتحويلات</li>";
echo "<li>يمكن إضافة المزيد من العملات من صفحة الإعدادات</li>";
echo "</ul>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

.badge {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background: white;
    margin-bottom: 1rem;
}

.card-body {
    padding: 1rem;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: -0.5rem;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    padding: 0.5rem;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    line-height: 1.6;
}
</style>
