<?php
/**
 * Trust Plus - Debug Login Process
 * تشخيص عملية تسجيل الدخول
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 تشخيص عملية تسجيل الدخول</h1>";
echo "<hr>";

try {
    // تحميل الملفات
    require_once 'config.php';
    require_once 'includes/database.php';
    require_once 'includes/auth.php';
    
    echo "<h2>📋 معلومات الجلسة الحالية</h2>";
    echo "<p><strong>حالة الجلسة:</strong> " . session_status() . "</p>";
    echo "<p><strong>معرف الجلسة:</strong> " . (session_id() ?: 'غير موجود') . "</p>";
    
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
        echo "<p>✅ تم بدء جلسة جديدة</p>";
    } else {
        echo "<p>ℹ️ الجلسة نشطة بالفعل</p>";
    }
    
    echo "<p><strong>محتويات $_SESSION:</strong></p>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    
    echo "<hr>";
    
    // إنشاء كائن Auth
    $auth = new Auth();
    echo "<h2>🔐 اختبار نظام المصادقة</h2>";
    
    // اختبار checkSession
    $session_valid = $auth->checkSession();
    echo "<p><strong>حالة الجلسة:</strong> " . ($session_valid ? '✅ صالحة' : '❌ غير صالحة') . "</p>";
    
    if ($session_valid) {
        $current_user = $auth->getCurrentUser();
        if ($current_user) {
            echo "<p><strong>المستخدم الحالي:</strong> " . htmlspecialchars($current_user['full_name']) . "</p>";
            echo "<p><strong>معرف المستخدم:</strong> " . $current_user['id'] . "</p>";
            echo "<p><strong>الدور:</strong> " . $current_user['role_id'] . "</p>";
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h3>✅ المستخدم مسجل دخول بالفعل</h3>";
            echo "<p><a href='dashboard/index.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 اذهب للوحة التحكم</a></p>";
            echo "</div>";
        } else {
            echo "<p>❌ فشل في الحصول على معلومات المستخدم</p>";
        }
    } else {
        echo "<h3>🔑 اختبار تسجيل الدخول</h3>";
        
        // اختبار تسجيل الدخول
        $login_result = $auth->login('admin', 'admin123');
        
        if ($login_result['success']) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h3>🎉 نجح تسجيل الدخول!</h3>";
            echo "<p><strong>الرسالة:</strong> " . htmlspecialchars($login_result['message']) . "</p>";
            
            // فحص الجلسة بعد تسجيل الدخول
            echo "<h4>📋 معلومات الجلسة بعد تسجيل الدخول:</h4>";
            echo "<pre>" . print_r($_SESSION, true) . "</pre>";
            
            // اختبار checkSession مرة أخرى
            $session_valid_after = $auth->checkSession();
            echo "<p><strong>حالة الجلسة بعد تسجيل الدخول:</strong> " . ($session_valid_after ? '✅ صالحة' : '❌ غير صالحة') . "</p>";
            
            if ($session_valid_after) {
                $current_user_after = $auth->getCurrentUser();
                if ($current_user_after) {
                    echo "<p><strong>المستخدم:</strong> " . htmlspecialchars($current_user_after['full_name']) . "</p>";
                    
                    echo "<h4>🧪 اختبار إعادة التوجيه...</h4>";
                    echo "<p>سيتم إعادة توجيهك للوحة التحكم خلال 3 ثوان...</p>";
                    echo "<script>";
                    echo "setTimeout(function() {";
                    echo "  window.location.href = 'dashboard/index.php';";
                    echo "}, 3000);";
                    echo "</script>";
                    
                    echo "<p><a href='dashboard/index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 اذهب للوحة التحكم الآن</a></p>";
                } else {
                    echo "<p>❌ فشل في الحصول على معلومات المستخدم بعد تسجيل الدخول</p>";
                }
            } else {
                echo "<p>❌ الجلسة غير صالحة بعد تسجيل الدخول!</p>";
            }
            
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h3>❌ فشل تسجيل الدخول</h3>";
            echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($login_result['message']) . "</p>";
            echo "</div>";
            
            // محاولة تشخيص المشكلة
            echo "<h3>🔧 تشخيص المشكلة...</h3>";
            
            $database = new Database();
            $db = $database->getConnection();
            
            if ($db) {
                echo "<p>✅ اتصال قاعدة البيانات يعمل</p>";
                
                // فحص المستخدم في قاعدة البيانات
                $stmt = $db->prepare("SELECT * FROM users WHERE username = 'admin'");
                $stmt->execute();
                $admin_user = $stmt->fetch();
                
                if ($admin_user) {
                    echo "<p>✅ المستخدم admin موجود في قاعدة البيانات</p>";
                    echo "<p><strong>حالة المستخدم:</strong> " . ($admin_user['is_active'] ? 'نشط' : 'غير نشط') . "</p>";
                    echo "<p><strong>محاولات فاشلة:</strong> " . $admin_user['failed_login_attempts'] . "</p>";
                    echo "<p><strong>مقفل حتى:</strong> " . ($admin_user['locked_until'] ?: 'غير مقفل') . "</p>";
                    
                    // اختبار كلمة المرور
                    $password_valid = password_verify('admin123', $admin_user['password_hash']);
                    echo "<p><strong>كلمة المرور:</strong> " . ($password_valid ? '✅ صحيحة' : '❌ خاطئة') . "</p>";
                    
                    if (!$password_valid) {
                        echo "<p>🔧 سأقوم بإعادة تعيين كلمة المرور...</p>";
                        $new_hash = password_hash('admin123', PASSWORD_DEFAULT);
                        $stmt = $db->prepare("UPDATE users SET password_hash = :hash WHERE username = 'admin'");
                        $stmt->bindParam(':hash', $new_hash);
                        if ($stmt->execute()) {
                            echo "<p>✅ تم إعادة تعيين كلمة المرور</p>";
                            echo "<p><a href='debug_login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 إعادة المحاولة</a></p>";
                        }
                    }
                } else {
                    echo "<p>❌ المستخدم admin غير موجود</p>";
                    echo "<p>🔧 سأقوم بإنشاء المستخدم...</p>";
                    
                    $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
                    $stmt = $db->prepare("
                        INSERT INTO users (username, email, password_hash, full_name, role_id, branch_id, is_active) 
                        VALUES ('admin', '<EMAIL>', :password_hash, 'مدير النظام', 1, 1, 1)
                    ");
                    $stmt->bindParam(':password_hash', $password_hash);
                    
                    if ($stmt->execute()) {
                        echo "<p>✅ تم إنشاء المستخدم admin</p>";
                        echo "<p><a href='debug_login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 إعادة المحاولة</a></p>";
                    }
                }
            } else {
                echo "<p>❌ فشل اتصال قاعدة البيانات</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في النظام</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='auth/login.php' target='_blank'>🔑 صفحة تسجيل الدخول</a></li>";
echo "<li><a href='dashboard/index.php' target='_blank'>📊 لوحة التحكم</a></li>";
echo "<li><a href='debug_login.php'>🔄 إعادة تشغيل التشخيص</a></li>";
echo "</ul>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

pre {
    background: #e9ecef;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
