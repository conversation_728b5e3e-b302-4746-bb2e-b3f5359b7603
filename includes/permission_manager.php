<?php
/**
 * فئة إدارة الصلاحيات - Trust Plus System
 */

require_once __DIR__ . '/../config.php';

class PermissionManager {
    private $db;
    
    public function __construct() {
        try {
            // الاتصال بقاعدة البيانات
            $this->db = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
            
            // إنشاء الجداول إذا لم تكن موجودة
            $this->createTables();
            
        } catch (PDOException $e) {
            throw new Exception('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب جميع الصلاحيات
     */
    public function getAllPermissions() {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM permissions 
                ORDER BY module, permission_name
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            // إنشاء صلاحيات افتراضية إذا لم توجد
            $this->createDefaultPermissions();
            return $this->getAllPermissions();
        }
    }
    
    /**
     * جلب صلاحية بواسطة المعرف
     */
    public function getPermissionById($permission_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM permissions 
                WHERE id = :permission_id
            ");
            $stmt->bindParam(':permission_id', $permission_id, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception('خطأ في جلب بيانات الصلاحية: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب جميع الوحدات
     */
    public function getModules() {
        try {
            $stmt = $this->db->prepare("
                SELECT DISTINCT module FROM permissions 
                ORDER BY module
            ");
            $stmt->execute();
            return array_column($stmt->fetchAll(), 'module');
        } catch (PDOException $e) {
            return ['users', 'roles', 'permissions', 'customers', 'exchange', 'transfers', 'transactions', 'accounting', 'reports', 'settings', 'audit', 'backup'];
        }
    }
    
    /**
     * إنشاء صلاحية جديدة
     */
    public function createPermission($data) {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validatePermissionData($data);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // تحديد الوحدة
            $module = $data['module'] === 'new' ? $data['new_module'] : $data['module'];
            
            // التحقق من عدم تكرار اسم الصلاحية
            if ($this->isPermissionNameExists($data['permission_name'])) {
                return ['success' => false, 'message' => 'اسم الصلاحية موجود مسبقاً'];
            }
            
            $this->db->beginTransaction();
            
            // إدراج الصلاحية
            $stmt = $this->db->prepare("
                INSERT INTO permissions (permission_name, description, module)
                VALUES (:permission_name, :description, :module)
            ");
            
            $stmt->bindParam(':permission_name', $data['permission_name']);
            $stmt->bindParam(':description', $data['description'] ?? '');
            $stmt->bindParam(':module', $module);
            
            $stmt->execute();
            $permission_id = $this->db->lastInsertId();
            
            // تسجيل العملية في سجل المراجعة
            $this->logActivity($_SESSION['user_id'] ?? 1, 'create_permission', 'permission', $permission_id, 'تم إنشاء صلاحية جديدة: ' . $data['permission_name']);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم إنشاء الصلاحية بنجاح', 'permission_id' => $permission_id];
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في إنشاء الصلاحية: ' . $e->getMessage()];
        }
    }
    
    /**
     * تحديث بيانات الصلاحية
     */
    public function updatePermission($permission_id, $data) {
        try {
            // التحقق من وجود الصلاحية
            $existing_permission = $this->getPermissionById($permission_id);
            if (!$existing_permission) {
                return ['success' => false, 'message' => 'الصلاحية غير موجودة'];
            }
            
            // التحقق من صحة البيانات
            $validation = $this->validatePermissionData($data, $permission_id);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // تحديد الوحدة
            $module = $data['module'] === 'new' ? $data['new_module'] : $data['module'];
            
            // التحقق من عدم تكرار اسم الصلاحية
            if ($this->isPermissionNameExists($data['permission_name'], $permission_id)) {
                return ['success' => false, 'message' => 'اسم الصلاحية موجود مسبقاً'];
            }
            
            $this->db->beginTransaction();
            
            // تحديث البيانات
            $stmt = $this->db->prepare("
                UPDATE permissions SET 
                    permission_name = :permission_name,
                    description = :description,
                    module = :module
                WHERE id = :permission_id
            ");
            
            $stmt->bindParam(':permission_name', $data['permission_name']);
            $stmt->bindParam(':description', $data['description'] ?? '');
            $stmt->bindParam(':module', $module);
            $stmt->bindParam(':permission_id', $permission_id, PDO::PARAM_INT);
            
            $stmt->execute();
            
            // تسجيل العملية في سجل المراجعة
            $this->logActivity($_SESSION['user_id'] ?? 1, 'update_permission', 'permission', $permission_id, 'تم تحديث الصلاحية: ' . $data['permission_name']);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم تحديث الصلاحية بنجاح'];
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في تحديث الصلاحية: ' . $e->getMessage()];
        }
    }
    
    /**
     * التحقق من صحة بيانات الصلاحية
     */
    private function validatePermissionData($data, $permission_id = null) {
        // التحقق من الحقول المطلوبة
        if (empty($data['permission_name'])) {
            return ['valid' => false, 'message' => 'اسم الصلاحية مطلوب'];
        }
        
        // التحقق من نمط اسم الصلاحية
        if (!preg_match('/^[a-zA-Z0-9_]+\.[a-zA-Z0-9_]+$/', $data['permission_name'])) {
            return ['valid' => false, 'message' => 'اسم الصلاحية يجب أن يكون بالنمط: module.action'];
        }
        
        // التحقق من الوحدة
        if (empty($data['module'])) {
            return ['valid' => false, 'message' => 'الوحدة مطلوبة'];
        }
        
        // التحقق من الوحدة الجديدة
        if ($data['module'] === 'new') {
            if (empty($data['new_module'])) {
                return ['valid' => false, 'message' => 'اسم الوحدة الجديدة مطلوب'];
            }
            
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['new_module'])) {
                return ['valid' => false, 'message' => 'اسم الوحدة يجب أن يحتوي على أحرف وأرقام فقط'];
            }
        }
        
        return ['valid' => true];
    }
    
    /**
     * التحقق من وجود اسم الصلاحية
     */
    private function isPermissionNameExists($permission_name, $exclude_permission_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM permissions WHERE permission_name = :permission_name";
            $params = [':permission_name' => $permission_name];
            
            if ($exclude_permission_id) {
                $sql .= " AND id != :permission_id";
                $params[':permission_id'] = $exclude_permission_id;
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * إنشاء الجداول المطلوبة
     */
    private function createTables() {
        try {
            // جدول الصلاحيات
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS permissions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    permission_name VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT,
                    module VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // إنشاء الصلاحيات الافتراضية
            $this->createDefaultPermissions();
            
        } catch (PDOException $e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * إنشاء الصلاحيات الافتراضية
     */
    private function createDefaultPermissions() {
        try {
            $permissions = [
                // إدارة المستخدمين
                ['users.view', 'عرض المستخدمين', 'users'],
                ['users.create', 'إضافة مستخدمين', 'users'],
                ['users.edit', 'تعديل المستخدمين', 'users'],
                ['users.delete', 'حذف المستخدمين', 'users'],

                // إدارة الأدوار
                ['roles.view', 'عرض الأدوار', 'roles'],
                ['roles.create', 'إضافة أدوار', 'roles'],
                ['roles.edit', 'تعديل الأدوار', 'roles'],
                ['roles.delete', 'حذف الأدوار', 'roles'],

                // إدارة الصلاحيات
                ['permissions.view', 'عرض الصلاحيات', 'permissions'],
                ['permissions.create', 'إضافة صلاحيات', 'permissions'],
                ['permissions.edit', 'تعديل الصلاحيات', 'permissions'],
                ['permissions.delete', 'حذف الصلاحيات', 'permissions'],

                // إدارة العملاء
                ['customers.view', 'عرض العملاء', 'customers'],
                ['customers.create', 'إضافة عملاء', 'customers'],
                ['customers.edit', 'تعديل العملاء', 'customers'],
                ['customers.delete', 'حذف العملاء', 'customers'],

                // عمليات الصرافة
                ['exchange.view', 'عرض عمليات الصرافة', 'exchange'],
                ['exchange.create', 'إنشاء عمليات صرافة', 'exchange'],
                ['exchange.rates', 'إدارة أسعار الصرف', 'exchange'],

                // التحويلات المالية
                ['transfers.view', 'عرض التحويلات', 'transfers'],
                ['transfers.create', 'إنشاء تحويلات', 'transfers'],
                ['transfers.approve', 'اعتماد التحويلات', 'transfers'],

                // المعاملات المالية
                ['transactions.view', 'عرض المعاملات', 'transactions'],
                ['transactions.edit', 'تعديل المعاملات', 'transactions'],

                // المحاسبة
                ['accounting.view', 'عرض المحاسبة', 'accounting'],
                ['accounting.edit', 'تعديل المحاسبة', 'accounting'],

                // الصناديق والبنوك
                ['cash.view', 'عرض الصناديق والبنوك', 'cash'],
                ['cash.edit', 'تعديل الصناديق والبنوك', 'cash'],

                // التقارير
                ['reports.financial', 'التقارير المالية', 'reports'],
                ['reports.compliance', 'تقارير الامتثال', 'reports'],
                ['reports.export', 'تصدير التقارير', 'reports'],

                // الإعدادات
                ['settings.view', 'عرض الإعدادات', 'settings'],
                ['settings.edit', 'تعديل الإعدادات', 'settings'],
                ['settings.currencies', 'إدارة العملات', 'settings'],
                ['settings.branches', 'إدارة الفروع', 'settings'],
                ['settings.partners', 'إدارة الشركاء', 'settings'],

                // سجل المراجعة
                ['audit.view', 'عرض سجل المراجعة', 'audit'],
                ['audit.export', 'تصدير سجل المراجعة', 'audit'],

                // الأمان
                ['security.view', 'عرض إعدادات الأمان', 'security'],
                ['security.edit', 'تعديل إعدادات الأمان', 'security'],

                // النسخ الاحتياطي
                ['backup.create', 'إنشاء نسخ احتياطية', 'backup'],
                ['backup.restore', 'استعادة النسخ الاحتياطية', 'backup'],
                ['backup.download', 'تحميل النسخ الاحتياطية', 'backup'],

                // الملف الشخصي
                ['profile.view', 'عرض الملف الشخصي', 'profile'],
                ['profile.edit', 'تعديل الملف الشخصي', 'profile'],

                // المساعدة
                ['help.view', 'عرض المساعدة', 'help'],
            ];

            $stmt = $this->db->prepare("
                INSERT IGNORE INTO permissions (permission_name, description, module)
                VALUES (?, ?, ?)
            ");

            foreach ($permissions as $permission) {
                $stmt->execute($permission);
            }

        } catch (PDOException $e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * تسجيل النشاط في سجل المراجعة
     */
    private function logActivity($user_id, $action, $table_name, $record_id, $description = '') {
        try {
            // إنشاء جدول سجل المراجعة إذا لم يكن موجوداً
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    action VARCHAR(50) NOT NULL,
                    table_name VARCHAR(50) NOT NULL,
                    record_id INT NOT NULL,
                    description TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            $stmt = $this->db->prepare("
                INSERT INTO audit_logs (user_id, action, table_name, record_id, description, ip_address, user_agent)
                VALUES (:user_id, :action, :table_name, :record_id, :description, :ip_address, :user_agent)
            ");

            $stmt->execute([
                ':user_id' => $user_id,
                ':action' => $action,
                ':table_name' => $table_name,
                ':record_id' => $record_id,
                ':description' => $description,
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (PDOException $e) {
            // تجاهل أخطاء تسجيل المراجعة
        }
    }
}
?>
