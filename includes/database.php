<?php
/**
 * Trust Plus - Database Connection Class
 * فئة الاتصال بقاعدة البيانات
 */

class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $conn;
    
    public function __construct() {
        // استخدام الثوابت من config.php إذا كانت متاحة
        $this->host = defined('DB_HOST') ? DB_HOST : 'localhost';
        $this->db_name = defined('DB_NAME') ? DB_NAME : 'trust_plus';
        $this->username = defined('DB_USER') ? DB_USER : 'root';
        $this->password = defined('DB_PASS') ? DB_PASS : '';
    }
    
    /**
     * إنشاء اتصال بقاعدة البيانات
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $e) {
            // في بيئة التطوير، عرض الخطأ
            if (!defined('PRODUCTION') || !PRODUCTION) {
                echo "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
            } else {
                // في بيئة الإنتاج، تسجيل الخطأ فقط
                error_log("Database connection error: " . $e->getMessage());
            }
            
            // إرجاع null في حالة فشل الاتصال
            return null;
        }
        
        return $this->conn;
    }
    
    /**
     * إغلاق الاتصال
     */
    public function closeConnection() {
        $this->conn = null;
    }
    
    /**
     * التحقق من حالة الاتصال
     */
    public function isConnected() {
        return $this->conn !== null;
    }
    
    /**
     * تنفيذ استعلام بسيط
     */
    public function query($sql, $params = []) {
        try {
            if (!$this->conn) {
                $this->getConnection();
            }
            
            if (!$this->conn) {
                return false;
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt;
            
        } catch(PDOException $e) {
            if (!defined('PRODUCTION') || !PRODUCTION) {
                echo "خطأ في تنفيذ الاستعلام: " . $e->getMessage();
            } else {
                error_log("Query error: " . $e->getMessage());
            }
            
            return false;
        }
    }
    
    /**
     * الحصول على آخر ID مُدرج
     */
    public function lastInsertId() {
        if ($this->conn) {
            return $this->conn->lastInsertId();
        }
        return false;
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        if ($this->conn) {
            return $this->conn->beginTransaction();
        }
        return false;
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        if ($this->conn) {
            return $this->conn->commit();
        }
        return false;
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        if ($this->conn) {
            return $this->conn->rollback();
        }
        return false;
    }
    
    /**
     * إنشاء جداول النظام الأساسية (للاختبار)
     */
    public function createBasicTables() {
        if (!$this->conn) {
            $this->getConnection();
        }
        
        if (!$this->conn) {
            return false;
        }
        
        try {
            // جدول المستخدمين
            $sql = "CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role_id INT DEFAULT 1,
                branch_id INT DEFAULT 1,
                is_active TINYINT(1) DEFAULT 1,
                failed_login_attempts INT DEFAULT 0,
                locked_until DATETIME NULL,
                last_login DATETIME NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // إدراج مستخدم افتراضي إذا لم يكن موجوداً
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result['count'] == 0) {
                $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $this->conn->prepare("
                    INSERT INTO users (username, email, password_hash, full_name, role_id) 
                    VALUES ('admin', '<EMAIL>', :password_hash, 'مدير النظام', 1)
                ");
                $stmt->bindParam(':password_hash', $password_hash);
                $stmt->execute();
            }
            
            // جدول الأدوار (بسيط)
            $sql = "CREATE TABLE IF NOT EXISTS roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                role_name VARCHAR(50) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // إدراج دور افتراضي
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM roles WHERE id = 1");
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result['count'] == 0) {
                $stmt = $this->conn->prepare("
                    INSERT INTO roles (id, role_name, description) 
                    VALUES (1, 'مدير النظام', 'صلاحيات كاملة للنظام')
                ");
                $stmt->execute();
            }
            
            // جدول الفروع (بسيط)
            $sql = "CREATE TABLE IF NOT EXISTS branches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                address TEXT,
                phone VARCHAR(20),
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            // إدراج فرع افتراضي
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM branches WHERE id = 1");
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result['count'] == 0) {
                $stmt = $this->conn->prepare("
                    INSERT INTO branches (id, name, address) 
                    VALUES (1, 'الفرع الرئيسي', 'العنوان الرئيسي')
                ");
                $stmt->execute();
            }
            
            // جدول سجل التدقيق (بسيط)
            $sql = "CREATE TABLE IF NOT EXISTS audit_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                action VARCHAR(50),
                entity_type VARCHAR(50),
                entity_id INT,
                old_value TEXT,
                new_value TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->conn->exec($sql);
            
            return true;
            
        } catch(PDOException $e) {
            if (!defined('PRODUCTION') || !PRODUCTION) {
                echo "خطأ في إنشاء الجداول: " . $e->getMessage();
            } else {
                error_log("Table creation error: " . $e->getMessage());
            }
            
            return false;
        }
    }
}
?>
