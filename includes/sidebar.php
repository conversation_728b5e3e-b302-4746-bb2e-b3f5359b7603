<?php
/**
 * Trust Plus - Sidebar Navigation
 * الشريط الجانبي للتنقل
 */

// التأكد من وجود متغير المصادقة
if (!isset($auth)) {
    require_once 'auth.php';
    $auth = new Auth();
}

$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>

<!-- الشريط الجانبي -->
<nav class="sidebar" id="sidebar">
    <!-- رأس الشريط الجانبي -->
    <div class="sidebar-header">
        <div class="p-3 text-center border-bottom border-light">
            <h5 class="text-white mb-0">
                <i class="fas fa-shield-alt me-2"></i>
                <span class="sidebar-text"><?php echo defined('SYSTEM_NAME') ? SYSTEM_NAME : 'Trust Plus'; ?></span>
            </h5>
        </div>
    </div>
    
    <!-- قائمة التنقل -->
    <ul class="nav flex-column">
        <!-- لوحة التحكم -->
        <li class="nav-item">
            <a class="nav-link <?php echo $current_page == 'index' ? 'active' : ''; ?>" href="index.php">
                <i class="fas fa-tachometer-alt"></i>
                <span class="sidebar-text">لوحة التحكم</span>
            </a>
        </li>
        
        <!-- إدارة العملاء -->
        <?php if ($auth->hasPermission('customers.view')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo $current_page == 'customers' ? 'active' : ''; ?>" href="customers.php">
                <i class="fas fa-users"></i>
                <span class="sidebar-text">إدارة العملاء</span>
            </a>
        </li>
        <?php endif; ?>
        
        <!-- عمليات الصرافة -->
        <?php if ($auth->hasPermission('exchange.view')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo $current_page == 'exchange' ? 'active' : ''; ?>" href="exchange.php">
                <i class="fas fa-exchange-alt"></i>
                <span class="sidebar-text">عمليات الصرافة</span>
            </a>
        </li>
        <?php endif; ?>

        <!-- أسعار الصرف -->
        <?php if ($auth->hasPermission('exchange.rates')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo $current_page == 'exchange_rates' ? 'active' : ''; ?>" href="exchange_rates.php">
                <i class="fas fa-chart-line"></i>
                <span class="sidebar-text">أسعار الصرف</span>
            </a>
        </li>
        <?php endif; ?>

        <!-- التحويلات المالية -->
        <?php if ($auth->hasPermission('transfers.view')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo $current_page == 'transfers' ? 'active' : ''; ?>" href="transfers.php">
                <i class="fas fa-paper-plane"></i>
                <span class="sidebar-text">التحويلات المالية</span>
            </a>
        </li>
        <?php endif; ?>
        
        <!-- المعاملات المالية -->
        <?php if ($auth->hasPermission('transactions.view')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo $current_page == 'transactions' ? 'active' : ''; ?>" href="transactions.php">
                <i class="fas fa-receipt"></i>
                <span class="sidebar-text">المعاملات المالية</span>
            </a>
        </li>
        <?php endif; ?>
        
        <!-- المحاسبة -->
        <?php if ($auth->hasPermission('accounting.view')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo $current_page == 'accounting' ? 'active' : ''; ?>" href="accounting.php">
                <i class="fas fa-calculator"></i>
                <span class="sidebar-text">المحاسبة</span>
            </a>
        </li>
        <?php endif; ?>
        
        <!-- الصناديق والبنوك -->
        <?php if ($auth->hasPermission('cash.view')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo $current_page == 'cash' ? 'active' : ''; ?>" href="cash.php">
                <i class="fas fa-cash-register"></i>
                <span class="sidebar-text">الصناديق والبنوك</span>
            </a>
        </li>
        <?php endif; ?>
        
        <!-- التقارير -->
        <?php if ($auth->hasPermission('reports.financial')): ?>
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle <?php echo in_array($current_page, ['financial_dashboard', 'reports', 'compliance_reports']) ? 'active' : ''; ?>" 
               href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-chart-bar"></i>
                <span class="sidebar-text">التقارير</span>
            </a>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item <?php echo $current_page == 'financial_dashboard' ? 'active' : ''; ?>" href="financial_dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة الأداء المالي
                </a></li>
                <li><a class="dropdown-item <?php echo $current_page == 'reports' ? 'active' : ''; ?>" href="reports.php">
                    <i class="fas fa-chart-line me-2"></i>التقارير المالية
                </a></li>
                <li><a class="dropdown-item <?php echo $current_page == 'compliance_reports' ? 'active' : ''; ?>" href="compliance_reports.php">
                    <i class="fas fa-shield-alt me-2"></i>تقارير الامتثال
                </a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- الإعدادات -->
        <?php if ($auth->hasPermission('settings.view')): ?>
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle <?php echo in_array($current_page, ['settings', 'currencies', 'branches', 'partners']) ? 'active' : ''; ?>" 
               href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-cog"></i>
                <span class="sidebar-text">الإعدادات</span>
            </a>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item <?php echo $current_page == 'settings' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog me-2"></i>إعدادات النظام
                </a></li>
                <li><a class="dropdown-item <?php echo $current_page == 'currencies' ? 'active' : ''; ?>" href="currencies.php">
                    <i class="fas fa-coins me-2"></i>إدارة العملات
                </a></li>
                <li><a class="dropdown-item <?php echo $current_page == 'branches' ? 'active' : ''; ?>" href="branches.php">
                    <i class="fas fa-building me-2"></i>إدارة الفروع
                </a></li>
                <li><a class="dropdown-item <?php echo $current_page == 'partners' ? 'active' : ''; ?>" href="partners.php">
                    <i class="fas fa-handshake me-2"></i>إدارة الشركاء
                </a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- إدارة المستخدمين -->
        <?php if ($auth->hasPermission('users.view')): ?>
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle <?php echo in_array($current_page, ['users', 'roles', 'permissions']) ? 'active' : ''; ?>" 
               href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-user-cog"></i>
                <span class="sidebar-text">إدارة المستخدمين</span>
            </a>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item <?php echo $current_page == 'users' ? 'active' : ''; ?>" href="users.php">
                    <i class="fas fa-users me-2"></i>المستخدمين
                </a></li>
                <li><a class="dropdown-item <?php echo $current_page == 'roles' ? 'active' : ''; ?>" href="roles.php">
                    <i class="fas fa-user-tag me-2"></i>الأدوار
                </a></li>
                <li><a class="dropdown-item <?php echo $current_page == 'permissions' ? 'active' : ''; ?>" href="permissions.php">
                    <i class="fas fa-key me-2"></i>الصلاحيات
                </a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- الأمان والمراجعة -->
        <?php if ($auth->hasPermission('audit.view')): ?>
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle <?php echo in_array($current_page, ['audit_logs', 'security', 'backup']) ? 'active' : ''; ?>" 
               href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-shield-alt"></i>
                <span class="sidebar-text">الأمان والمراجعة</span>
            </a>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item <?php echo $current_page == 'audit_logs' ? 'active' : ''; ?>" href="audit_logs.php">
                    <i class="fas fa-history me-2"></i>سجلات المراجعة
                </a></li>
                <li><a class="dropdown-item <?php echo $current_page == 'security' ? 'active' : ''; ?>" href="security.php">
                    <i class="fas fa-lock me-2"></i>إعدادات الأمان
                </a></li>
                <li><a class="dropdown-item <?php echo $current_page == 'backup' ? 'active' : ''; ?>" href="backup.php">
                    <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                </a></li>
            </ul>
        </li>
        <?php endif; ?>
        
        <!-- فاصل -->
        <li class="nav-item">
            <hr class="sidebar-divider">
        </li>
        
        <!-- الملف الشخصي -->
        <li class="nav-item">
            <a class="nav-link <?php echo $current_page == 'profile' ? 'active' : ''; ?>" href="profile.php">
                <i class="fas fa-user"></i>
                <span class="sidebar-text">الملف الشخصي</span>
            </a>
        </li>
        
        <!-- المساعدة -->
        <li class="nav-item">
            <a class="nav-link <?php echo $current_page == 'help' ? 'active' : ''; ?>" href="help.php">
                <i class="fas fa-question-circle"></i>
                <span class="sidebar-text">المساعدة</span>
            </a>
        </li>
        
        <!-- تسجيل الخروج -->
        <li class="nav-item mt-auto">
            <a class="nav-link" href="../auth/logout.php" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                <i class="fas fa-sign-out-alt"></i>
                <span class="sidebar-text">تسجيل الخروج</span>
            </a>
        </li>
    </ul>
    
    <!-- تذييل الشريط الجانبي -->
    <div class="sidebar-footer">
        <div class="p-3 text-center border-top border-light">
            <small class="text-white-50 sidebar-text">
                الإصدار <?php echo defined('SYSTEM_VERSION') ? SYSTEM_VERSION : '1.0.0'; ?>
            </small>
        </div>
    </div>
</nav>

<!-- زر تبديل الشريط الجانبي للهواتف -->
<button class="btn btn-primary sidebar-toggle d-lg-none" type="button" data-sidebar-toggle>
    <i class="fas fa-bars"></i>
</button>

<!-- خلفية شفافة للهواتف -->
<div class="sidebar-backdrop d-lg-none" onclick="TrustPlus.ui.toggleSidebar()"></div>
