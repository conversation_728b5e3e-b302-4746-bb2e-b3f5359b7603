<?php
/**
 * Exchange Rate API Manager
 * إدارة استدعاء API لأسعار الصرف
 */

class ExchangeRateAPI {
    private $api_key;
    private $base_url;
    private $db;
    
    public function __construct() {
        // يمكن تخزين مفتاح API في ملف الإعدادات أو قاعدة البيانات
        $this->api_key = 'YOUR_API_KEY'; // يمكن تغييره لاحقاً (مفتاح تجريبي)
        $this->base_url = 'https://open.er-api.com/v6/latest/'; // نستخدم API مجاني للتجربة
        
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * الحصول على أسعار الصرف من API
     * @param string $base_currency العملة الأساسية (مثل USD)
     * @return array|false
     */
    public function fetchLatestRates($base_currency = 'USD') {
        try {
            error_log("Fetching exchange rates for $base_currency from API");
            
            // استخدام curl للاتصال بالـ API
            $ch = curl_init();
            $url = $this->base_url . urlencode($base_currency);
            
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10); // تحديد مهلة انتظار 10 ثوان
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if ($error = curl_error($ch)) {
                error_log("API call error: " . $error);
                curl_close($ch);
                return false;
            }
            
            curl_close($ch);
            
            if ($httpCode != 200) {
                error_log("API returned error code: $httpCode, response: $response");
                return false;
            }
            
            $data = json_decode($response, true);
            
            // تسجيل الاستجابة للتصحيح
            error_log("API Response: " . json_encode($data));
            
            if (!$data || !isset($data['rates'])) {
                error_log("Invalid API response format");
                return false;
            }
            
            // التحقق من نجاح عملية الاستدعاء
            if (isset($data['result']) && $data['result'] === 'success') {
                return [
                    'base_currency' => $data['base_code'],
                    'rates' => $data['rates'],
                    'timestamp' => $data['time_last_update_unix']
                ];
            } elseif (isset($data['rates']) && !empty($data['rates'])) {
                // بعض واجهات API لا ترسل ترويسة "نجاح"، لكنها ترسل أسعار صحيحة
                return [
                    'base_currency' => $base_currency,
                    'rates' => $data['rates'],
                    'timestamp' => time() // استخدام الوقت الحالي إذا لم يكن متاحًا
                ];
            } else {
                error_log("API call failed: " . json_encode($data));
                return false;
            }
        } catch (Exception $e) {
            error_log("Exception in API call: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث أسعار الصرف في قاعدة البيانات باستخدام البيانات من API
     * @param int $user_id معرف المستخدم الذي يقوم بالتحديث
     * @return array ['success' => boolean, 'message' => string]
     */
    public function updateRatesFromAPI($user_id) {
        try {
            // الحصول على الأسعار من API
            $rates_data = $this->fetchLatestRates('USD'); // استخدام الدولار كعملة أساسية
            
            if (!$rates_data) {
                return ['success' => false, 'message' => 'فشل في الحصول على أسعار الصرف من API'];
            }
            
            // بدء معاملة قاعدة البيانات
            $this->db->beginTransaction();
            
            // الحصول على جميع العملات النشطة من قاعدة البيانات
            $stmt = $this->db->prepare("SELECT id, code FROM currencies WHERE is_active = 1");
            $stmt->execute();
            $currencies = $stmt->fetchAll();
            
            // إنشاء مصفوفة للربط بين رموز العملات والمعرفات
            $currency_map = [];
            foreach ($currencies as $currency) {
                $currency_map[$currency['code']] = $currency['id'];
            }
            
            // التأكد من وجود الدولار كعملة أساسية
            if (!isset($currency_map['USD'])) {
                $this->db->rollBack();
                return ['success' => false, 'message' => 'العملة الأساسية (USD) غير موجودة في النظام'];
            }
            
            // عداد العملات التي تم تحديثها
            $updated_pairs = 0;
            
            // تحديث سعر الصرف لكل زوج من العملات
            foreach ($currencies as $currency) {
                if ($currency['code'] === 'USD') continue; // تخطي الدولار نفسه
                
                $currency_code = $currency['code'];
                $currency_id = $currency['id'];
                $usd_id = $currency_map['USD'];
                
                // إذا كان رمز العملة موجوداً في نتائج API
                if (isset($rates_data['rates'][$currency_code])) {
                    $rate = floatval($rates_data['rates'][$currency_code]);
                    
                    if ($rate <= 0) {
                        error_log("Invalid rate for $currency_code: $rate");
                        continue;
                    }
                    
                    // إضافة هامش للبيع والشراء (يمكن تعديله حسب سياسة العمل)
                    $buy_margin = 0.02; // 2% هامش للشراء
                    $sell_margin = 0.03; // 3% هامش للبيع
                    
                    // تحديث الأسعار
                    $this->updateCurrencyPairRates(
                        $usd_id, 
                        $currency_id, 
                        1 / $rate * (1 - $buy_margin), 
                        1 / $rate * (1 + $sell_margin),
                        $user_id
                    );
                    $updated_pairs++;
                    
                    // تحديث الأسعار بشكل عكسي أيضاً
                    $this->updateCurrencyPairRates(
                        $currency_id,
                        $usd_id,
                        $rate * (1 - $buy_margin),
                        $rate * (1 + $sell_margin),
                        $user_id
                    );
                    $updated_pairs++;
                }
            }
            
            // حفظ التغييرات
            $this->db->commit();
            
            // تسجيل حدث تحديث الأسعار
            $this->logSystemEvent('api_rates_updated', [
                'source' => 'API',
                'updated_pairs' => $updated_pairs,
                'timestamp' => date('Y-m-d H:i:s')
            ], $user_id);
            
            return [
                'success' => true,
                'message' => "تم تحديث $updated_pairs زوج من أسعار الصرف من API",
                'updated_pairs' => $updated_pairs,
                'last_update' => date('Y-m-d H:i:s', $rates_data['timestamp'])
            ];
            
        } catch (Exception $e) {
            if ($this->db && $this->db->inTransaction()) {
                $this->db->rollBack();
            }
            error_log("Error updating rates from API: " . $e->getMessage());
            return ['success' => false, 'message' => 'خطأ في تحديث أسعار الصرف: ' . $e->getMessage()];
        }
    }
    
    /**
     * تحديث أسعار زوج عملات معين في قاعدة البيانات
     */
    private function updateCurrencyPairRates($from_id, $to_id, $buy_rate, $sell_rate, $user_id) {
        try {
            // إلغاء تنشيط الأسعار القديمة
            $stmt = $this->db->prepare("
                UPDATE exchange_rates 
                SET is_active = 0 
                WHERE from_currency_id = :from_id 
                  AND to_currency_id = :to_id
                  AND effective_date = CURDATE()
            ");
            $stmt->bindParam(':from_id', $from_id);
            $stmt->bindParam(':to_id', $to_id);
            $stmt->execute();
            
            // إضافة الأسعار الجديدة
            $stmt = $this->db->prepare("
                INSERT INTO exchange_rates (
                    from_currency_id, to_currency_id, 
                    buy_rate, sell_rate,
                    effective_date, user_id, is_active,
                    source
                ) VALUES (
                    :from_id, :to_id,
                    :buy_rate, :sell_rate,
                    CURDATE(), :user_id, 1,
                    'API'
                )
            ");
            $stmt->bindParam(':from_id', $from_id);
            $stmt->bindParam(':to_id', $to_id);
            $stmt->bindParam(':buy_rate', $buy_rate);
            $stmt->bindParam(':sell_rate', $sell_rate);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            
            return true;
        } catch (Exception $e) {
            error_log("Error updating pair rates: " . $e->getMessage());
            throw $e; // إعادة رمي الاستثناء للمعالجة في الدالة الأم
        }
    }
    
    /**
     * تسجيل حدث نظام
     */
    private function logSystemEvent($event_type, $details, $user_id) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO system_events (
                    event_type, event_details, user_id
                ) VALUES (
                    :event_type, :details, :user_id
                )
            ");
            $details_json = json_encode($details);
            $stmt->bindParam(':event_type', $event_type);
            $stmt->bindParam(':details', $details_json);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
        } catch (Exception $e) {
            error_log("Failed to log system event: " . $e->getMessage());
            // لا نرمي استثناءً هنا حتى لا تفشل العملية الرئيسية
        }
    }
}
?> 