<?php
/**
 * Exchange Rate API Manager
 * إدارة استدعاء API لأسعار الصرف
 */

class ExchangeRateAPI {
    private $api_key;
    private $base_url;
    private $db;
    private $provider;
    
    public function __construct() {
        // تهيئة قاعدة البيانات
        $database = new Database();
        $this->db = $database->getConnection();
        
        // تحديد مزود البيانات الافتراضي
        $this->setProvider('openexchangerates');
    }
    
    /**
     * تعيين مزود بيانات أسعار الصرف
     * @param string $provider اسم المزود ['openexchangerates', 'exchangerate-api', 'frankfurter']
     */
    public function setProvider($provider) {
        $this->provider = $provider;
        
        switch ($provider) {
            case 'openexchangerates':
                $this->api_key = 'YOUR_OPEN_EXCHANGE_RATES_KEY'; // يمكن تغييره لاحقاً
                $this->base_url = 'https://openexchangerates.org/api/latest.json?app_id=';
                break;
                
            case 'exchangerate-api':
                $this->api_key = 'YOUR_API_KEY'; // يمكن تغييره لاحقاً
                $this->base_url = 'https://open.er-api.com/v6/latest/';
                break;
                
            case 'frankfurter':
                // لا يحتاج مفتاح API
                $this->api_key = '';
                $this->base_url = 'https://api.frankfurter.app/latest?from=';
                break;
                
            default:
                // المزود الافتراضي - مجاني بدون مفتاح API
                $this->api_key = '';
                $this->base_url = 'https://open.er-api.com/v6/latest/';
                break;
        }
    }
    
    /**
     * الحصول على أسعار الصرف من API
     * @param string $base_currency العملة الأساسية (مثل USD)
     * @return array|false
     */
    public function fetchLatestRates($base_currency = 'USD') {
        try {
            error_log("Fetching exchange rates for $base_currency from API (Provider: {$this->provider})");
            
            // بناء URL حسب المزود
            $url = $this->buildApiUrl($base_currency);
            
            // استخدام curl للاتصال بالـ API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_USERAGENT, 'TrustPlus/1.0');
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if ($error = curl_error($ch)) {
                error_log("API call error: " . $error);
                curl_close($ch);
                return false;
            }
            
            curl_close($ch);
            
            if ($httpCode != 200) {
                error_log("API returned error code: $httpCode, response: $response");
                return false;
            }
            
            $data = json_decode($response, true);
            
            // تسجيل الاستجابة للتصحيح
            error_log("API Response: " . substr(json_encode($data), 0, 200) . '...');
            
            // معالجة استجابة API حسب المزود
            return $this->processApiResponse($data, $base_currency);
            
        } catch (Exception $e) {
            error_log("Exception in API call: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * بناء URL لاستدعاء API حسب المزود
     */
    private function buildApiUrl($base_currency) {
        switch ($this->provider) {
            case 'openexchangerates':
                return $this->base_url . $this->api_key . '&base=' . urlencode($base_currency);
                
            case 'exchangerate-api':
                return $this->base_url . urlencode($base_currency);
                
            case 'frankfurter':
                return $this->base_url . urlencode($base_currency);
                
            default:
                return $this->base_url . urlencode($base_currency);
        }
    }
    
    /**
     * معالجة استجابة API حسب المزود
     */
    private function processApiResponse($data, $base_currency) {
        if (!$data) {
            return false;
        }
        
        switch ($this->provider) {
            case 'openexchangerates':
                if (isset($data['rates'])) {
                    return [
                        'base_currency' => $data['base'] ?? $base_currency,
                        'rates' => $data['rates'],
                        'timestamp' => $data['timestamp'] ?? time()
                    ];
                }
                break;
                
            case 'exchangerate-api':
                if (isset($data['result']) && $data['result'] === 'success' && isset($data['rates'])) {
                    return [
                        'base_currency' => $data['base_code'] ?? $base_currency,
                        'rates' => $data['rates'],
                        'timestamp' => $data['time_last_update_unix'] ?? time()
                    ];
                }
                break;
                
            case 'frankfurter':
                if (isset($data['rates'])) {
                    return [
                        'base_currency' => $data['base'] ?? $base_currency,
                        'rates' => $data['rates'],
                        'timestamp' => strtotime($data['date'] ?? 'now')
                    ];
                }
                break;
                
            default:
                if (isset($data['rates'])) {
                    return [
                        'base_currency' => $base_currency,
                        'rates' => $data['rates'],
                        'timestamp' => time()
                    ];
                }
                break;
        }
        
        error_log("Failed to process API response from {$this->provider}");
        return false;
    }
    
    /**
     * تحديث أسعار الصرف في قاعدة البيانات باستخدام البيانات من API
     * @param int $user_id معرف المستخدم الذي يقوم بالتحديث
     * @return array ['success' => boolean, 'message' => string]
     */
    public function updateRatesFromAPI($user_id) {
        try {
            // الحصول على الأسعار من API - نبدأ بالدولار كعملة أساسية
            $rates_data = $this->fetchLatestRates('USD'); 
            
            if (!$rates_data) {
                // محاولة باستخدام مزود بيانات آخر
                $this->setProvider('frankfurter');
                $rates_data = $this->fetchLatestRates('USD');
                
                if (!$rates_data) {
                    // محاولة أخيرة باستخدام مزود بيانات آخر
                    $this->setProvider('exchangerate-api');
                    $rates_data = $this->fetchLatestRates('USD');
                    
                    if (!$rates_data) {
                        return ['success' => false, 'message' => 'فشل في الحصول على أسعار الصرف من جميع مزودي البيانات المتاحين'];
                    }
                }
            }
            
            // بدء معاملة قاعدة البيانات
            $this->db->beginTransaction();
            
            // الحصول على جميع العملات النشطة من قاعدة البيانات
            $stmt = $this->db->prepare("SELECT id, code FROM currencies WHERE is_active = 1");
            $stmt->execute();
            $currencies = $stmt->fetchAll();
            
            // إنشاء مصفوفة للربط بين رموز العملات والمعرفات
            $currency_map = [];
            foreach ($currencies as $currency) {
                $currency_map[$currency['code']] = $currency['id'];
            }
            
            // البحث عن الدولار كعملة أساسية أو العملة الأساسية للنظام
            $base_currency_id = null;
            if (isset($currency_map['USD'])) {
                $base_currency_id = $currency_map['USD'];
            } else {
                // البحث عن العملة الأساسية للنظام
                $stmt = $this->db->prepare("SELECT id, code FROM currencies WHERE is_base_currency = 1 LIMIT 1");
                $stmt->execute();
                $base_currency = $stmt->fetch();
                
                if (!$base_currency) {
                    $this->db->rollBack();
                    return ['success' => false, 'message' => 'لم يتم العثور على عملة أساسية في النظام'];
                }
                
                $base_currency_id = $base_currency['id'];
                $base_currency_code = $base_currency['code'];
                
                // إذا لم تكن العملة الأساسية هي الدولار، نحتاج للحصول على أسعار الصرف بالعملة الأساسية
                if ($base_currency_code !== 'USD') {
                    $rates_data = $this->fetchLatestRates($base_currency_code);
                    if (!$rates_data) {
                        $this->db->rollBack();
                        return ['success' => false, 'message' => "فشل في الحصول على أسعار الصرف بالعملة الأساسية $base_currency_code"];
                    }
                }
            }
            
            // عداد العملات التي تم تحديثها
            $updated_pairs = 0;
            
            // تحديث سعر الصرف لكل زوج من العملات
            foreach ($currencies as $currency) {
                if ($currency['code'] === $rates_data['base_currency']) continue; // تخطي العملة الأساسية نفسها
                
                $currency_code = $currency['code'];
                $currency_id = $currency['id'];
                
                // إذا كان رمز العملة موجوداً في نتائج API
                if (isset($rates_data['rates'][$currency_code])) {
                    $rate = floatval($rates_data['rates'][$currency_code]);
                    
                    if ($rate <= 0) {
                        error_log("Invalid rate for $currency_code: $rate");
                        continue;
                    }
                    
                    // إضافة هامش للبيع والشراء حسب سياسة العمل
                    $buy_margin = 0.02; // 2% هامش للشراء
                    $sell_margin = 0.03; // 3% هامش للبيع
                    
                    // الحصول على معرف العملة الأساسية من الخريطة
                    $base_id = $currency_map[$rates_data['base_currency']];
                    
                    // تحديث الأسعار من العملة الأساسية إلى العملة الحالية
                    $this->updateCurrencyPairRates(
                        $base_id, 
                        $currency_id, 
                        $rate * (1 - $buy_margin), 
                        $rate * (1 + $sell_margin),
                        $user_id
                    );
                    $updated_pairs++;
                    
                    // تحديث الأسعار بشكل عكسي أيضاً (من العملة الحالية إلى العملة الأساسية)
                    $this->updateCurrencyPairRates(
                        $currency_id,
                        $base_id,
                        1 / $rate * (1 - $buy_margin),
                        1 / $rate * (1 + $sell_margin),
                        $user_id
                    );
                    $updated_pairs++;
                }
            }
            
            // إنشاء أزواج صرف بين العملات الأخرى
            foreach ($currencies as $currency1) {
                foreach ($currencies as $currency2) {
                    // تخطي نفس العملة ونخطي أزواج العملات المتعلقة بالعملة الأساسية (تم معالجتها سابقًا)
                    if ($currency1['code'] === $currency2['code'] || 
                        $currency1['code'] === $rates_data['base_currency'] || 
                        $currency2['code'] === $rates_data['base_currency']) {
                        continue;
                    }
                    
                    // لدينا الآن زوج من العملات ليس العملة الأساسية
                    $currency1_code = $currency1['code'];
                    $currency2_code = $currency2['code'];
                    
                    if (isset($rates_data['rates'][$currency1_code]) && isset($rates_data['rates'][$currency2_code])) {
                        $rate1 = floatval($rates_data['rates'][$currency1_code]);
                        $rate2 = floatval($rates_data['rates'][$currency2_code]);
                        
                        if ($rate1 <= 0 || $rate2 <= 0) {
                            continue;
                        }
                        
                        // حساب سعر صرف من currency1 إلى currency2
                        $cross_rate = $rate2 / $rate1;
                        
                        // إضافة هامش
                        $buy_margin = 0.02;
                        $sell_margin = 0.03;
                        
                        // تحديث الأسعار
                        $this->updateCurrencyPairRates(
                            $currency1['id'],
                            $currency2['id'],
                            $cross_rate * (1 - $buy_margin),
                            $cross_rate * (1 + $sell_margin),
                            $user_id
                        );
                        $updated_pairs++;
                    }
                }
            }
            
            // حفظ التغييرات
            $this->db->commit();
            
            // تسجيل حدث تحديث الأسعار
            $this->logSystemEvent('api_rates_updated', [
                'source' => $this->provider,
                'updated_pairs' => $updated_pairs,
                'timestamp' => date('Y-m-d H:i:s'),
                'base_currency' => $rates_data['base_currency']
            ], $user_id);
            
            return [
                'success' => true,
                'message' => "تم تحديث $updated_pairs زوج من أسعار الصرف من API ({$this->provider})",
                'updated_pairs' => $updated_pairs,
                'provider' => $this->provider,
                'last_update' => date('Y-m-d H:i:s', $rates_data['timestamp'])
            ];
            
        } catch (Exception $e) {
            if ($this->db && $this->db->inTransaction()) {
                $this->db->rollBack();
            }
            error_log("Error updating rates from API: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ['success' => false, 'message' => 'خطأ في تحديث أسعار الصرف: ' . $e->getMessage()];
        }
    }
    
    /**
     * تحديث أسعار زوج عملات معين في قاعدة البيانات
     */
    private function updateCurrencyPairRates($from_id, $to_id, $buy_rate, $sell_rate, $user_id) {
        try {
            // التأكد من أن السعر إيجابي وليس صفر
            $buy_rate = max(0.000001, floatval($buy_rate));
            $sell_rate = max(0.000001, floatval($sell_rate));
            
            // البحث عن سعر صرف موجود لنفس اليوم
            $stmt = $this->db->prepare("
                SELECT id 
                FROM exchange_rates 
                WHERE from_currency_id = :from_id 
                  AND to_currency_id = :to_id
                  AND effective_date = CURDATE()
            ");
            $stmt->bindParam(':from_id', $from_id);
            $stmt->bindParam(':to_id', $to_id);
            $stmt->execute();
            $existing_rate = $stmt->fetch();
            
            if ($existing_rate) {
                // تحديث السعر الموجود
                $stmt = $this->db->prepare("
                    UPDATE exchange_rates 
                    SET buy_rate = :buy_rate,
                        sell_rate = :sell_rate,
                        user_id = :user_id,
                        is_active = 1,
                        source = 'API'
                    WHERE id = :id
                ");
                $stmt->bindParam(':id', $existing_rate['id']);
                $stmt->bindParam(':buy_rate', $buy_rate);
                $stmt->bindParam(':sell_rate', $sell_rate);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
            } else {
                // إضافة سعر جديد
                $stmt = $this->db->prepare("
                    INSERT INTO exchange_rates (
                        from_currency_id, to_currency_id, 
                        buy_rate, sell_rate,
                        effective_date, user_id, is_active,
                        source
                    ) VALUES (
                        :from_id, :to_id,
                        :buy_rate, :sell_rate,
                        CURDATE(), :user_id, 1,
                        'API'
                    )
                ");
                $stmt->bindParam(':from_id', $from_id);
                $stmt->bindParam(':to_id', $to_id);
                $stmt->bindParam(':buy_rate', $buy_rate);
                $stmt->bindParam(':sell_rate', $sell_rate);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Error updating pair rates: " . $e->getMessage());
            throw $e; // إعادة رمي الاستثناء للمعالجة في الدالة الأم
        }
    }
    
    /**
     * تسجيل حدث نظام
     */
    private function logSystemEvent($event_type, $details, $user_id) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO system_events (
                    event_type, event_details, user_id
                ) VALUES (
                    :event_type, :details, :user_id
                )
            ");
            $details_json = json_encode($details);
            $stmt->bindParam(':event_type', $event_type);
            $stmt->bindParam(':details', $details_json);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
        } catch (Exception $e) {
            error_log("Failed to log system event: " . $e->getMessage());
            // لا نرمي استثناءً هنا حتى لا تفشل العملية الرئيسية
        }
    }
}
?> 