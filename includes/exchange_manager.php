<?php
/**
 * فئة إدارة عمليات الصرافة مع حساب الأرباح - Trust Plus System
 */

require_once 'auth.php';

class ExchangeManager {
    private $db;
    private $auth;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->auth = new Auth();
    }
    
    /**
     * إنشاء عملية صرافة جديدة
     */
    public function createExchangeTransaction($data, $user_id) {
        try {
            // تسجيل بداية العملية
            error_log("Starting exchange transaction: " . json_encode($data));
            
            $this->db->beginTransaction();
            
            // التحقق من صحة البيانات
            $validation = $this->validateExchangeData($data);
            if (!$validation['valid']) {
                throw new Exception($validation['message']);
            }
            
            // التحقق من حالة العميل
            $customer_check = $this->validateCustomer($data['customer_id'], $data['sell_amount']);
            if (!$customer_check['valid']) {
                throw new Exception($customer_check['message']);
            }
            
            // الحصول على أسعار الصرف الحالية
            $rates = $this->getCurrentExchangeRates($data['sell_currency_id'], $data['buy_currency_id']);
            if (!$rates) {
                // محاولة الحصول على السعر معكوساً
                $reverse_rates = $this->getCurrentExchangeRates($data['buy_currency_id'], $data['sell_currency_id']);
                
                if ($reverse_rates) {
                    // عكس السعر للاستخدام
                    $buy_rate_original = $reverse_rates['buy_rate'];
                    $sell_rate_original = $reverse_rates['sell_rate'];
                    
                    $rates = [
                        'buy_rate' => 1 / $sell_rate_original,
                        'sell_rate' => 1 / $buy_rate_original,
                        'effective_date' => $reverse_rates['effective_date']
                    ];
                    
                    error_log("Using reversed exchange rates: " . json_encode($rates));
                } else {
                    throw new Exception('أسعار الصرف غير متوفرة لهذا الزوج من العملات');
                }
            }
            
            // حساب المبالغ والأرباح
            $calculations = $this->calculateExchangeAmounts($data, $rates);
            
            // التحقق من الحدود اليومية
            $limit_check = $this->checkDailyLimits($data['sell_currency_id'], $data['buy_currency_id'], $calculations['sell_amount'], $user_id);
            if (!$limit_check['valid']) {
                throw new Exception($limit_check['message']);
            }
            
            // إنشاء رقم مرجعي فريد
            $reference_number = $this->generateReferenceNumber('EX');
            
            // إنشاء المعاملة الرئيسية
            $stmt = $this->db->prepare("
                INSERT INTO transactions (
                    transaction_date, transaction_type, reference_number, description,
                    total_amount_base_currency, branch_id, user_id, customer_id, status
                ) VALUES (
                    CURDATE(), 'exchange', :reference_number, :description,
                    :total_amount, :branch_id, :user_id, :customer_id, 'completed'
                )
            ");
            
            $description = sprintf(
                'صرافة: %s %s إلى %s %s - سعر الصرف: %s',
                number_format($calculations['sell_amount'], 2),
                $calculations['sell_currency_code'],
                number_format($calculations['buy_amount'], 2),
                $calculations['buy_currency_code'],
                $calculations['exchange_rate_used']
            );
            
            $stmt->bindParam(':reference_number', $reference_number);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':total_amount', $calculations['total_amount_base']);
            $stmt->bindParam(':branch_id', $data['branch_id']);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':customer_id', $data['customer_id']);
            $stmt->execute();
            
            $transaction_id = $this->db->lastInsertId();
            if (!$transaction_id) {
                throw new Exception("فشل في إنشاء المعاملة الرئيسية");
            }
            
            error_log("Created transaction #$transaction_id: $reference_number");
            
            // إنشاء تفاصيل عملية الصرافة
            $stmt = $this->db->prepare("
                INSERT INTO currency_exchange_transactions (
                    transaction_id, customer_id, buy_currency_id, buy_amount,
                    sell_currency_id, sell_amount, exchange_rate_used,
                    commission_amount, commission_currency_id
                ) VALUES (
                    :transaction_id, :customer_id, :buy_currency_id, :buy_amount,
                    :sell_currency_id, :sell_amount, :exchange_rate_used,
                    :commission_amount, :commission_currency_id
                )
            ");
            
            $stmt->bindParam(':transaction_id', $transaction_id);
            $stmt->bindParam(':customer_id', $data['customer_id']);
            $stmt->bindParam(':buy_currency_id', $data['buy_currency_id']);
            $stmt->bindParam(':buy_amount', $calculations['buy_amount']);
            $stmt->bindParam(':sell_currency_id', $data['sell_currency_id']);
            $stmt->bindParam(':sell_amount', $calculations['sell_amount']);
            $stmt->bindParam(':exchange_rate_used', $calculations['exchange_rate_used']);
            $stmt->bindParam(':commission_amount', $calculations['commission_amount']);
            $stmt->bindParam(':commission_currency_id', $calculations['commission_currency_id']);
            $stmt->execute();
            
            $exchange_id = $this->db->lastInsertId();
            if (!$exchange_id) {
                throw new Exception("فشل في إنشاء تفاصيل عملية الصرافة");
            }
            
            error_log("Created exchange transaction #$exchange_id");
            
            // إنشاء القيود المحاسبية
            $this->createAccountingEntries($transaction_id, $calculations);
            
            // تحديث أرباح اليوم
            $this->updateDailyProfits($calculations, $data['branch_id']);
            
            // تسجيل الحدث
            $this->logSystemEvent('exchange_transaction', [
                'transaction_id' => $transaction_id,
                'exchange_id' => $exchange_id,
                'currency_pair' => $calculations['sell_currency_code'] . '/' . $calculations['buy_currency_code'],
                'amount' => $calculations['sell_amount'],
                'profit' => $calculations['total_profit']
            ], $data['customer_id'], $transaction_id, $user_id);
            
            $this->db->commit();
            error_log("Exchange transaction committed successfully");
            
            return [
                'success' => true,
                'message' => 'تم إنشاء عملية الصرافة بنجاح',
                'transaction_id' => $transaction_id,
                'exchange_id' => $exchange_id,
                'reference_number' => $reference_number,
                'calculations' => $calculations
            ];
            
        } catch (Exception $e) {
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
                error_log("Exchange transaction rolled back due to error: " . $e->getMessage());
            }
            
            error_log("Exchange transaction failed: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return [
                'success' => false, 
                'message' => $e->getMessage(),
                'error_details' => $e->getTraceAsString()
            ];
        }
    }
    
    /**
     * حساب مبالغ الصرافة والأرباح
     */
    public function calculateExchangeAmounts($data, $rates) {
        // تحديد نوع العملية (شراء أم بيع)
        $is_selling_base = $data['sell_currency_id'] == BASE_CURRENCY_ID;
        $rate_to_use = $is_selling_base ? $rates['sell_rate'] : $rates['buy_rate'];
        
        // التحقق من الأسعار
        if (!$rate_to_use) {
            throw new Exception("سعر الصرف غير محدد");
        }
        
        // تأكد أن السعر أكبر من الصفر
        if ($rate_to_use <= 0) {
            throw new Exception("سعر الصرف يجب أن يكون أكبر من الصفر");
        }
        
        // حساب المبلغ المستلم
        if (isset($data['sell_amount']) && $data['sell_amount'] > 0) {
            $sell_amount = $data['sell_amount'];
            $buy_amount = $sell_amount * $rate_to_use;
        } else if (isset($data['buy_amount']) && $data['buy_amount'] > 0) {
            $buy_amount = $data['buy_amount'];
            $sell_amount = $buy_amount / $rate_to_use;
        } else {
            throw new Exception("يجب تحديد مبلغ البيع أو الشراء");
        }
        
        // تقريب المبالغ لمنع أخطاء الحسابات
        $sell_amount = round($sell_amount, 2);
        $buy_amount = round($buy_amount, 2);
        
        // الحصول على إعدادات الصرافة
        $settings = $this->getExchangeSettings($data['sell_currency_id'], $data['buy_currency_id'], $data['branch_id']);
        
        // حساب العمولة
        $commission_amount = max(
            $sell_amount * $settings['commission_percentage'],
            $settings['min_commission']
        );
        
        if ($settings['max_commission'] > 0) {
            $commission_amount = min($commission_amount, $settings['max_commission']);
        }
        
        // تقريب العمولة
        $commission_amount = round($commission_amount, 2);
        
        // حساب ربح الفارق (Spread)
        $market_rate = $this->getMarketRate($data['sell_currency_id'], $data['buy_currency_id']);
        $spread_profit = $market_rate ? abs($rate_to_use - $market_rate) * $sell_amount : 0;
        $spread_profit = round($spread_profit, 2);
        
        // إجمالي الربح
        $total_profit = $commission_amount + $spread_profit;
        
        // تحويل إلى العملة الأساسية للمحاسبة
        $total_amount_base = $this->convertToBaseCurrency($sell_amount, $data['sell_currency_id']);
        
        // جلب رموز العملات
        $sell_currency_code = $this->getCurrencyCode($data['sell_currency_id']);
        $buy_currency_code = $this->getCurrencyCode($data['buy_currency_id']);
        
        // تسجيل عملية الحساب
        error_log(
            "Exchange calculation: {$sell_amount} {$sell_currency_code} -> {$buy_amount} {$buy_currency_code}, " .
            "Rate: {$rate_to_use}, Commission: {$commission_amount}"
        );
        
        return [
            'sell_amount' => $sell_amount,
            'buy_amount' => $buy_amount,
            'exchange_rate_used' => $rate_to_use,
            'commission_amount' => $commission_amount,
            'commission_currency_id' => $data['sell_currency_id'],
            'spread_profit' => $spread_profit,
            'total_profit' => $total_profit,
            'total_amount_base' => $total_amount_base,
            'sell_currency_code' => $sell_currency_code,
            'buy_currency_code' => $buy_currency_code
        ];
    }
    
    /**
     * الحصول على أسعار الصرف الحالية
     */
    public function getCurrentExchangeRates($from_currency_id, $to_currency_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT buy_rate, sell_rate, effective_date, source
                FROM exchange_rates
                WHERE from_currency_id = :from_currency_id 
                  AND to_currency_id = :to_currency_id
                  AND is_active = 1
                  AND effective_date <= CURDATE()
                ORDER BY effective_date DESC
                LIMIT 1
            ");
            
            $stmt->bindParam(':from_currency_id', $from_currency_id);
            $stmt->bindParam(':to_currency_id', $to_currency_id);
            $stmt->execute();
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Error in getCurrentExchangeRates: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * تحديث أسعار الصرف
     */
    public function updateExchangeRates($rates_data, $user_id) {
        try {
            $this->db->beginTransaction();
            
            $updated_pairs = 0;
            
            foreach ($rates_data as $rate_data) {
                // إلغاء تفعيل الأسعار القديمة
                $stmt = $this->db->prepare("
                    UPDATE exchange_rates 
                    SET is_active = 0 
                    WHERE from_currency_id = :from_currency_id 
                      AND to_currency_id = :to_currency_id
                      AND effective_date = CURDATE()
                ");
                $stmt->bindParam(':from_currency_id', $rate_data['from_currency_id']);
                $stmt->bindParam(':to_currency_id', $rate_data['to_currency_id']);
                $stmt->execute();
                
                // إدراج السعر الجديد
                $stmt = $this->db->prepare("
                    INSERT INTO exchange_rates (
                        from_currency_id, to_currency_id, buy_rate, sell_rate,
                        effective_date, user_id, is_active
                    ) VALUES (
                        :from_currency_id, :to_currency_id, :buy_rate, :sell_rate,
                        CURDATE(), :user_id, 1
                    )
                ");
                
                $stmt->bindParam(':from_currency_id', $rate_data['from_currency_id']);
                $stmt->bindParam(':to_currency_id', $rate_data['to_currency_id']);
                $stmt->bindParam(':buy_rate', $rate_data['buy_rate']);
                $stmt->bindParam(':sell_rate', $rate_data['sell_rate']);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
                
                $updated_pairs++;
            }
            
            // تسجيل الحدث
            $this->logSystemEvent('rate_updated', [
                'updated_pairs' => $updated_pairs,
                'update_date' => date('Y-m-d')
            ], null, null, $user_id);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => "تم تحديث {$updated_pairs} زوج من أسعار الصرف بنجاح"
            ];
            
        } catch (Exception $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في تحديث أسعار الصرف: ' . $e->getMessage()];
        }
    }
    
    /**
     * الحصول على إحصائيات الصرافة
     */
    public function getExchangeStatistics($date_from = null, $date_to = null, $branch_id = null) {
        try {
            $where_conditions = ['t.transaction_type = "exchange"'];
            $params = [];
            
            if ($date_from) {
                $where_conditions[] = 't.transaction_date >= :date_from';
                $params[':date_from'] = $date_from;
            }
            
            if ($date_to) {
                $where_conditions[] = 't.transaction_date <= :date_to';
                $params[':date_to'] = $date_to;
            }
            
            if ($branch_id) {
                $where_conditions[] = 't.branch_id = :branch_id';
                $params[':branch_id'] = $branch_id;
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(*) as total_transactions,
                    SUM(cet.sell_amount) as total_volume,
                    SUM(cet.commission_amount) as total_commission,
                    AVG(cet.exchange_rate_used) as avg_exchange_rate,
                    c1.code as sell_currency,
                    c2.code as buy_currency,
                    COUNT(DISTINCT t.customer_id) as unique_customers
                FROM transactions t
                JOIN currency_exchange_transactions cet ON t.id = cet.transaction_id
                JOIN currencies c1 ON cet.sell_currency_id = c1.id
                JOIN currencies c2 ON cet.buy_currency_id = c2.id
                $where_clause
                GROUP BY cet.sell_currency_id, cet.buy_currency_id
                ORDER BY total_volume DESC
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * التحقق من صحة بيانات الصرافة
     */
    private function validateExchangeData($data) {
        // تسجيل البيانات للتصحيح
        error_log("Validating exchange data: " . json_encode($data));
        
        $required_fields = ['customer_id', 'sell_currency_id', 'buy_currency_id', 'branch_id'];
        
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                return ['valid' => false, 'message' => "الحقل {$field} مطلوب"];
            }
        }
        
        if (empty($data['sell_amount']) && empty($data['buy_amount'])) {
            return ['valid' => false, 'message' => 'يجب تحديد مبلغ البيع أو الشراء'];
        }
        
        // التحقق من أن المبلغ موجب
        if (isset($data['sell_amount']) && $data['sell_amount'] <= 0) {
            return ['valid' => false, 'message' => 'مبلغ البيع يجب أن يكون أكبر من صفر'];
        }
        
        if (isset($data['buy_amount']) && $data['buy_amount'] <= 0) {
            return ['valid' => false, 'message' => 'مبلغ الشراء يجب أن يكون أكبر من صفر'];
        }
        
        // التحقق من أن العملات مختلفة
        if ($data['sell_currency_id'] == $data['buy_currency_id']) {
            return ['valid' => false, 'message' => 'لا يمكن صرف نفس العملة'];
        }
        
        // التحقق من وجود العملات
        try {
            $stmt = $this->db->prepare("SELECT id FROM currencies WHERE id IN (?, ?) AND is_active = 1");
            $stmt->execute([$data['sell_currency_id'], $data['buy_currency_id']]);
            $found_currencies = $stmt->fetchAll();
            
            if (count($found_currencies) != 2) {
                return ['valid' => false, 'message' => 'واحدة أو أكثر من العملات غير نشطة أو غير موجودة'];
            }
        } catch (Exception $e) {
            error_log("Currency validation error: " . $e->getMessage());
            return ['valid' => false, 'message' => 'خطأ في التحقق من العملات'];
        }
        
        // التحقق من وجود الفرع
        try {
            $stmt = $this->db->prepare("SELECT id FROM branches WHERE id = ? AND is_active = 1");
            $stmt->execute([$data['branch_id']]);
            if (!$stmt->fetch()) {
                return ['valid' => false, 'message' => 'الفرع غير نشط أو غير موجود'];
            }
        } catch (Exception $e) {
            error_log("Branch validation error: " . $e->getMessage());
            return ['valid' => false, 'message' => 'خطأ في التحقق من الفرع'];
        }
        
        return ['valid' => true, 'message' => 'البيانات صحيحة'];
    }
    
    /**
     * التحقق من حالة العميل
     */
    private function validateCustomer($customer_id, $amount) {
        try {
            $stmt = $this->db->prepare("
                SELECT kyc_status, blacklisted, risk_level
                FROM customers
                WHERE id = :customer_id
            ");
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();
            
            $customer = $stmt->fetch();
            
            if (!$customer) {
                return ['valid' => false, 'message' => 'العميل غير موجود'];
            }
            
            if ($customer['blacklisted']) {
                return ['valid' => false, 'message' => 'العميل في القائمة السوداء'];
            }
            
            if ($customer['kyc_status'] !== 'approved' && $amount > 10000) {
                return ['valid' => false, 'message' => 'يتطلب اعتماد KYC للمعاملات الكبيرة'];
            }
            
            return ['valid' => true, 'message' => 'العميل مؤهل للمعاملة'];
            
        } catch (Exception $e) {
            return ['valid' => false, 'message' => 'خطأ في التحقق من العميل'];
        }
    }
    
    /**
     * دوال مساعدة
     */
    private function generateReferenceNumber($prefix) {
        return $prefix . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
    
    private function getCurrencyCode($currency_id) {
        $stmt = $this->db->prepare("SELECT code FROM currencies WHERE id = :id");
        $stmt->bindParam(':id', $currency_id);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result ? $result['code'] : '';
    }
    
    private function convertToBaseCurrency($amount, $currency_id) {
        if ($currency_id == BASE_CURRENCY_ID) {
            return $amount;
        }
        
        $rate = $this->getCurrentExchangeRates($currency_id, BASE_CURRENCY_ID);
        return $rate ? $amount * $rate['sell_rate'] : $amount;
    }
    
    private function getMarketRate($from_currency_id, $to_currency_id) {
        $rate = $this->getCurrentExchangeRates($from_currency_id, $to_currency_id);
        return $rate ? ($rate['buy_rate'] + $rate['sell_rate']) / 2 : 0;
    }
    
    private function getExchangeSettings($from_currency_id, $to_currency_id, $branch_id) {
        $currency_pair = $this->getCurrencyCode($from_currency_id) . '/' . $this->getCurrencyCode($to_currency_id);
        
        $stmt = $this->db->prepare("
            SELECT * FROM exchange_settings
            WHERE currency_pair = :currency_pair AND branch_id = :branch_id AND is_active = 1
        ");
        $stmt->bindParam(':currency_pair', $currency_pair);
        $stmt->bindParam(':branch_id', $branch_id);
        $stmt->execute();
        
        $settings = $stmt->fetch();
        
        // إعدادات افتراضية إذا لم توجد
        if (!$settings) {
            return [
                'commission_percentage' => 0.0025,
                'min_commission' => 0,
                'max_commission' => 0,
                'daily_limit' => 100000
            ];
        }
        
        return $settings;
    }
    
    private function checkDailyLimits($sell_currency_id, $buy_currency_id, $amount, $user_id) {
        // التحقق من الحد اليومي للمستخدم والفرع
        // يمكن تطوير هذه الدالة حسب المتطلبات
        return ['valid' => true, 'message' => 'ضمن الحدود المسموحة'];
    }
    
    private function createAccountingEntries($transaction_id, $calculations) {
        // إنشاء القيود المحاسبية
        // يمكن تطوير هذه الدالة لاحقاً مع نظام المحاسبة
    }
    
    private function updateDailyProfits($calculations, $branch_id) {
        $currency_pair = $calculations['sell_currency_code'] . '/' . $calculations['buy_currency_code'];
        
        $stmt = $this->db->prepare("
            INSERT INTO daily_exchange_profits (
                profit_date, currency_pair, from_currency_id, to_currency_id,
                total_transactions, total_volume_from, total_commission,
                total_spread_profit, total_profit, branch_id
            ) VALUES (
                CURDATE(), :currency_pair, :from_currency_id, :to_currency_id,
                1, :volume, :commission, :spread_profit, :total_profit, :branch_id
            ) ON DUPLICATE KEY UPDATE
                total_transactions = total_transactions + 1,
                total_volume_from = total_volume_from + :volume,
                total_commission = total_commission + :commission,
                total_spread_profit = total_spread_profit + :spread_profit,
                total_profit = total_profit + :total_profit
        ");
        
        $stmt->bindParam(':currency_pair', $currency_pair);
        $stmt->bindParam(':from_currency_id', $calculations['sell_currency_code']);
        $stmt->bindParam(':to_currency_id', $calculations['buy_currency_code']);
        $stmt->bindParam(':volume', $calculations['sell_amount']);
        $stmt->bindParam(':commission', $calculations['commission_amount']);
        $stmt->bindParam(':spread_profit', $calculations['spread_profit']);
        $stmt->bindParam(':total_profit', $calculations['total_profit']);
        $stmt->bindParam(':branch_id', $branch_id);
        $stmt->execute();
    }
    
    private function logSystemEvent($event_type, $details, $customer_id = null, $transaction_id = null, $user_id = null) {
        $stmt = $this->db->prepare("
            INSERT INTO system_events (
                event_type, event_details, customer_id, transaction_id, user_id
            ) VALUES (
                :event_type, :details, :customer_id, :transaction_id, :user_id
            )
        ");
        $details_json = json_encode($details);
        $stmt->bindParam(':event_type', $event_type);
        $stmt->bindParam(':details', $details_json);
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->bindParam(':transaction_id', $transaction_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
    }
}
?>
