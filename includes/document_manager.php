<?php
/**
 * فئة إدارة المستندات - Trust Plus System
 */

require_once 'auth.php';

class DocumentManager {
    private $db;
    private $upload_path;
    private $allowed_types;
    private $max_file_size;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->upload_path = '../uploads/';
        $this->allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'];
        $this->max_file_size = 5 * 1024 * 1024; // 5MB
        
        // إنشاء مجلد الرفع إذا لم يكن موجوداً
        if (!is_dir($this->upload_path)) {
            mkdir($this->upload_path, 0755, true);
        }
    }
    
    /**
     * رفع مستند جديد
     */
    public function uploadDocument($file, $entity_type, $entity_id, $document_type, $user_id, $notes = '') {
        try {
            // التحقق من صحة الملف
            $validation = $this->validateFile($file);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // إنشاء اسم ملف فريد
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $file_name = $entity_type . '_' . $entity_id . '_' . $document_type . '_' . time() . '.' . $file_extension;
            $file_path = $this->upload_path . $file_name;
            
            // رفع الملف
            if (!move_uploaded_file($file['tmp_name'], $file_path)) {
                return ['success' => false, 'message' => 'فشل في رفع الملف'];
            }
            
            // حفظ معلومات الملف في قاعدة البيانات
            $stmt = $this->db->prepare("
                INSERT INTO documents (
                    related_entity_type, related_entity_id, document_type,
                    file_name, file_path, file_size, mime_type,
                    uploaded_by_user_id, notes
                ) VALUES (
                    :entity_type, :entity_id, :document_type,
                    :file_name, :file_path, :file_size, :mime_type,
                    :user_id, :notes
                )
            ");
            
            $stmt->bindParam(':entity_type', $entity_type);
            $stmt->bindParam(':entity_id', $entity_id);
            $stmt->bindParam(':document_type', $document_type);
            $stmt->bindParam(':file_name', $file['name']);
            $stmt->bindParam(':file_path', $file_path);
            $stmt->bindParam(':file_size', $file['size']);
            $stmt->bindParam(':mime_type', $file['type']);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':notes', $notes);
            
            $stmt->execute();
            $document_id = $this->db->lastInsertId();
            
            return [
                'success' => true, 
                'message' => 'تم رفع المستند بنجاح',
                'document_id' => $document_id
            ];
            
        } catch (Exception $e) {
            // حذف الملف في حالة الخطأ
            if (isset($file_path) && file_exists($file_path)) {
                unlink($file_path);
            }
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }
    
    /**
     * التحقق من المستند
     */
    public function verifyDocument($document_id, $user_id, $notes = '') {
        try {
            $stmt = $this->db->prepare("
                UPDATE documents SET 
                    is_verified = TRUE,
                    verified_by_user_id = :user_id,
                    verified_date = NOW(),
                    notes = CONCAT(COALESCE(notes, ''), '\n', 'تم التحقق: ', :notes)
                WHERE id = :document_id
            ");
            
            $stmt->bindParam(':document_id', $document_id);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':notes', $notes);
            $stmt->execute();
            
            return ['success' => true, 'message' => 'تم التحقق من المستند بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }
    
    /**
     * رفض المستند
     */
    public function rejectDocument($document_id, $user_id, $reason) {
        try {
            $stmt = $this->db->prepare("
                UPDATE documents SET 
                    is_verified = FALSE,
                    verified_by_user_id = :user_id,
                    verified_date = NOW(),
                    notes = CONCAT(COALESCE(notes, ''), '\n', 'تم الرفض: ', :reason)
                WHERE id = :document_id
            ");
            
            $stmt->bindParam(':document_id', $document_id);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':reason', $reason);
            $stmt->execute();
            
            return ['success' => true, 'message' => 'تم رفض المستند'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }
    
    /**
     * حذف مستند
     */
    public function deleteDocument($document_id, $user_id) {
        try {
            // جلب معلومات الملف
            $stmt = $this->db->prepare("SELECT file_path FROM documents WHERE id = :document_id");
            $stmt->bindParam(':document_id', $document_id);
            $stmt->execute();
            $document = $stmt->fetch();
            
            if (!$document) {
                return ['success' => false, 'message' => 'المستند غير موجود'];
            }
            
            // حذف الملف من النظام
            if (file_exists($document['file_path'])) {
                unlink($document['file_path']);
            }
            
            // حذف السجل من قاعدة البيانات
            $stmt = $this->db->prepare("DELETE FROM documents WHERE id = :document_id");
            $stmt->bindParam(':document_id', $document_id);
            $stmt->execute();
            
            return ['success' => true, 'message' => 'تم حذف المستند بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }
    
    /**
     * جلب مستندات كيان معين
     */
    public function getDocuments($entity_type, $entity_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT d.*, 
                       u1.full_name as uploaded_by_name,
                       u2.full_name as verified_by_name
                FROM documents d
                LEFT JOIN users u1 ON d.uploaded_by_user_id = u1.id
                LEFT JOIN users u2 ON d.verified_by_user_id = u2.id
                WHERE d.related_entity_type = :entity_type 
                  AND d.related_entity_id = :entity_id
                ORDER BY d.upload_date DESC
            ");
            
            $stmt->bindParam(':entity_type', $entity_type);
            $stmt->bindParam(':entity_id', $entity_id);
            $stmt->execute();
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * جلب مستند بالمعرف
     */
    public function getDocumentById($document_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT d.*, 
                       u1.full_name as uploaded_by_name,
                       u2.full_name as verified_by_name
                FROM documents d
                LEFT JOIN users u1 ON d.uploaded_by_user_id = u1.id
                LEFT JOIN users u2 ON d.verified_by_user_id = u2.id
                WHERE d.id = :document_id
            ");
            
            $stmt->bindParam(':document_id', $document_id);
            $stmt->execute();
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * التحقق من صحة الملف
     */
    private function validateFile($file) {
        // التحقق من وجود خطأ في الرفع
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['valid' => false, 'message' => 'خطأ في رفع الملف'];
        }
        
        // التحقق من حجم الملف
        if ($file['size'] > $this->max_file_size) {
            return ['valid' => false, 'message' => 'حجم الملف كبير جداً (الحد الأقصى 5MB)'];
        }
        
        // التحقق من نوع الملف
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_extension, $this->allowed_types)) {
            return ['valid' => false, 'message' => 'نوع الملف غير مدعوم'];
        }
        
        // التحقق من MIME type
        $allowed_mimes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        
        if (!in_array($file['type'], $allowed_mimes)) {
            return ['valid' => false, 'message' => 'نوع الملف غير صحيح'];
        }
        
        // التحقق من أن الملف ليس سكريبت
        $file_content = file_get_contents($file['tmp_name'], false, null, 0, 1024);
        if (strpos($file_content, '<?php') !== false || strpos($file_content, '<script') !== false) {
            return ['valid' => false, 'message' => 'الملف يحتوي على محتوى غير آمن'];
        }
        
        return ['valid' => true, 'message' => 'الملف صحيح'];
    }
    
    /**
     * الحصول على أنواع المستندات المدعومة
     */
    public function getDocumentTypes() {
        return [
            'id_front' => 'صورة الهوية (الوجه الأمامي)',
            'id_back' => 'صورة الهوية (الوجه الخلفي)',
            'passport' => 'صورة جواز السفر',
            'utility_bill' => 'فاتورة خدمات (إثبات العنوان)',
            'bank_statement' => 'كشف حساب بنكي',
            'other' => 'مستند آخر'
        ];
    }
    
    /**
     * إحصائيات المستندات
     */
    public function getDocumentStats($entity_type = null, $entity_id = null) {
        try {
            $where_clause = '';
            $params = [];
            
            if ($entity_type && $entity_id) {
                $where_clause = 'WHERE related_entity_type = :entity_type AND related_entity_id = :entity_id';
                $params[':entity_type'] = $entity_type;
                $params[':entity_id'] = $entity_id;
            }
            
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(*) as total_documents,
                    SUM(CASE WHEN is_verified = 1 THEN 1 ELSE 0 END) as verified_documents,
                    SUM(CASE WHEN is_verified = 0 THEN 1 ELSE 0 END) as pending_documents,
                    SUM(file_size) as total_size
                FROM documents 
                $where_clause
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            
            $stmt->execute();
            return $stmt->fetch();
            
        } catch (Exception $e) {
            return [
                'total_documents' => 0,
                'verified_documents' => 0,
                'pending_documents' => 0,
                'total_size' => 0
            ];
        }
    }
}
?>
