<?php
/**
 * فئة إدارة المستخدمين - Trust Plus System
 */

require_once __DIR__ . '/../config.php';

class UserManager {
    private $db;
    
    public function __construct() {
        try {
            // الاتصال بقاعدة البيانات
            $this->db = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
        } catch (PDOException $e) {
            throw new Exception('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب جميع المستخدمين مع معلومات الأدوار والفروع
     */
    public function getAllUsers() {
        try {
            $stmt = $this->db->prepare("
                SELECT u.*, r.role_name, b.name as branch_name
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                LEFT JOIN branches b ON u.branch_id = b.id
                ORDER BY u.created_at DESC
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception('خطأ في جلب المستخدمين: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب مستخدم بواسطة المعرف
     */
    public function getUserById($user_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT u.*, r.role_name, b.name as branch_name
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                LEFT JOIN branches b ON u.branch_id = b.id
                WHERE u.id = :user_id
            ");
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception('خطأ في جلب بيانات المستخدم: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب جميع الأدوار
     */
    public function getAllRoles() {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM roles 
                WHERE is_active = 1 
                ORDER BY role_name
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            // إنشاء دور افتراضي إذا لم توجد أدوار
            $this->createDefaultRole();
            return [['id' => 1, 'role_name' => 'مدير النظام', 'description' => 'صلاحيات كاملة']];
        }
    }
    
    /**
     * جلب جميع الفروع
     */
    public function getAllBranches() {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM branches 
                WHERE is_active = 1 
                ORDER BY name
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            // إنشاء فرع افتراضي إذا لم توجد فروع
            $this->createDefaultBranch();
            return [['id' => 1, 'name' => 'الفرع الرئيسي']];
        }
    }
    
    /**
     * إنشاء مستخدم جديد
     */
    public function createUser($data) {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateUserData($data);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // التحقق من عدم تكرار اسم المستخدم والبريد الإلكتروني
            if ($this->isUsernameExists($data['username'])) {
                return ['success' => false, 'message' => 'اسم المستخدم موجود مسبقاً'];
            }
            
            if ($this->isEmailExists($data['email'])) {
                return ['success' => false, 'message' => 'البريد الإلكتروني موجود مسبقاً'];
            }
            
            $this->db->beginTransaction();
            
            // تشفير كلمة المرور
            $password_hash = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // إدراج المستخدم
            $stmt = $this->db->prepare("
                INSERT INTO users (username, email, password_hash, full_name, role_id, branch_id, is_active)
                VALUES (:username, :email, :password_hash, :full_name, :role_id, :branch_id, :is_active)
            ");
            
            $stmt->bindParam(':username', $data['username']);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':password_hash', $password_hash);
            $stmt->bindParam(':full_name', $data['full_name']);
            $stmt->bindParam(':role_id', $data['role_id'], PDO::PARAM_INT);
            $stmt->bindParam(':branch_id', $data['branch_id'] ?: null, PDO::PARAM_INT);
            $stmt->bindParam(':is_active', isset($data['is_active']) ? 1 : 0, PDO::PARAM_INT);
            
            $stmt->execute();
            $user_id = $this->db->lastInsertId();
            
            // تسجيل العملية في سجل المراجعة
            $this->logActivity($user_id, 'create_user', 'user', $user_id, 'تم إنشاء مستخدم جديد: ' . $data['full_name']);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم إنشاء المستخدم بنجاح', 'user_id' => $user_id];
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في إنشاء المستخدم: ' . $e->getMessage()];
        }
    }
    
    /**
     * تحديث بيانات المستخدم
     */
    public function updateUser($user_id, $data) {
        try {
            // التحقق من وجود المستخدم
            $existing_user = $this->getUserById($user_id);
            if (!$existing_user) {
                return ['success' => false, 'message' => 'المستخدم غير موجود'];
            }
            
            // التحقق من صحة البيانات
            $validation = $this->validateUserData($data, $user_id);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // التحقق من عدم تكرار اسم المستخدم والبريد الإلكتروني
            if ($this->isUsernameExists($data['username'], $user_id)) {
                return ['success' => false, 'message' => 'اسم المستخدم موجود مسبقاً'];
            }
            
            if ($this->isEmailExists($data['email'], $user_id)) {
                return ['success' => false, 'message' => 'البريد الإلكتروني موجود مسبقاً'];
            }
            
            $this->db->beginTransaction();
            
            // تحديث البيانات الأساسية
            $sql = "UPDATE users SET 
                        username = :username,
                        email = :email,
                        full_name = :full_name,
                        role_id = :role_id,
                        branch_id = :branch_id,
                        is_active = :is_active,
                        updated_at = NOW()";
            
            $params = [
                ':username' => $data['username'],
                ':email' => $data['email'],
                ':full_name' => $data['full_name'],
                ':role_id' => $data['role_id'],
                ':branch_id' => $data['branch_id'] ?: null,
                ':is_active' => isset($data['is_active']) ? 1 : 0,
                ':user_id' => $user_id
            ];
            
            // تحديث كلمة المرور إذا تم إدخالها
            if (!empty($data['new_password'])) {
                $sql .= ", password_hash = :password_hash";
                $params[':password_hash'] = password_hash($data['new_password'], PASSWORD_DEFAULT);
            }
            
            $sql .= " WHERE id = :user_id";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            // تسجيل العملية في سجل المراجعة
            $this->logActivity($_SESSION['user_id'] ?? 1, 'update_user', 'user', $user_id, 'تم تحديث بيانات المستخدم: ' . $data['full_name']);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم تحديث بيانات المستخدم بنجاح'];
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في تحديث المستخدم: ' . $e->getMessage()];
        }
    }
    
    /**
     * تغيير حالة المستخدم (تفعيل/إلغاء تفعيل)
     */
    public function toggleUserStatus($user_id) {
        try {
            $user = $this->getUserById($user_id);
            if (!$user) {
                return ['success' => false, 'message' => 'المستخدم غير موجود'];
            }
            
            $new_status = $user['is_active'] ? 0 : 1;
            
            $stmt = $this->db->prepare("
                UPDATE users SET is_active = :status, updated_at = NOW() 
                WHERE id = :user_id
            ");
            $stmt->bindParam(':status', $new_status, PDO::PARAM_INT);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $action = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
            $this->logActivity($_SESSION['user_id'] ?? 1, 'toggle_user_status', 'user', $user_id, $action . ' المستخدم: ' . $user['full_name']);
            
            return ['success' => true, 'message' => 'تم ' . $action . ' المستخدم بنجاح'];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'خطأ في تغيير حالة المستخدم: ' . $e->getMessage()];
        }
    }
    
    /**
     * إعادة تعيين كلمة المرور
     */
    public function resetPassword($user_id) {
        try {
            $user = $this->getUserById($user_id);
            if (!$user) {
                return ['success' => false, 'message' => 'المستخدم غير موجود'];
            }
            
            // إنشاء كلمة مرور جديدة
            $new_password = $this->generateRandomPassword();
            $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
            
            $stmt = $this->db->prepare("
                UPDATE users SET 
                    password_hash = :password_hash,
                    failed_login_attempts = 0,
                    locked_until = NULL,
                    updated_at = NOW()
                WHERE id = :user_id
            ");
            $stmt->bindParam(':password_hash', $password_hash);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $this->logActivity($_SESSION['user_id'] ?? 1, 'reset_password', 'user', $user_id, 'إعادة تعيين كلمة المرور للمستخدم: ' . $user['full_name']);
            
            return [
                'success' => true, 
                'message' => 'تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة: ' . $new_password,
                'new_password' => $new_password
            ];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'خطأ في إعادة تعيين كلمة المرور: ' . $e->getMessage()];
        }
    }

    /**
     * التحقق من صحة بيانات المستخدم
     */
    private function validateUserData($data, $user_id = null) {
        // التحقق من الحقول المطلوبة
        $required_fields = ['username', 'email', 'full_name', 'role_id'];

        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                return ['valid' => false, 'message' => 'جميع الحقول المطلوبة يجب ملؤها'];
            }
        }

        // التحقق من صحة اسم المستخدم
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
            return ['valid' => false, 'message' => 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط'];
        }

        if (strlen($data['username']) < 3 || strlen($data['username']) > 50) {
            return ['valid' => false, 'message' => 'اسم المستخدم يجب أن يكون بين 3 و 50 حرف'];
        }

        // التحقق من صحة البريد الإلكتروني
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return ['valid' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
        }

        // التحقق من كلمة المرور للمستخدمين الجدد
        if (!$user_id && (empty($data['password']) || strlen($data['password']) < 8)) {
            return ['valid' => false, 'message' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'];
        }

        // التحقق من كلمة المرور الجديدة للتحديث
        if ($user_id && !empty($data['new_password']) && strlen($data['new_password']) < 8) {
            return ['valid' => false, 'message' => 'كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل'];
        }

        return ['valid' => true];
    }

    /**
     * التحقق من وجود اسم المستخدم
     */
    private function isUsernameExists($username, $exclude_user_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM users WHERE username = :username";
            $params = [':username' => $username];

            if ($exclude_user_id) {
                $sql .= " AND id != :user_id";
                $params[':user_id'] = $exclude_user_id;
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * التحقق من وجود البريد الإلكتروني
     */
    private function isEmailExists($email, $exclude_user_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM users WHERE email = :email";
            $params = [':email' => $email];

            if ($exclude_user_id) {
                $sql .= " AND id != :user_id";
                $params[':user_id'] = $exclude_user_id;
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * إنشاء كلمة مرور عشوائية
     */
    private function generateRandomPassword($length = 12) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';

        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }

        return $password;
    }

    /**
     * إنشاء دور افتراضي
     */
    private function createDefaultRole() {
        try {
            // إنشاء جدول الأدوار إذا لم يكن موجوداً
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS roles (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    role_name VARCHAR(50) NOT NULL,
                    description TEXT,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            // إدراج دور افتراضي
            $stmt = $this->db->prepare("
                INSERT IGNORE INTO roles (id, role_name, description)
                VALUES (1, 'مدير النظام', 'صلاحيات كاملة للنظام')
            ");
            $stmt->execute();
        } catch (PDOException $e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * إنشاء فرع افتراضي
     */
    private function createDefaultBranch() {
        try {
            // إنشاء جدول الفروع إذا لم يكن موجوداً
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS branches (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    address TEXT,
                    phone VARCHAR(20),
                    email VARCHAR(100),
                    manager_id INT,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            // إدراج فرع افتراضي
            $stmt = $this->db->prepare("
                INSERT IGNORE INTO branches (id, name)
                VALUES (1, 'الفرع الرئيسي')
            ");
            $stmt->execute();
        } catch (PDOException $e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * تسجيل النشاط في سجل المراجعة
     */
    private function logActivity($user_id, $action, $table_name, $record_id, $description = '') {
        try {
            // إنشاء جدول سجل المراجعة إذا لم يكن موجوداً
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    action VARCHAR(50) NOT NULL,
                    table_name VARCHAR(50) NOT NULL,
                    record_id INT NOT NULL,
                    description TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            $stmt = $this->db->prepare("
                INSERT INTO audit_logs (user_id, action, table_name, record_id, description, ip_address, user_agent)
                VALUES (:user_id, :action, :table_name, :record_id, :description, :ip_address, :user_agent)
            ");

            $stmt->execute([
                ':user_id' => $user_id,
                ':action' => $action,
                ':table_name' => $table_name,
                ':record_id' => $record_id,
                ':description' => $description,
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (PDOException $e) {
            // تجاهل أخطاء تسجيل المراجعة
        }
    }
}
?>
