<?php
/**
 * Trust Plus - Footer Template
 * قالب التذييل المشترك
 */
?>
                <!-- نهاية محتوى الصفحة الفعلي -->
            </div>
            
            <?php if (!isset($hide_footer) || !$hide_footer): ?>
                <!-- التذييل -->
                <footer class="page-footer">
                    <div class="container-fluid">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <p class="mb-0 text-muted">
                                    &copy; <?php echo date('Y'); ?> Trust Plus. جميع الحقوق محفوظة.
                                </p>
                            </div>
                            <div class="col-md-6 text-end">
                                <p class="mb-0 text-muted">
                                    الإصدار <?php echo defined('SYSTEM_VERSION') ? SYSTEM_VERSION : '1.0.0'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </footer>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- حاوية التنبيهات -->
    <div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>
    
    <!-- نوافذ منبثقة مشتركة -->
    
    <!-- نافذة تأكيد الحذف -->
    <div class="modal fade" id="confirmDeleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>هل أنت متأكد؟</h5>
                        <p class="text-muted" id="deleteMessage">لن تتمكن من التراجع عن هذا الإجراء!</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-2"></i>حذف
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- نافذة التحميل -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center p-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <h6 id="loadingMessage">جاري المعالجة...</h6>
                    <p class="text-muted small mb-0" id="loadingSubMessage">يرجى الانتظار</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Files -->
    <?php echo Assets::renderJS(); ?>
    
    <!-- JavaScript إضافي للصفحة -->
    <?php if (isset($additional_js)): ?>
        <?php echo $additional_js; ?>
    <?php endif; ?>
    
    <!-- JavaScript مضمن للصفحة -->
    <?php if (isset($inline_js)): ?>
        <script>
            <?php echo $inline_js; ?>
        </script>
    <?php endif; ?>
    
    <!-- إعدادات النظام العامة -->
    <script>
        // إعدادات النظام
        window.SYSTEM_CONFIG = {
            baseUrl: '<?php echo defined('BASE_URL') ? BASE_URL : '/'; ?>',
            language: '<?php echo defined('SYSTEM_LANGUAGE') ? SYSTEM_LANGUAGE : 'ar'; ?>',
            currency: '<?php echo defined('DEFAULT_CURRENCY') ? DEFAULT_CURRENCY : 'USD'; ?>',
            dateFormat: '<?php echo defined('DATE_FORMAT') ? DATE_FORMAT : 'Y-m-d'; ?>',
            timeFormat: '<?php echo defined('TIME_FORMAT') ? TIME_FORMAT : 'H:i:s'; ?>',
            timezone: '<?php echo defined('TIMEZONE') ? TIMEZONE : 'Asia/Riyadh'; ?>'
        };
        
        // معلومات المستخدم الحالي
        <?php if (isset($current_user) && $current_user): ?>
        window.CURRENT_USER = {
            id: <?php echo $current_user['id']; ?>,
            username: '<?php echo htmlspecialchars($current_user['username']); ?>',
            fullName: '<?php echo htmlspecialchars($current_user['full_name']); ?>',
            role: '<?php echo htmlspecialchars($current_user['role_name'] ?? $current_user['role'] ?? 'مستخدم'); ?>',
            roleId: <?php echo $current_user['role_id'] ?? 'null'; ?>,
            branchId: <?php echo $current_user['branch_id'] ?? 'null'; ?>,
            permissions: <?php echo json_encode($current_user['permissions'] ?? []); ?>
        };
        <?php else: ?>
        window.CURRENT_USER = null;
        <?php endif; ?>
        
        // رسائل النظام
        window.SYSTEM_MESSAGES = {
            loading: 'جاري التحميل...',
            saving: 'جاري الحفظ...',
            deleting: 'جاري الحذف...',
            success: 'تم بنجاح',
            error: 'حدث خطأ',
            confirmDelete: 'هل أنت متأكد من الحذف؟',
            noData: 'لا توجد بيانات',
            networkError: 'خطأ في الاتصال بالشبكة',
            sessionExpired: 'انتهت صلاحية الجلسة',
            accessDenied: 'ليس لديك صلاحية للوصول',
            invalidInput: 'بيانات غير صحيحة',
            requiredField: 'هذا الحقل مطلوب'
        };
        
        // دوال مساعدة عامة
        window.showLoading = function(message = 'جاري المعالجة...', subMessage = 'يرجى الانتظار') {
            if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
                console.log(message + ' - ' + subMessage);
                return;
            }
            document.getElementById('loadingMessage').textContent = message;
            document.getElementById('loadingSubMessage').textContent = subMessage;
            new bootstrap.Modal(document.getElementById('loadingModal')).show();
        };

        window.hideLoading = function() {
            if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
                return;
            }
            const modal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
            if (modal) modal.hide();
        };
        
        window.confirmDelete = function(message = 'هل أنت متأكد من الحذف؟') {
            return new Promise((resolve) => {
                if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
                    resolve(confirm(message));
                    return;
                }

                document.getElementById('deleteMessage').textContent = message;
                const modal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
                modal.show();

                const confirmBtn = document.getElementById('confirmDeleteBtn');
                const newConfirmBtn = confirmBtn.cloneNode(true);
                confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

                newConfirmBtn.addEventListener('click', () => {
                    modal.hide();
                    resolve(true);
                });

                document.getElementById('confirmDeleteModal').addEventListener('hidden.bs.modal', () => {
                    resolve(false);
                }, { once: true });
            });
        };
        
        // تفعيل tooltips و popovers
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من وجود Bootstrap
            if (typeof bootstrap !== 'undefined') {
                // Tooltips
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function(tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });

                // Popovers
                const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                popoverTriggerList.map(function(popoverTriggerEl) {
                    return new bootstrap.Popover(popoverTriggerEl);
                });
            }
            
            // تفعيل التحقق من النماذج
            const forms = document.querySelectorAll('.needs-validation');
            forms.forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            });
            
            // إخفاء التنبيهات تلقائياً
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
                alerts.forEach(alert => {
                    if (alert.classList.contains('fade')) {
                        alert.style.transition = 'opacity 0.5s ease';
                        alert.style.opacity = '0';
                        setTimeout(() => {
                            if (alert.parentNode) {
                                alert.remove();
                            }
                        }, 500);
                    }
                });
            }, 5000);
        });
        
        // معالجة الأخطاء العامة
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled Promise Rejection:', e.reason);
        });
    </script>
    
    <!-- Google Analytics (إذا كان مطلوباً) -->
    <?php if (defined('GOOGLE_ANALYTICS_ID') && GOOGLE_ANALYTICS_ID): ?>
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo GOOGLE_ANALYTICS_ID; ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?php echo GOOGLE_ANALYTICS_ID; ?>');
    </script>
    <?php endif; ?>
    
</body>
</html>

<?php
// تنظيف الأصول بعد الاستخدام
Assets::reset();
?>
