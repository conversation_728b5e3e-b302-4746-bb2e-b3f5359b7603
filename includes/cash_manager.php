<?php
/**
 * فئة إدارة الصناديق والبنوك - Trust Plus System
 */

require_once __DIR__ . '/../config.php';

class CashManager {
    private $db;
    
    public function __construct() {
        try {
            // الاتصال بقاعدة البيانات
            $this->db = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
            
            // إنشاء الجداول إذا لم تكن موجودة
            $this->createTables();
            
        } catch (PDOException $e) {
            throw new Exception('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب جميع الصناديق النقدية
     */
    public function getAllCashBoxes($branch_id = null) {
        try {
            $sql = "
                SELECT cb.*, c.name as currency_name, c.code as currency_code, c.symbol as currency_symbol,
                       b.name as branch_name, u.full_name as responsible_user_name
                FROM cash_boxes cb
                LEFT JOIN currencies c ON cb.currency_id = c.id
                LEFT JOIN branches b ON cb.branch_id = b.id
                LEFT JOIN users u ON cb.responsible_user_id = u.id
            ";
            
            $params = [];
            if ($branch_id) {
                $sql .= " WHERE cb.branch_id = :branch_id";
                $params[':branch_id'] = $branch_id;
            }
            
            $sql .= " ORDER BY cb.name ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception('خطأ في جلب الصناديق النقدية: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب جميع الحسابات البنكية
     */
    public function getAllBankAccounts($branch_id = null) {
        try {
            $sql = "
                SELECT ba.*, c.name as currency_name, c.code as currency_code, c.symbol as currency_symbol,
                       b.name as branch_name
                FROM bank_accounts ba
                LEFT JOIN currencies c ON ba.currency_id = c.id
                LEFT JOIN branches b ON ba.branch_id = b.id
            ";
            
            $params = [];
            if ($branch_id) {
                $sql .= " WHERE ba.branch_id = :branch_id";
                $params[':branch_id'] = $branch_id;
            }
            
            $sql .= " ORDER BY ba.account_name ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception('خطأ في جلب الحسابات البنكية: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب صندوق نقدي بواسطة المعرف
     */
    public function getCashBoxById($cash_box_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT cb.*, c.name as currency_name, c.code as currency_code, c.symbol as currency_symbol,
                       b.name as branch_name, u.full_name as responsible_user_name
                FROM cash_boxes cb
                LEFT JOIN currencies c ON cb.currency_id = c.id
                LEFT JOIN branches b ON cb.branch_id = b.id
                LEFT JOIN users u ON cb.responsible_user_id = u.id
                WHERE cb.id = :cash_box_id
            ");
            $stmt->bindParam(':cash_box_id', $cash_box_id, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception('خطأ في جلب بيانات الصندوق النقدي: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب حساب بنكي بواسطة المعرف
     */
    public function getBankAccountById($bank_account_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT ba.*, c.name as currency_name, c.code as currency_code, c.symbol as currency_symbol,
                       b.name as branch_name
                FROM bank_accounts ba
                LEFT JOIN currencies c ON ba.currency_id = c.id
                LEFT JOIN branches b ON ba.branch_id = b.id
                WHERE ba.id = :bank_account_id
            ");
            $stmt->bindParam(':bank_account_id', $bank_account_id, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception('خطأ في جلب بيانات الحساب البنكي: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب جميع العملات النشطة
     */
    public function getActiveCurrencies() {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM currencies 
                WHERE is_active = 1 
                ORDER BY is_base_currency DESC, name ASC
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * جلب جميع الفروع النشطة
     */
    public function getActiveBranches() {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM branches 
                WHERE is_active = 1 
                ORDER BY name ASC
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * جلب جميع المستخدمين النشطين
     */
    public function getActiveUsers($branch_id = null) {
        try {
            $sql = "
                SELECT u.id, u.full_name, u.username, r.role_name
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE u.is_active = 1
            ";
            
            $params = [];
            if ($branch_id) {
                $sql .= " AND u.branch_id = :branch_id";
                $params[':branch_id'] = $branch_id;
            }
            
            $sql .= " ORDER BY u.full_name ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * إنشاء صندوق نقدي جديد
     */
    public function createCashBox($data) {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateCashBoxData($data);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // التحقق من عدم تكرار اسم الصندوق في نفس الفرع
            if ($this->isCashBoxNameExists($data['name'], $data['branch_id'])) {
                return ['success' => false, 'message' => 'اسم الصندوق موجود مسبقاً في هذا الفرع'];
            }
            
            $this->db->beginTransaction();
            
            // إدراج الصندوق النقدي
            $stmt = $this->db->prepare("
                INSERT INTO cash_boxes (name, currency_id, initial_balance, current_balance, branch_id, responsible_user_id, is_active)
                VALUES (:name, :currency_id, :initial_balance, :current_balance, :branch_id, :responsible_user_id, :is_active)
            ");
            
            $initial_balance = floatval($data['initial_balance'] ?? 0);
            
            $stmt->execute([
                ':name' => $data['name'],
                ':currency_id' => $data['currency_id'],
                ':initial_balance' => $initial_balance,
                ':current_balance' => $initial_balance,
                ':branch_id' => $data['branch_id'],
                ':responsible_user_id' => $data['responsible_user_id'] ?: null,
                ':is_active' => isset($data['is_active']) ? 1 : 0
            ]);
            
            $cash_box_id = $this->db->lastInsertId();
            
            // تسجيل الرصيد الافتتاحي إذا كان أكبر من صفر
            if ($initial_balance > 0) {
                $this->recordCashMovement($cash_box_id, 'deposit', $initial_balance, 'الرصيد الافتتاحي', null, $_SESSION['user_id'] ?? 1);
            }
            
            // تسجيل العملية في سجل المراجعة
            $this->logActivity($_SESSION['user_id'] ?? 1, 'create_cash_box', 'cash_box', $cash_box_id, 'تم إنشاء صندوق نقدي جديد: ' . $data['name']);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم إنشاء الصندوق النقدي بنجاح', 'cash_box_id' => $cash_box_id];
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في إنشاء الصندوق النقدي: ' . $e->getMessage()];
        }
    }
    
    /**
     * إنشاء حساب بنكي جديد
     */
    public function createBankAccount($data) {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateBankAccountData($data);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // التحقق من عدم تكرار رقم الحساب
            if ($this->isBankAccountNumberExists($data['account_number'])) {
                return ['success' => false, 'message' => 'رقم الحساب موجود مسبقاً'];
            }
            
            $this->db->beginTransaction();
            
            // إدراج الحساب البنكي
            $stmt = $this->db->prepare("
                INSERT INTO bank_accounts (account_name, bank_name, account_number, iban, swift_code, currency_id, initial_balance, current_balance, branch_id, is_active)
                VALUES (:account_name, :bank_name, :account_number, :iban, :swift_code, :currency_id, :initial_balance, :current_balance, :branch_id, :is_active)
            ");
            
            $initial_balance = floatval($data['initial_balance'] ?? 0);
            
            $stmt->execute([
                ':account_name' => $data['account_name'],
                ':bank_name' => $data['bank_name'],
                ':account_number' => $data['account_number'],
                ':iban' => $data['iban'] ?: null,
                ':swift_code' => $data['swift_code'] ?: null,
                ':currency_id' => $data['currency_id'],
                ':initial_balance' => $initial_balance,
                ':current_balance' => $initial_balance,
                ':branch_id' => $data['branch_id'],
                ':is_active' => isset($data['is_active']) ? 1 : 0
            ]);
            
            $bank_account_id = $this->db->lastInsertId();
            
            // تسجيل الرصيد الافتتاحي إذا كان أكبر من صفر
            if ($initial_balance > 0) {
                $this->recordBankMovement($bank_account_id, 'deposit', $initial_balance, 'الرصيد الافتتاحي', null, $_SESSION['user_id'] ?? 1);
            }
            
            // تسجيل العملية في سجل المراجعة
            $this->logActivity($_SESSION['user_id'] ?? 1, 'create_bank_account', 'bank_account', $bank_account_id, 'تم إنشاء حساب بنكي جديد: ' . $data['account_name']);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم إنشاء الحساب البنكي بنجاح', 'bank_account_id' => $bank_account_id];
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في إنشاء الحساب البنكي: ' . $e->getMessage()];
        }
    }

    /**
     * تحديث صندوق نقدي
     */
    public function updateCashBox($cash_box_id, $data) {
        try {
            // التحقق من وجود الصندوق
            $existing_cash_box = $this->getCashBoxById($cash_box_id);
            if (!$existing_cash_box) {
                return ['success' => false, 'message' => 'الصندوق النقدي غير موجود'];
            }

            // التحقق من صحة البيانات
            $validation = $this->validateCashBoxData($data, $cash_box_id);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }

            // التحقق من عدم تكرار اسم الصندوق
            if ($this->isCashBoxNameExists($data['name'], $data['branch_id'], $cash_box_id)) {
                return ['success' => false, 'message' => 'اسم الصندوق موجود مسبقاً في هذا الفرع'];
            }

            $this->db->beginTransaction();

            // تحديث البيانات
            $stmt = $this->db->prepare("
                UPDATE cash_boxes SET
                    name = :name,
                    currency_id = :currency_id,
                    branch_id = :branch_id,
                    responsible_user_id = :responsible_user_id,
                    is_active = :is_active,
                    updated_at = NOW()
                WHERE id = :cash_box_id
            ");

            $stmt->execute([
                ':name' => $data['name'],
                ':currency_id' => $data['currency_id'],
                ':branch_id' => $data['branch_id'],
                ':responsible_user_id' => $data['responsible_user_id'] ?: null,
                ':is_active' => isset($data['is_active']) ? 1 : 0,
                ':cash_box_id' => $cash_box_id
            ]);

            // تسجيل العملية في سجل المراجعة
            $this->logActivity($_SESSION['user_id'] ?? 1, 'update_cash_box', 'cash_box', $cash_box_id, 'تم تحديث الصندوق النقدي: ' . $data['name']);

            $this->db->commit();

            return ['success' => true, 'message' => 'تم تحديث الصندوق النقدي بنجاح'];

        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في تحديث الصندوق النقدي: ' . $e->getMessage()];
        }
    }

    /**
     * تحديث حساب بنكي
     */
    public function updateBankAccount($bank_account_id, $data) {
        try {
            // التحقق من وجود الحساب
            $existing_account = $this->getBankAccountById($bank_account_id);
            if (!$existing_account) {
                return ['success' => false, 'message' => 'الحساب البنكي غير موجود'];
            }

            // التحقق من صحة البيانات
            $validation = $this->validateBankAccountData($data, $bank_account_id);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }

            // التحقق من عدم تكرار رقم الحساب
            if ($this->isBankAccountNumberExists($data['account_number'], $bank_account_id)) {
                return ['success' => false, 'message' => 'رقم الحساب موجود مسبقاً'];
            }

            $this->db->beginTransaction();

            // تحديث البيانات
            $stmt = $this->db->prepare("
                UPDATE bank_accounts SET
                    account_name = :account_name,
                    bank_name = :bank_name,
                    account_number = :account_number,
                    iban = :iban,
                    swift_code = :swift_code,
                    currency_id = :currency_id,
                    branch_id = :branch_id,
                    is_active = :is_active,
                    updated_at = NOW()
                WHERE id = :bank_account_id
            ");

            $stmt->execute([
                ':account_name' => $data['account_name'],
                ':bank_name' => $data['bank_name'],
                ':account_number' => $data['account_number'],
                ':iban' => $data['iban'] ?: null,
                ':swift_code' => $data['swift_code'] ?: null,
                ':currency_id' => $data['currency_id'],
                ':branch_id' => $data['branch_id'],
                ':is_active' => isset($data['is_active']) ? 1 : 0,
                ':bank_account_id' => $bank_account_id
            ]);

            // تسجيل العملية في سجل المراجعة
            $this->logActivity($_SESSION['user_id'] ?? 1, 'update_bank_account', 'bank_account', $bank_account_id, 'تم تحديث الحساب البنكي: ' . $data['account_name']);

            $this->db->commit();

            return ['success' => true, 'message' => 'تم تحديث الحساب البنكي بنجاح'];

        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في تحديث الحساب البنكي: ' . $e->getMessage()];
        }
    }

    /**
     * تسجيل حركة نقدية (إيداع/سحب)
     */
    public function recordCashMovement($cash_box_id, $movement_type, $amount, $description, $reference_number = null, $user_id = null) {
        try {
            // التحقق من وجود الصندوق
            $cash_box = $this->getCashBoxById($cash_box_id);
            if (!$cash_box) {
                return ['success' => false, 'message' => 'الصندوق النقدي غير موجود'];
            }

            // التحقق من صحة المبلغ
            if ($amount <= 0) {
                return ['success' => false, 'message' => 'المبلغ يجب أن يكون أكبر من صفر'];
            }

            // التحقق من توفر الرصيد للسحب
            if ($movement_type == 'withdrawal' && $cash_box['current_balance'] < $amount) {
                return ['success' => false, 'message' => 'الرصيد غير كافي'];
            }

            $this->db->beginTransaction();

            // تسجيل الحركة
            $stmt = $this->db->prepare("
                INSERT INTO cash_movements (cash_box_id, movement_type, amount, description, reference_number, user_id)
                VALUES (:cash_box_id, :movement_type, :amount, :description, :reference_number, :user_id)
            ");

            $stmt->execute([
                ':cash_box_id' => $cash_box_id,
                ':movement_type' => $movement_type,
                ':amount' => $amount,
                ':description' => $description,
                ':reference_number' => $reference_number,
                ':user_id' => $user_id ?: $_SESSION['user_id'] ?? 1
            ]);

            // تحديث رصيد الصندوق
            $new_balance = $movement_type == 'deposit' ?
                $cash_box['current_balance'] + $amount :
                $cash_box['current_balance'] - $amount;

            $stmt = $this->db->prepare("
                UPDATE cash_boxes SET current_balance = :new_balance, updated_at = NOW()
                WHERE id = :cash_box_id
            ");
            $stmt->execute([
                ':new_balance' => $new_balance,
                ':cash_box_id' => $cash_box_id
            ]);

            $this->db->commit();

            return ['success' => true, 'message' => 'تم تسجيل الحركة بنجاح', 'new_balance' => $new_balance];

        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في تسجيل الحركة: ' . $e->getMessage()];
        }
    }

    /**
     * تسجيل حركة بنكية (إيداع/سحب)
     */
    public function recordBankMovement($bank_account_id, $movement_type, $amount, $description, $reference_number = null, $user_id = null) {
        try {
            // التحقق من وجود الحساب
            $bank_account = $this->getBankAccountById($bank_account_id);
            if (!$bank_account) {
                return ['success' => false, 'message' => 'الحساب البنكي غير موجود'];
            }

            // التحقق من صحة المبلغ
            if ($amount <= 0) {
                return ['success' => false, 'message' => 'المبلغ يجب أن يكون أكبر من صفر'];
            }

            // التحقق من توفر الرصيد للسحب
            if ($movement_type == 'withdrawal' && $bank_account['current_balance'] < $amount) {
                return ['success' => false, 'message' => 'الرصيد غير كافي'];
            }

            $this->db->beginTransaction();

            // تسجيل الحركة
            $stmt = $this->db->prepare("
                INSERT INTO bank_movements (bank_account_id, movement_type, amount, description, reference_number, user_id)
                VALUES (:bank_account_id, :movement_type, :amount, :description, :reference_number, :user_id)
            ");

            $stmt->execute([
                ':bank_account_id' => $bank_account_id,
                ':movement_type' => $movement_type,
                ':amount' => $amount,
                ':description' => $description,
                ':reference_number' => $reference_number,
                ':user_id' => $user_id ?: $_SESSION['user_id'] ?? 1
            ]);

            // تحديث رصيد الحساب
            $new_balance = $movement_type == 'deposit' ?
                $bank_account['current_balance'] + $amount :
                $bank_account['current_balance'] - $amount;

            $stmt = $this->db->prepare("
                UPDATE bank_accounts SET current_balance = :new_balance, updated_at = NOW()
                WHERE id = :bank_account_id
            ");
            $stmt->execute([
                ':new_balance' => $new_balance,
                ':bank_account_id' => $bank_account_id
            ]);

            $this->db->commit();

            return ['success' => true, 'message' => 'تم تسجيل الحركة بنجاح', 'new_balance' => $new_balance];

        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في تسجيل الحركة: ' . $e->getMessage()];
        }
    }

    /**
     * جلب حركات الصندوق النقدي
     */
    public function getCashMovements($cash_box_id, $limit = 50) {
        try {
            $stmt = $this->db->prepare("
                SELECT cm.*, u.full_name as user_name
                FROM cash_movements cm
                LEFT JOIN users u ON cm.user_id = u.id
                WHERE cm.cash_box_id = :cash_box_id
                ORDER BY cm.created_at DESC
                LIMIT :limit
            ");
            $stmt->bindParam(':cash_box_id', $cash_box_id, PDO::PARAM_INT);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }

    /**
     * جلب حركات الحساب البنكي
     */
    public function getBankMovements($bank_account_id, $limit = 50) {
        try {
            $stmt = $this->db->prepare("
                SELECT bm.*, u.full_name as user_name
                FROM bank_movements bm
                LEFT JOIN users u ON bm.user_id = u.id
                WHERE bm.bank_account_id = :bank_account_id
                ORDER BY bm.created_at DESC
                LIMIT :limit
            ");
            $stmt->bindParam(':bank_account_id', $bank_account_id, PDO::PARAM_INT);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            return [];
        }
    }

    /**
     * التحقق من صحة بيانات الصندوق النقدي
     */
    private function validateCashBoxData($data, $cash_box_id = null) {
        // التحقق من الحقول المطلوبة
        $required_fields = ['name', 'currency_id', 'branch_id'];

        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                return ['valid' => false, 'message' => 'جميع الحقول المطلوبة يجب ملؤها'];
            }
        }

        // التحقق من طول اسم الصندوق
        if (strlen($data['name']) < 2 || strlen($data['name']) > 100) {
            return ['valid' => false, 'message' => 'اسم الصندوق يجب أن يكون بين 2 و 100 حرف'];
        }

        // التحقق من الرصيد الافتتاحي
        if (isset($data['initial_balance']) && $data['initial_balance'] < 0) {
            return ['valid' => false, 'message' => 'الرصيد الافتتاحي لا يمكن أن يكون سالباً'];
        }

        return ['valid' => true];
    }

    /**
     * التحقق من صحة بيانات الحساب البنكي
     */
    private function validateBankAccountData($data, $bank_account_id = null) {
        // التحقق من الحقول المطلوبة
        $required_fields = ['account_name', 'bank_name', 'account_number', 'currency_id', 'branch_id'];

        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                return ['valid' => false, 'message' => 'جميع الحقول المطلوبة يجب ملؤها'];
            }
        }

        // التحقق من طول اسم الحساب
        if (strlen($data['account_name']) < 2 || strlen($data['account_name']) > 100) {
            return ['valid' => false, 'message' => 'اسم الحساب يجب أن يكون بين 2 و 100 حرف'];
        }

        // التحقق من رقم الحساب
        if (strlen($data['account_number']) < 5 || strlen($data['account_number']) > 50) {
            return ['valid' => false, 'message' => 'رقم الحساب يجب أن يكون بين 5 و 50 رقم'];
        }

        // التحقق من IBAN إذا تم إدخاله
        if (!empty($data['iban']) && strlen($data['iban']) > 50) {
            return ['valid' => false, 'message' => 'رقم IBAN طويل جداً'];
        }

        // التحقق من الرصيد الافتتاحي
        if (isset($data['initial_balance']) && $data['initial_balance'] < 0) {
            return ['valid' => false, 'message' => 'الرصيد الافتتاحي لا يمكن أن يكون سالباً'];
        }

        return ['valid' => true];
    }

    /**
     * التحقق من وجود اسم الصندوق
     */
    private function isCashBoxNameExists($name, $branch_id, $exclude_cash_box_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM cash_boxes WHERE name = :name AND branch_id = :branch_id";
            $params = [':name' => $name, ':branch_id' => $branch_id];

            if ($exclude_cash_box_id) {
                $sql .= " AND id != :cash_box_id";
                $params[':cash_box_id'] = $exclude_cash_box_id;
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * التحقق من وجود رقم الحساب البنكي
     */
    private function isBankAccountNumberExists($account_number, $exclude_bank_account_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM bank_accounts WHERE account_number = :account_number";
            $params = [':account_number' => $account_number];

            if ($exclude_bank_account_id) {
                $sql .= " AND id != :bank_account_id";
                $params[':bank_account_id'] = $exclude_bank_account_id;
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * إنشاء الجداول المطلوبة
     */
    private function createTables() {
        try {
            // جدول الصناديق النقدية (موجود مسبقاً في schema)
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS cash_boxes (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    name VARCHAR(100) NOT NULL,
                    currency_id INT NOT NULL,
                    initial_balance DECIMAL(15,2) DEFAULT 0,
                    current_balance DECIMAL(15,2) DEFAULT 0,
                    branch_id INT NOT NULL,
                    responsible_user_id INT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (currency_id) REFERENCES currencies(id),
                    FOREIGN KEY (branch_id) REFERENCES branches(id),
                    FOREIGN KEY (responsible_user_id) REFERENCES users(id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            // جدول الحسابات البنكية (موجود مسبقاً في schema)
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS bank_accounts (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    account_name VARCHAR(100) NOT NULL,
                    bank_name VARCHAR(100) NOT NULL,
                    account_number VARCHAR(50) NOT NULL,
                    iban VARCHAR(50),
                    swift_code VARCHAR(20),
                    currency_id INT NOT NULL,
                    initial_balance DECIMAL(15,2) DEFAULT 0,
                    current_balance DECIMAL(15,2) DEFAULT 0,
                    branch_id INT NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (currency_id) REFERENCES currencies(id),
                    FOREIGN KEY (branch_id) REFERENCES branches(id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            // جدول حركات الصناديق النقدية
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS cash_movements (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    cash_box_id INT NOT NULL,
                    movement_type ENUM('deposit', 'withdrawal') NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    description TEXT,
                    reference_number VARCHAR(50),
                    user_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            // جدول حركات الحسابات البنكية
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS bank_movements (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    bank_account_id INT NOT NULL,
                    movement_type ENUM('deposit', 'withdrawal') NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    description TEXT,
                    reference_number VARCHAR(50),
                    user_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

        } catch (PDOException $e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * تسجيل النشاط في سجل المراجعة
     */
    private function logActivity($user_id, $action, $table_name, $record_id, $description = '') {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO audit_logs (user_id, action, table_name, record_id, description, ip_address, user_agent)
                VALUES (:user_id, :action, :table_name, :record_id, :description, :ip_address, :user_agent)
            ");

            $stmt->execute([
                ':user_id' => $user_id,
                ':action' => $action,
                ':table_name' => $table_name,
                ':record_id' => $record_id,
                ':description' => $description,
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (PDOException $e) {
            // تجاهل أخطاء تسجيل المراجعة
        }
    }
}
?>
