<?php
/**
 * فئة إدارة التقارير المالية والمحاسبية - Trust Plus System
 */

require_once 'auth.php';

class ReportsManager {
    private $db;
    private $auth;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->auth = new Auth();
    }
    
    /**
     * إنشاء تقرير الأرباح والخسائر
     */
    public function generateProfitLossReport($date_from, $date_to, $branch_id = null) {
        try {
            $where_conditions = ['t.transaction_date BETWEEN :date_from AND :date_to'];
            $params = [':date_from' => $date_from, ':date_to' => $date_to];
            
            if ($branch_id) {
                $where_conditions[] = 't.branch_id = :branch_id';
                $params[':branch_id'] = $branch_id;
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            // الإيرادات من الصرافة
            $stmt = $this->db->prepare("
                SELECT 
                    SUM(dep.total_commission) as exchange_commission,
                    SUM(dep.total_spread_profit) as exchange_spread,
                    SUM(dep.total_profit) as total_exchange_profit
                FROM daily_exchange_profits dep
                WHERE dep.profit_date BETWEEN :date_from AND :date_to
                " . ($branch_id ? "AND dep.branch_id = :branch_id" : "")
            );
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            $exchange_revenue = $stmt->fetch() ?: ['exchange_commission' => 0, 'exchange_spread' => 0, 'total_exchange_profit' => 0];
            
            // الإيرادات من التحويلات
            $stmt = $this->db->prepare("
                SELECT 
                    SUM(dtp.total_fees) as transfer_fees,
                    SUM(dtp.total_exchange_profit) as transfer_exchange_profit,
                    SUM(dtp.total_profit) as total_transfer_profit
                FROM daily_transfer_profits dtp
                WHERE dtp.profit_date BETWEEN :date_from AND :date_to
                " . ($branch_id ? "AND dtp.branch_id = :branch_id" : "")
            );
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            $transfer_revenue = $stmt->fetch() ?: ['transfer_fees' => 0, 'transfer_exchange_profit' => 0, 'total_transfer_profit' => 0];
            
            // المصروفات من القيود المحاسبية
            $stmt = $this->db->prepare("
                SELECT 
                    a.account_type,
                    SUM(td.debit) as total_debits,
                    SUM(td.credit) as total_credits
                FROM transaction_details td
                JOIN transactions t ON td.transaction_id = t.id
                JOIN accounts a ON td.account_id = a.id
                $where_clause
                GROUP BY a.account_type
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            $account_totals = $stmt->fetchAll();
            
            // تنظيم البيانات
            $revenue = [
                'exchange_commission' => floatval($exchange_revenue['exchange_commission']),
                'exchange_spread' => floatval($exchange_revenue['exchange_spread']),
                'transfer_fees' => floatval($transfer_revenue['transfer_fees']),
                'transfer_exchange_profit' => floatval($transfer_revenue['transfer_exchange_profit']),
                'other_revenue' => 0
            ];
            
            $expenses = [
                'operational_expenses' => 0,
                'administrative_expenses' => 0,
                'other_expenses' => 0
            ];
            
            foreach ($account_totals as $account) {
                if ($account['account_type'] == 'expenses') {
                    $expenses['operational_expenses'] += floatval($account['total_debits']);
                }
            }
            
            $total_revenue = array_sum($revenue);
            $total_expenses = array_sum($expenses);
            $gross_profit = $total_revenue - $total_expenses;
            $net_profit = $gross_profit; // يمكن تعديلها لاحقاً لتشمل الضرائب
            
            return [
                'success' => true,
                'data' => [
                    'period' => ['from' => $date_from, 'to' => $date_to],
                    'revenue' => $revenue,
                    'total_revenue' => $total_revenue,
                    'expenses' => $expenses,
                    'total_expenses' => $total_expenses,
                    'gross_profit' => $gross_profit,
                    'net_profit' => $net_profit,
                    'profit_margin' => $total_revenue > 0 ? ($net_profit / $total_revenue) * 100 : 0
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في إنشاء تقرير الأرباح والخسائر: ' . $e->getMessage()];
        }
    }
    
    /**
     * إنشاء الميزانية العمومية
     */
    public function generateBalanceSheet($as_of_date, $branch_id = null) {
        try {
            $where_conditions = ['t.transaction_date <= :as_of_date'];
            $params = [':as_of_date' => $as_of_date];
            
            if ($branch_id) {
                $where_conditions[] = 't.branch_id = :branch_id';
                $params[':branch_id'] = $branch_id;
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            // الأصول والخصوم وحقوق الملكية
            $stmt = $this->db->prepare("
                SELECT 
                    a.account_type,
                    a.account_name,
                    a.account_code,
                    SUM(td.debit - td.credit) as balance
                FROM accounts a
                LEFT JOIN transaction_details td ON a.id = td.account_id
                LEFT JOIN transactions t ON td.transaction_id = t.id
                $where_clause
                GROUP BY a.id, a.account_type, a.account_name, a.account_code
                HAVING balance != 0 OR a.account_type IN ('assets', 'liabilities', 'equity')
                ORDER BY a.account_type, a.account_code
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            $accounts = $stmt->fetchAll();
            
            $balance_sheet = [
                'assets' => [],
                'liabilities' => [],
                'equity' => []
            ];
            
            $totals = [
                'assets' => 0,
                'liabilities' => 0,
                'equity' => 0
            ];
            
            foreach ($accounts as $account) {
                $balance = floatval($account['balance']);
                
                if ($account['account_type'] == 'assets') {
                    $balance_sheet['assets'][] = [
                        'account_code' => $account['account_code'],
                        'account_name' => $account['account_name'],
                        'balance' => $balance
                    ];
                    $totals['assets'] += $balance;
                } elseif ($account['account_type'] == 'liabilities') {
                    $balance = -$balance; // الخصوم تظهر بالسالب في القيود
                    $balance_sheet['liabilities'][] = [
                        'account_code' => $account['account_code'],
                        'account_name' => $account['account_name'],
                        'balance' => $balance
                    ];
                    $totals['liabilities'] += $balance;
                } elseif ($account['account_type'] == 'equity') {
                    $balance = -$balance; // حقوق الملكية تظهر بالسالب في القيود
                    $balance_sheet['equity'][] = [
                        'account_code' => $account['account_code'],
                        'account_name' => $account['account_name'],
                        'balance' => $balance
                    ];
                    $totals['equity'] += $balance;
                }
            }
            
            return [
                'success' => true,
                'data' => [
                    'as_of_date' => $as_of_date,
                    'assets' => $balance_sheet['assets'],
                    'liabilities' => $balance_sheet['liabilities'],
                    'equity' => $balance_sheet['equity'],
                    'totals' => $totals,
                    'total_liabilities_equity' => $totals['liabilities'] + $totals['equity'],
                    'balanced' => abs($totals['assets'] - ($totals['liabilities'] + $totals['equity'])) < 0.01
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في إنشاء الميزانية العمومية: ' . $e->getMessage()];
        }
    }
    
    /**
     * إنشاء تقرير التدفق النقدي
     */
    public function generateCashFlowReport($date_from, $date_to, $branch_id = null) {
        try {
            $where_conditions = ['t.transaction_date BETWEEN :date_from AND :date_to'];
            $params = [':date_from' => $date_from, ':date_to' => $date_to];
            
            if ($branch_id) {
                $where_conditions[] = 't.branch_id = :branch_id';
                $params[':branch_id'] = $branch_id;
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            // التدفقات النقدية من الأنشطة التشغيلية
            $stmt = $this->db->prepare("
                SELECT 
                    t.transaction_type,
                    SUM(CASE WHEN td.debit > 0 THEN td.debit ELSE 0 END) as cash_inflow,
                    SUM(CASE WHEN td.credit > 0 THEN td.credit ELSE 0 END) as cash_outflow
                FROM transactions t
                JOIN transaction_details td ON t.id = td.transaction_id
                JOIN accounts a ON td.account_id = a.id
                $where_clause AND a.account_type = 'assets' AND a.account_code LIKE '1001%'
                GROUP BY t.transaction_type
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            $cash_flows = $stmt->fetchAll();
            
            $operating_activities = [
                'exchange_operations' => 0,
                'transfer_operations' => 0,
                'other_operations' => 0
            ];
            
            foreach ($cash_flows as $flow) {
                $net_flow = floatval($flow['cash_inflow']) - floatval($flow['cash_outflow']);
                
                switch ($flow['transaction_type']) {
                    case 'exchange':
                        $operating_activities['exchange_operations'] = $net_flow;
                        break;
                    case 'transfer':
                        $operating_activities['transfer_operations'] = $net_flow;
                        break;
                    default:
                        $operating_activities['other_operations'] += $net_flow;
                        break;
                }
            }
            
            $net_operating_cash_flow = array_sum($operating_activities);
            
            return [
                'success' => true,
                'data' => [
                    'period' => ['from' => $date_from, 'to' => $date_to],
                    'operating_activities' => $operating_activities,
                    'investing_activities' => [], // يمكن تطويرها لاحقاً
                    'financing_activities' => [], // يمكن تطويرها لاحقاً
                    'net_operating_cash_flow' => $net_operating_cash_flow,
                    'net_investing_cash_flow' => 0,
                    'net_financing_cash_flow' => 0,
                    'net_cash_flow' => $net_operating_cash_flow
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في إنشاء تقرير التدفق النقدي: ' . $e->getMessage()];
        }
    }
    
    /**
     * إنشاء ميزان المراجعة
     */
    public function generateTrialBalance($as_of_date, $branch_id = null) {
        try {
            $where_conditions = ['t.transaction_date <= :as_of_date'];
            $params = [':as_of_date' => $as_of_date];
            
            if ($branch_id) {
                $where_conditions[] = 't.branch_id = :branch_id';
                $params[':branch_id'] = $branch_id;
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            $stmt = $this->db->prepare("
                SELECT 
                    a.account_code,
                    a.account_name,
                    a.account_type,
                    SUM(td.debit) as total_debits,
                    SUM(td.credit) as total_credits,
                    SUM(td.debit - td.credit) as balance
                FROM accounts a
                LEFT JOIN transaction_details td ON a.id = td.account_id
                LEFT JOIN transactions t ON td.transaction_id = t.id
                $where_clause
                GROUP BY a.id, a.account_code, a.account_name, a.account_type
                HAVING total_debits > 0 OR total_credits > 0 OR a.account_type IN ('assets', 'liabilities', 'equity')
                ORDER BY a.account_code
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            $accounts = $stmt->fetchAll();
            
            $trial_balance = [];
            $totals = [
                'total_debits' => 0,
                'total_credits' => 0,
                'debit_balances' => 0,
                'credit_balances' => 0
            ];
            
            foreach ($accounts as $account) {
                $debit = floatval($account['total_debits']);
                $credit = floatval($account['total_credits']);
                $balance = floatval($account['balance']);
                
                $trial_balance[] = [
                    'account_code' => $account['account_code'],
                    'account_name' => $account['account_name'],
                    'account_type' => $account['account_type'],
                    'total_debits' => $debit,
                    'total_credits' => $credit,
                    'debit_balance' => $balance > 0 ? $balance : 0,
                    'credit_balance' => $balance < 0 ? -$balance : 0
                ];
                
                $totals['total_debits'] += $debit;
                $totals['total_credits'] += $credit;
                $totals['debit_balances'] += $balance > 0 ? $balance : 0;
                $totals['credit_balances'] += $balance < 0 ? -$balance : 0;
            }
            
            return [
                'success' => true,
                'data' => [
                    'as_of_date' => $as_of_date,
                    'accounts' => $trial_balance,
                    'totals' => $totals,
                    'balanced' => abs($totals['total_debits'] - $totals['total_credits']) < 0.01 &&
                                 abs($totals['debit_balances'] - $totals['credit_balances']) < 0.01
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في إنشاء ميزان المراجعة: ' . $e->getMessage()];
        }
    }
    
    /**
     * إنشاء تقرير ملخص العمليات
     */
    public function generateOperationsSummary($date_from, $date_to, $branch_id = null) {
        try {
            $where_conditions = ['DATE(t.transaction_date) BETWEEN :date_from AND :date_to'];
            $params = [':date_from' => $date_from, ':date_to' => $date_to];
            
            if ($branch_id) {
                $where_conditions[] = 't.branch_id = :branch_id';
                $params[':branch_id'] = $branch_id;
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            // ملخص المعاملات
            $stmt = $this->db->prepare("
                SELECT 
                    t.transaction_type,
                    COUNT(*) as transaction_count,
                    SUM(t.total_amount_base_currency) as total_amount,
                    AVG(t.total_amount_base_currency) as average_amount
                FROM transactions t
                $where_clause
                GROUP BY t.transaction_type
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            $transaction_summary = $stmt->fetchAll();
            
            // ملخص العملاء
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(DISTINCT t.customer_id) as unique_customers,
                    COUNT(*) as total_transactions
                FROM transactions t
                $where_clause AND t.customer_id IS NOT NULL
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();
            $customer_summary = $stmt->fetch();
            
            return [
                'success' => true,
                'data' => [
                    'period' => ['from' => $date_from, 'to' => $date_to],
                    'transaction_summary' => $transaction_summary,
                    'customer_summary' => $customer_summary,
                    'generated_at' => date('Y-m-d H:i:s')
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في إنشاء تقرير ملخص العمليات: ' . $e->getMessage()];
        }
    }
    
    /**
     * حفظ تقرير في قاعدة البيانات
     */
    public function saveReport($report_name, $report_type, $report_data, $user_id, $file_path = null) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO saved_reports (
                    report_name, report_type, report_parameters, 
                    generated_by_user_id, file_path, status
                ) VALUES (
                    :report_name, :report_type, :report_parameters,
                    :user_id, :file_path, 'completed'
                )
            ");
            
            $stmt->bindParam(':report_name', $report_name);
            $stmt->bindParam(':report_type', $report_type);
            $stmt->bindParam(':report_parameters', json_encode($report_data));
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':file_path', $file_path);
            $stmt->execute();
            
            return [
                'success' => true,
                'report_id' => $this->db->lastInsertId(),
                'message' => 'تم حفظ التقرير بنجاح'
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في حفظ التقرير: ' . $e->getMessage()];
        }
    }
    
    /**
     * الحصول على قائمة التقارير المحفوظة
     */
    public function getSavedReports($user_id = null, $report_type = null, $limit = 50) {
        try {
            $where_conditions = ['1=1'];
            $params = [];
            
            if ($user_id) {
                $where_conditions[] = 'generated_by_user_id = :user_id';
                $params[':user_id'] = $user_id;
            }
            
            if ($report_type) {
                $where_conditions[] = 'report_type = :report_type';
                $params[':report_type'] = $report_type;
            }
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            $stmt = $this->db->prepare("
                SELECT sr.*, u.full_name as generated_by_name
                FROM saved_reports sr
                JOIN users u ON sr.generated_by_user_id = u.id
                $where_clause
                ORDER BY sr.generated_at DESC
                LIMIT :limit
            ");
            
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
}
?>
