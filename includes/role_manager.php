<?php
/**
 * فئة إدارة الأدوار - Trust Plus System
 */

require_once __DIR__ . '/../config.php';

class RoleManager {
    private $db;
    
    public function __construct() {
        try {
            // الاتصال بقاعدة البيانات
            $this->db = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
            
            // إنشاء الجداول إذا لم تكن موجودة
            $this->createTables();
            
        } catch (PDOException $e) {
            throw new Exception('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب جميع الأدوار مع عدد المستخدمين
     */
    public function getAllRoles() {
        try {
            $stmt = $this->db->prepare("
                SELECT r.*, 
                       COUNT(u.id) as users_count
                FROM roles r
                LEFT JOIN users u ON r.id = u.role_id
                GROUP BY r.id
                ORDER BY r.id ASC
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception('خطأ في جلب الأدوار: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب دور بواسطة المعرف
     */
    public function getRoleById($role_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT r.*, 
                       COUNT(u.id) as users_count
                FROM roles r
                LEFT JOIN users u ON r.id = u.role_id
                WHERE r.id = :role_id
                GROUP BY r.id
            ");
            $stmt->bindParam(':role_id', $role_id, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception('خطأ في جلب بيانات الدور: ' . $e->getMessage());
        }
    }
    
    /**
     * جلب جميع الصلاحيات
     */
    public function getAllPermissions() {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM permissions 
                ORDER BY module, permission_name
            ");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            // إنشاء صلاحيات افتراضية إذا لم توجد
            $this->createDefaultPermissions();
            return $this->getAllPermissions();
        }
    }
    
    /**
     * جلب صلاحيات دور معين
     */
    public function getRolePermissions($role_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT permission_id 
                FROM role_permissions 
                WHERE role_id = :role_id
            ");
            $stmt->bindParam(':role_id', $role_id, PDO::PARAM_INT);
            $stmt->execute();
            return array_column($stmt->fetchAll(), 'permission_id');
        } catch (PDOException $e) {
            return [];
        }
    }
    
    /**
     * إنشاء دور جديد
     */
    public function createRole($data) {
        try {
            // التحقق من صحة البيانات
            $validation = $this->validateRoleData($data);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // التحقق من عدم تكرار اسم الدور
            if ($this->isRoleNameExists($data['role_name'])) {
                return ['success' => false, 'message' => 'اسم الدور موجود مسبقاً'];
            }
            
            $this->db->beginTransaction();
            
            // إدراج الدور
            $stmt = $this->db->prepare("
                INSERT INTO roles (role_name, description, is_active)
                VALUES (:role_name, :description, :is_active)
            ");
            
            $stmt->bindParam(':role_name', $data['role_name']);
            $stmt->bindParam(':description', $data['description'] ?? '');
            $stmt->bindParam(':is_active', $data['is_active'] ?? 1, PDO::PARAM_INT);
            
            $stmt->execute();
            $role_id = $this->db->lastInsertId();
            
            // تسجيل العملية في سجل المراجعة
            $this->logActivity($_SESSION['user_id'] ?? 1, 'create_role', 'role', $role_id, 'تم إنشاء دور جديد: ' . $data['role_name']);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم إنشاء الدور بنجاح', 'role_id' => $role_id];
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في إنشاء الدور: ' . $e->getMessage()];
        }
    }
    
    /**
     * تحديث بيانات الدور
     */
    public function updateRole($role_id, $data) {
        try {
            // التحقق من وجود الدور
            $existing_role = $this->getRoleById($role_id);
            if (!$existing_role) {
                return ['success' => false, 'message' => 'الدور غير موجود'];
            }
            
            // منع تعديل دور المدير
            if ($role_id == 1 && $data['role_name'] != $existing_role['role_name']) {
                return ['success' => false, 'message' => 'لا يمكن تعديل اسم دور المدير'];
            }
            
            // التحقق من صحة البيانات
            $validation = $this->validateRoleData($data, $role_id);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // التحقق من عدم تكرار اسم الدور
            if ($this->isRoleNameExists($data['role_name'], $role_id)) {
                return ['success' => false, 'message' => 'اسم الدور موجود مسبقاً'];
            }
            
            $this->db->beginTransaction();
            
            // تحديث البيانات
            $stmt = $this->db->prepare("
                UPDATE roles SET 
                    role_name = :role_name,
                    description = :description,
                    is_active = :is_active,
                    updated_at = NOW()
                WHERE id = :role_id
            ");
            
            $stmt->bindParam(':role_name', $data['role_name']);
            $stmt->bindParam(':description', $data['description'] ?? '');
            $stmt->bindParam(':is_active', $role_id == 1 ? 1 : ($data['is_active'] ?? 1), PDO::PARAM_INT);
            $stmt->bindParam(':role_id', $role_id, PDO::PARAM_INT);
            
            $stmt->execute();
            
            // تسجيل العملية في سجل المراجعة
            $this->logActivity($_SESSION['user_id'] ?? 1, 'update_role', 'role', $role_id, 'تم تحديث الدور: ' . $data['role_name']);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم تحديث الدور بنجاح'];
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في تحديث الدور: ' . $e->getMessage()];
        }
    }
    
    /**
     * تغيير حالة الدور (تفعيل/إلغاء تفعيل)
     */
    public function toggleRoleStatus($role_id) {
        try {
            // منع تعديل دور المدير
            if ($role_id == 1) {
                return ['success' => false, 'message' => 'لا يمكن إلغاء تفعيل دور المدير'];
            }
            
            $role = $this->getRoleById($role_id);
            if (!$role) {
                return ['success' => false, 'message' => 'الدور غير موجود'];
            }
            
            $new_status = $role['is_active'] ? 0 : 1;
            
            $stmt = $this->db->prepare("
                UPDATE roles SET is_active = :status, updated_at = NOW() 
                WHERE id = :role_id
            ");
            $stmt->bindParam(':status', $new_status, PDO::PARAM_INT);
            $stmt->bindParam(':role_id', $role_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $action = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
            $this->logActivity($_SESSION['user_id'] ?? 1, 'toggle_role_status', 'role', $role_id, $action . ' الدور: ' . $role['role_name']);
            
            return ['success' => true, 'message' => 'تم ' . $action . ' الدور بنجاح'];
            
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'خطأ في تغيير حالة الدور: ' . $e->getMessage()];
        }
    }
    
    /**
     * تحديث صلاحيات الدور
     */
    public function updateRolePermissions($role_id, $permissions) {
        try {
            // منع تعديل صلاحيات دور المدير
            if ($role_id == 1) {
                return ['success' => false, 'message' => 'دور المدير يحصل على جميع الصلاحيات تلقائياً'];
            }
            
            $role = $this->getRoleById($role_id);
            if (!$role) {
                return ['success' => false, 'message' => 'الدور غير موجود'];
            }
            
            $this->db->beginTransaction();
            
            // حذف الصلاحيات الحالية
            $stmt = $this->db->prepare("DELETE FROM role_permissions WHERE role_id = :role_id");
            $stmt->bindParam(':role_id', $role_id, PDO::PARAM_INT);
            $stmt->execute();
            
            // إضافة الصلاحيات الجديدة
            if (!empty($permissions)) {
                $stmt = $this->db->prepare("
                    INSERT INTO role_permissions (role_id, permission_id) 
                    VALUES (:role_id, :permission_id)
                ");
                
                foreach ($permissions as $permission_id) {
                    $stmt->bindParam(':role_id', $role_id, PDO::PARAM_INT);
                    $stmt->bindParam(':permission_id', $permission_id, PDO::PARAM_INT);
                    $stmt->execute();
                }
            }
            
            // تسجيل العملية في سجل المراجعة
            $this->logActivity($_SESSION['user_id'] ?? 1, 'update_role_permissions', 'role', $role_id, 'تم تحديث صلاحيات الدور: ' . $role['role_name']);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم تحديث صلاحيات الدور بنجاح'];
            
        } catch (PDOException $e) {
            $this->db->rollBack();
            return ['success' => false, 'message' => 'خطأ في تحديث صلاحيات الدور: ' . $e->getMessage()];
        }
    }

    /**
     * التحقق من صحة بيانات الدور
     */
    private function validateRoleData($data, $role_id = null) {
        // التحقق من الحقول المطلوبة
        if (empty($data['role_name'])) {
            return ['valid' => false, 'message' => 'اسم الدور مطلوب'];
        }

        // التحقق من طول اسم الدور
        if (strlen($data['role_name']) < 2 || strlen($data['role_name']) > 50) {
            return ['valid' => false, 'message' => 'اسم الدور يجب أن يكون بين 2 و 50 حرف'];
        }

        return ['valid' => true];
    }

    /**
     * التحقق من وجود اسم الدور
     */
    private function isRoleNameExists($role_name, $exclude_role_id = null) {
        try {
            $sql = "SELECT COUNT(*) FROM roles WHERE role_name = :role_name";
            $params = [':role_name' => $role_name];

            if ($exclude_role_id) {
                $sql .= " AND id != :role_id";
                $params[':role_id'] = $exclude_role_id;
            }

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * إنشاء الجداول المطلوبة
     */
    private function createTables() {
        try {
            // جدول الأدوار
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS roles (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    role_name VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            // جدول الصلاحيات
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS permissions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    permission_name VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT,
                    module VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            // جدول ربط الأدوار بالصلاحيات
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS role_permissions (
                    role_id INT,
                    permission_id INT,
                    PRIMARY KEY (role_id, permission_id),
                    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
                    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            // إنشاء دور المدير الافتراضي
            $this->createDefaultRole();

        } catch (PDOException $e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * إنشاء دور المدير الافتراضي
     */
    private function createDefaultRole() {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM roles WHERE id = 1");
            $stmt->execute();

            if ($stmt->fetchColumn() == 0) {
                $stmt = $this->db->prepare("
                    INSERT INTO roles (id, role_name, description, is_active)
                    VALUES (1, 'مدير النظام', 'صلاحيات كاملة للنظام', 1)
                ");
                $stmt->execute();
            }
        } catch (PDOException $e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * إنشاء الصلاحيات الافتراضية
     */
    private function createDefaultPermissions() {
        try {
            $permissions = [
                // إدارة المستخدمين
                ['users.view', 'عرض المستخدمين', 'users'],
                ['users.create', 'إضافة مستخدمين', 'users'],
                ['users.edit', 'تعديل المستخدمين', 'users'],
                ['users.delete', 'حذف المستخدمين', 'users'],

                // إدارة الأدوار
                ['roles.view', 'عرض الأدوار', 'roles'],
                ['roles.create', 'إضافة أدوار', 'roles'],
                ['roles.edit', 'تعديل الأدوار', 'roles'],
                ['roles.delete', 'حذف الأدوار', 'roles'],

                // إدارة الصلاحيات
                ['permissions.view', 'عرض الصلاحيات', 'permissions'],
                ['permissions.create', 'إضافة صلاحيات', 'permissions'],
                ['permissions.edit', 'تعديل الصلاحيات', 'permissions'],
                ['permissions.delete', 'حذف الصلاحيات', 'permissions'],

                // إدارة العملاء
                ['customers.view', 'عرض العملاء', 'customers'],
                ['customers.create', 'إضافة عملاء', 'customers'],
                ['customers.edit', 'تعديل العملاء', 'customers'],
                ['customers.delete', 'حذف العملاء', 'customers'],

                // عمليات الصرافة
                ['exchange.view', 'عرض عمليات الصرافة', 'exchange'],
                ['exchange.create', 'إنشاء عمليات صرافة', 'exchange'],
                ['exchange.rates', 'إدارة أسعار الصرف', 'exchange'],

                // التحويلات المالية
                ['transfers.view', 'عرض التحويلات', 'transfers'],
                ['transfers.create', 'إنشاء تحويلات', 'transfers'],
                ['transfers.approve', 'اعتماد التحويلات', 'transfers'],

                // المعاملات المالية
                ['transactions.view', 'عرض المعاملات', 'transactions'],
                ['transactions.edit', 'تعديل المعاملات', 'transactions'],

                // المحاسبة
                ['accounting.view', 'عرض المحاسبة', 'accounting'],
                ['accounting.edit', 'تعديل المحاسبة', 'accounting'],

                // التقارير
                ['reports.financial', 'التقارير المالية', 'reports'],
                ['reports.compliance', 'تقارير الامتثال', 'reports'],

                // الإعدادات
                ['settings.view', 'عرض الإعدادات', 'settings'],
                ['settings.edit', 'تعديل الإعدادات', 'settings'],

                // سجل المراجعة
                ['audit.view', 'عرض سجل المراجعة', 'audit'],

                // النسخ الاحتياطي
                ['backup.create', 'إنشاء نسخ احتياطية', 'backup'],
                ['backup.restore', 'استعادة النسخ الاحتياطية', 'backup'],
            ];

            $stmt = $this->db->prepare("
                INSERT IGNORE INTO permissions (permission_name, description, module)
                VALUES (?, ?, ?)
            ");

            foreach ($permissions as $permission) {
                $stmt->execute($permission);
            }

        } catch (PDOException $e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * تسجيل النشاط في سجل المراجعة
     */
    private function logActivity($user_id, $action, $table_name, $record_id, $description = '') {
        try {
            // إنشاء جدول سجل المراجعة إذا لم يكن موجوداً
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    action VARCHAR(50) NOT NULL,
                    table_name VARCHAR(50) NOT NULL,
                    record_id INT NOT NULL,
                    description TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");

            $stmt = $this->db->prepare("
                INSERT INTO audit_logs (user_id, action, table_name, record_id, description, ip_address, user_agent)
                VALUES (:user_id, :action, :table_name, :record_id, :description, :ip_address, :user_agent)
            ");

            $stmt->execute([
                ':user_id' => $user_id,
                ':action' => $action,
                ':table_name' => $table_name,
                ':record_id' => $record_id,
                ':description' => $description,
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (PDOException $e) {
            // تجاهل أخطاء تسجيل المراجعة
        }
    }
}
?>
