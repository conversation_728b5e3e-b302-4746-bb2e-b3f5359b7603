<?php
/**
 * Trust Plus - Dashboard Test
 * اختبار لوحة التحكم
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 اختبار لوحة التحكم</h1>";
echo "<hr>";

try {
    echo "<h2>📋 فحص الملفات المطلوبة...</h2>";
    
    $required_files = [
        'config.php' => 'ملف الإعدادات',
        'includes/database.php' => 'ملف قاعدة البيانات',
        'includes/auth.php' => 'ملف المصادقة',
        'includes/assets.php' => 'ملف إدارة الأصول',
        'includes/header.php' => 'ملف الرأس',
        'includes/footer.php' => 'ملف التذييل',
        'includes/sidebar.php' => 'ملف الشريط الجانبي',
        'includes/navbar.php' => 'ملف شريط التنقل',
        'dashboard/index.php' => 'ملف لوحة التحكم'
    ];
    
    $missing_files = [];
    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            echo "✅ $description: <code>$file</code><br>";
        } else {
            echo "❌ $description: <code>$file</code> - <strong>مفقود</strong><br>";
            $missing_files[] = $file;
        }
    }
    
    if (!empty($missing_files)) {
        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h3>❌ ملفات مفقودة</h3>";
        echo "<p>يجب إنشاء الملفات التالية:</p>";
        echo "<ul>";
        foreach ($missing_files as $file) {
            echo "<li><code>$file</code></li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h3>✅ جميع الملفات موجودة</h3>";
        echo "</div>";
    }
    
    echo "<hr>";
    echo "<h2>🔧 اختبار تحميل الملفات...</h2>";
    
    // اختبار config.php
    if (file_exists('config.php')) {
        require_once 'config.php';
        echo "✅ تم تحميل config.php<br>";
        echo "📝 اسم النظام: " . (defined('SYSTEM_NAME') ? SYSTEM_NAME : 'غير محدد') . "<br>";
    }
    
    // اختبار database.php
    if (file_exists('includes/database.php')) {
        require_once 'includes/database.php';
        echo "✅ تم تحميل database.php<br>";
        
        $database = new Database();
        $db = $database->getConnection();
        if ($db) {
            echo "✅ اتصال قاعدة البيانات يعمل<br>";
        } else {
            echo "❌ فشل اتصال قاعدة البيانات<br>";
        }
    }
    
    // اختبار auth.php
    if (file_exists('includes/auth.php')) {
        require_once 'includes/auth.php';
        echo "✅ تم تحميل auth.php<br>";
        
        $auth = new Auth();
        echo "✅ تم إنشاء كائن Auth<br>";
    }
    
    // اختبار assets.php
    if (file_exists('includes/assets.php')) {
        require_once 'includes/assets.php';
        echo "✅ تم تحميل assets.php<br>";
        
        Assets::loadDashboard();
        echo "✅ تم تحميل أصول لوحة التحكم<br>";
    }
    
    echo "<hr>";
    echo "<h2>🚀 اختبار تسجيل الدخول ولوحة التحكم...</h2>";
    
    // محاولة تسجيل دخول تجريبي
    if (isset($auth)) {
        $login_result = $auth->login('admin', 'admin123');
        
        if ($login_result['success']) {
            echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h3>🎉 تسجيل الدخول نجح!</h3>";
            echo "<p>يمكنك الآن الوصول للوحة التحكم</p>";
            echo "</div>";
            
            // اختبار الوصول للوحة التحكم
            echo "<h3>🔗 اختبار الوصول للوحة التحكم...</h3>";
            
            // محاكاة تحميل لوحة التحكم
            ob_start();
            
            // تعيين المتغيرات المطلوبة
            $current_user = $auth->getCurrentUser();
            $page_type = 'dashboard';
            $page_title = 'لوحة التحكم - ' . SYSTEM_NAME;
            $page_header = 'لوحة التحكم';
            $page_subtitle = 'مرحباً بك في نظام Trust Plus';
            $page_icon = 'fas fa-tachometer-alt';
            $show_breadcrumb = false;
            
            // محاولة تحميل header
            try {
                include 'includes/header.php';
                $header_content = ob_get_contents();
                ob_clean();
                
                echo "✅ تم تحميل header.php بنجاح<br>";
                
                // محاولة تحميل محتوى لوحة التحكم
                echo "<h4>📊 محتوى لوحة التحكم:</h4>";
                echo "<ul>";
                echo "<li>👤 المستخدم الحالي: " . htmlspecialchars($current_user['full_name']) . "</li>";
                echo "<li>📧 البريد الإلكتروني: " . htmlspecialchars($current_user['email']) . "</li>";
                echo "<li>🔑 الدور: " . $current_user['role_id'] . "</li>";
                echo "</ul>";
                
                echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>🎯 لوحة التحكم جاهزة!</h4>";
                echo "<p><a href='dashboard/index.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 افتح لوحة التحكم</a></p>";
                echo "</div>";
                
            } catch (Exception $e) {
                ob_clean();
                echo "❌ خطأ في تحميل header.php: " . $e->getMessage() . "<br>";
            }
            
            ob_end_clean();
            
            // تسجيل الخروج
            $auth->logout();
            echo "<p>✅ تم تسجيل الخروج</p>";
            
        } else {
            echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h3>❌ فشل تسجيل الدخول</h3>";
            echo "<p>الخطأ: " . htmlspecialchars($login_result['message']) . "</p>";
            echo "<p>يجب إصلاح مشكلة تسجيل الدخول أولاً</p>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في النظام</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='quick_login_test.php' target='_blank'>🧪 اختبار تسجيل الدخول</a></li>";
echo "<li><a href='auth/login.php' target='_blank'>🔑 صفحة تسجيل الدخول</a></li>";
echo "<li><a href='dashboard/index.php' target='_blank'>📊 لوحة التحكم</a></li>";
echo "<li><a href='check_password.php' target='_blank'>🔍 فحص كلمة المرور</a></li>";
echo "</ul>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
