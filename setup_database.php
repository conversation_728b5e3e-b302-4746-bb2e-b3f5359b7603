<?php
/**
 * Trust Plus - Database Setup Script
 * سكريبت إعداد قاعدة البيانات
 */

// تضمين ملف التكوين
if (file_exists('config.php')) {
    require_once 'config.php';
}

// تضمين ملف قاعدة البيانات
require_once 'includes/database.php';

echo "<h1>🔧 إعداد قاعدة البيانات - Trust Plus</h1>";
echo "<hr>";

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ فشل الاتصال بقاعدة البيانات</h3>";
        echo "<p>تأكد من:</p>";
        echo "<ul>";
        echo "<li>تشغيل خادم MySQL</li>";
        echo "<li>صحة إعدادات قاعدة البيانات في config.php</li>";
        echo "<li>وجود قاعدة البيانات المحددة</li>";
        echo "</ul>";
        echo "</div>";
        exit();
    }
    
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>";
    echo "</div>";
    
    // إنشاء الجداول الأساسية
    echo "<h2>📋 إنشاء الجداول الأساسية...</h2>";
    
    if ($database->createBasicTables()) {
        echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ تم إنشاء الجداول الأساسية بنجاح</h3>";
        echo "<p>تم إنشاء:</p>";
        echo "<ul>";
        echo "<li>جدول المستخدمين (users)</li>";
        echo "<li>جدول الأدوار (roles)</li>";
        echo "<li>جدول الفروع (branches)</li>";
        echo "<li>جدول سجل التدقيق (audit_logs)</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ فشل في إنشاء الجداول</h3>";
        echo "</div>";
        exit();
    }
    
    // التحقق من المستخدم الافتراضي
    echo "<h2>👤 التحقق من المستخدم الافتراضي...</h2>";
    
    $stmt = $db->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin_user = $stmt->fetch();
    
    if ($admin_user) {
        echo "<div style='color: blue; background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>ℹ️ معلومات المستخدم الافتراضي</h3>";
        echo "<p><strong>اسم المستخدم:</strong> admin</p>";
        echo "<p><strong>كلمة المرور:</strong> admin123</p>";
        echo "<p><strong>البريد الإلكتروني:</strong> " . htmlspecialchars($admin_user['email']) . "</p>";
        echo "<p><strong>الاسم الكامل:</strong> " . htmlspecialchars($admin_user['full_name']) . "</p>";
        echo "<p><strong>تاريخ الإنشاء:</strong> " . $admin_user['created_at'] . "</p>";
        echo "</div>";
    }
    
    // اختبار تسجيل الدخول
    echo "<h2>🧪 اختبار نظام المصادقة...</h2>";
    
    require_once 'includes/auth.php';
    $auth = new Auth();
    
    // اختبار تسجيل دخول صحيح
    $login_result = $auth->login('admin', 'admin123');
    
    if ($login_result['success']) {
        echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ نظام المصادقة يعمل بشكل صحيح</h3>";
        echo "<p>تم تسجيل الدخول بنجاح للمستخدم الافتراضي</p>";
        echo "</div>";
        
        // تسجيل الخروج
        $auth->logout();
    } else {
        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ مشكلة في نظام المصادقة</h3>";
        echo "<p>الخطأ: " . htmlspecialchars($login_result['message']) . "</p>";
        echo "</div>";
    }
    
    // إحصائيات قاعدة البيانات
    echo "<h2>📊 إحصائيات قاعدة البيانات</h2>";
    
    $tables = ['users', 'roles', 'branches', 'audit_logs'];
    echo "<table style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='border: 1px solid #dee2e6; padding: 8px;'>الجدول</th>";
    echo "<th style='border: 1px solid #dee2e6; padding: 8px;'>عدد السجلات</th>";
    echo "<th style='border: 1px solid #dee2e6; padding: 8px;'>الحالة</th>";
    echo "</tr>";
    
    foreach ($tables as $table) {
        try {
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $result = $stmt->fetch();
            $count = $result['count'];
            $status = "✅ موجود";
        } catch (Exception $e) {
            $count = "N/A";
            $status = "❌ غير موجود";
        }
        
        echo "<tr>";
        echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>$table</td>";
        echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>$count</td>";
        echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>$status</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // الخطوات التالية
    echo "<h2>🚀 الخطوات التالية</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ol>";
    echo "<li><strong>اختبار تسجيل الدخول:</strong> <a href='auth/login.php' target='_blank'>auth/login.php</a></li>";
    echo "<li><strong>الوصول للوحة التحكم:</strong> <a href='dashboard/index.php' target='_blank'>dashboard/index.php</a></li>";
    echo "<li><strong>اختبار النظام:</strong> <a href='test.php' target='_blank'>test.php</a></li>";
    echo "<li><strong>تشخيص النظام:</strong> <a href='debug.php' target='_blank'>debug.php</a></li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎉 تم إعداد قاعدة البيانات بنجاح!</h3>";
    echo "<p>النظام جاهز للاستخدام الآن.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ خطأ في إعداد قاعدة البيانات</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    
    echo "<h3>💡 نصائح لحل المشكلة:</h3>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تحقق من إعدادات قاعدة البيانات في config.php</li>";
    echo "<li>تأكد من وجود قاعدة البيانات المحددة</li>";
    echo "<li>تحقق من صلاحيات المستخدم لقاعدة البيانات</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
