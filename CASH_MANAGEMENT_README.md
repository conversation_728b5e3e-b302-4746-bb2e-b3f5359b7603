# نظام إدارة الصناديق والبنوك - Trust Plus

## نظرة عامة

تم إنشاء نظام شامل لإدارة الصناديق النقدية والحسابات البنكية لنظام Trust Plus المالي. يتضمن النظام إدارة كاملة للأرصدة والحركات المالية مع ربط محكم بقاعدة البيانات.

## الملفات المنشأة

### صفحات الواجهة الأمامية:
1. **dashboard/cash.php** - الصفحة الرئيسية لإدارة الصناديق والبنوك

### ملفات الخلفية (Backend):
1. **includes/cash_manager.php** - فئة إدارة الصناديق والبنوك

### ملفات التهيئة:
1. **setup_cash_management.php** - ملف تهيئة قاعدة البيانات والبيانات الأساسية

## المميزات الرئيسية

### إدارة الصناديق النقدية:
- ✅ عرض قائمة الصناديق النقدية مع الأرصدة الحالية
- ✅ إضافة صناديق نقدية جديدة مع رصيد افتتاحي
- ✅ تعديل بيانات الصناديق (الاسم، العملة، المسؤول، الفرع)
- ✅ تفعيل/إلغاء تفعيل الصناديق
- ✅ ربط كل صندوق بعملة ومسؤول وفرع محدد
- ✅ عرض الرصيد الحالي بالعملة المحددة

### إدارة الحسابات البنكية:
- ✅ عرض قائمة الحسابات البنكية مع تفاصيل البنك
- ✅ إضافة حسابات بنكية جديدة مع معلومات كاملة
- ✅ تعديل بيانات الحسابات (الاسم، البنك، رقم الحساب، IBAN، SWIFT)
- ✅ تفعيل/إلغاء تفعيل الحسابات
- ✅ دعم معلومات IBAN و SWIFT للتحويلات الدولية
- ✅ ربط كل حساب بعملة وفرع محدد

### إدارة الحركات المالية:
- ✅ تسجيل حركات الإيداع والسحب للصناديق
- ✅ تسجيل حركات الإيداع والسحب للحسابات البنكية
- ✅ تحديث الأرصدة تلقائياً مع كل حركة
- ✅ منع السحب إذا كان الرصيد غير كافي
- ✅ تسجيل وصف ورقم مرجعي لكل حركة
- ✅ ربط كل حركة بالمستخدم المنفذ

### البحث والفلترة:
- ✅ البحث في أسماء الصناديق والحسابات
- ✅ فلترة حسب العملة
- ✅ فلترة حسب الحالة (نشط/غير نشط)
- ✅ واجهة تفاعلية للبحث المباشر

## قاعدة البيانات

### الجداول المنشأة:

#### 1. جدول الصناديق النقدية (cash_boxes):
```sql
- id: المعرف الفريد
- name: اسم الصندوق
- currency_id: معرف العملة
- initial_balance: الرصيد الافتتاحي
- current_balance: الرصيد الحالي
- branch_id: معرف الفرع
- responsible_user_id: معرف المستخدم المسؤول
- is_active: حالة التفعيل
- created_at: تاريخ الإنشاء
- updated_at: تاريخ آخر تحديث
```

#### 2. جدول الحسابات البنكية (bank_accounts):
```sql
- id: المعرف الفريد
- account_name: اسم الحساب
- bank_name: اسم البنك
- account_number: رقم الحساب
- iban: رقم IBAN
- swift_code: رمز SWIFT
- currency_id: معرف العملة
- initial_balance: الرصيد الافتتاحي
- current_balance: الرصيد الحالي
- branch_id: معرف الفرع
- is_active: حالة التفعيل
- created_at: تاريخ الإنشاء
- updated_at: تاريخ آخر تحديث
```

#### 3. جدول حركات الصناديق (cash_movements):
```sql
- id: المعرف الفريد
- cash_box_id: معرف الصندوق
- movement_type: نوع الحركة (deposit/withdrawal)
- amount: المبلغ
- description: الوصف
- reference_number: الرقم المرجعي
- user_id: معرف المستخدم المنفذ
- created_at: تاريخ الحركة
```

#### 4. جدول حركات البنوك (bank_movements):
```sql
- id: المعرف الفريد
- bank_account_id: معرف الحساب البنكي
- movement_type: نوع الحركة (deposit/withdrawal)
- amount: المبلغ
- description: الوصف
- reference_number: الرقم المرجعي
- user_id: معرف المستخدم المنفذ
- created_at: تاريخ الحركة
```

## الأمان والحماية

### حماية CSRF:
- ✅ رموز CSRF في جميع النماذج
- ✅ التحقق من صحة الرموز قبل تنفيذ العمليات

### التحقق من الصلاحيات:
- ✅ فحص صلاحية `cash.view` للوصول للصفحة
- ✅ فحص صلاحية `cash.edit` لتعديل البيانات
- ✅ فحص صلاحية `cash.movements` لإدارة الحركات

### تسجيل المراجعة:
- ✅ تسجيل جميع العمليات في جدول audit_logs
- ✅ تسجيل معلومات المستخدم وعنوان IP
- ✅ وصف تفصيلي للعمليات المنفذة

### التحقق من صحة البيانات:
- ✅ التحقق من البيانات في الخلفية والواجهة الأمامية
- ✅ منع إدخال قيم سالبة للأرصدة
- ✅ التحقق من توفر الرصيد قبل السحب
- ✅ التحقق من عدم تكرار أرقام الحسابات البنكية

## التثبيت والتشغيل

### 1. تشغيل ملف التهيئة:
```
http://localhost/Trust%20Plus/setup_cash_management.php
```

### 2. الوصول للصفحة:
```
http://localhost/Trust%20Plus/dashboard/cash.php
```

### 3. التنقل بين التبويبات:
- الصناديق النقدية: `cash.php?type=cash`
- الحسابات البنكية: `cash.php?type=bank`

## واجهة المستخدم

### التصميم:
- ✅ تصميم متجاوب يعمل على جميع الأجهزة
- ✅ استخدام Bootstrap 5 للتنسيق
- ✅ أيقونات Font Awesome
- ✅ ألوان متناسقة مع نظام Trust Plus
- ✅ تبويبات للتنقل بين الصناديق والبنوك

### التفاعل:
- ✅ نماذج منبثقة للتأكيد
- ✅ رسائل نجاح وخطأ واضحة
- ✅ تحديث فوري للبيانات
- ✅ البحث والفلترة المباشرة
- ✅ عرض الأرصدة بألوان مختلفة (أخضر للموجب، أحمر للسالب)

### إمكانية الوصول:
- ✅ دعم اللغة العربية بالكامل
- ✅ تخطيط من اليمين لليسار (RTL)
- ✅ تسميات واضحة للنماذج
- ✅ رسائل مساعدة للمستخدمين

## الصلاحيات المطلوبة

### إدارة الصناديق والبنوك:
- **cash.view**: عرض الصناديق والحسابات البنكية
- **cash.edit**: إضافة وتعديل الصناديق والحسابات
- **cash.movements**: إدارة حركات الإيداع والسحب

## المميزات المتقدمة

### إدارة العملات:
- ✅ دعم عملات متعددة لكل صندوق وحساب
- ✅ عرض الأرصدة برموز العملات المناسبة
- ✅ ربط مع نظام إدارة العملات الموجود

### إدارة الفروع:
- ✅ ربط كل صندوق وحساب بفرع محدد
- ✅ فلترة البيانات حسب فرع المستخدم الحالي
- ✅ إمكانية نقل الصناديق والحسابات بين الفروع

### إدارة المستخدمين:
- ✅ تعيين مسؤول لكل صندوق نقدي
- ✅ تسجيل المستخدم المنفذ لكل حركة مالية
- ✅ ربط مع نظام إدارة المستخدمين الموجود

## ملاحظات مهمة

1. **الأرصدة**: يتم تحديث الأرصدة تلقائياً مع كل حركة مالية
2. **العملات**: يجب إضافة العملات في النظام قبل إنشاء الصناديق والحسابات
3. **الفروع**: يجب وجود فرع واحد على الأقل في النظام
4. **الصلاحيات**: يتم إضافة الصلاحيات تلقائياً لدور المدير
5. **الحركات**: جميع الحركات المالية مؤرخة ومربوطة بالمستخدم المنفذ

## الدعم والصيانة

النظام مصمم ليكون قابلاً للتوسع والصيانة:
- كود منظم ومعلق باللغة العربية
- فصل منطق العمل عن واجهة المستخدم
- معالجة شاملة للأخطاء
- تسجيل مفصل للعمليات
- إمكانية إضافة مميزات جديدة بسهولة
- ربط محكم مع باقي وحدات النظام
