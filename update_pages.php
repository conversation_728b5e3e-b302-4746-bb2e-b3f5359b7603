<?php
/**
 * Trust Plus - Page Update Script
 * سكريبت تحديث الصفحات لاستخدام النظام الجديد
 */

// قائمة الصفحات المراد تحديثها
$pages = [
    'dashboard/transfers.php' => [
        'page_type' => 'default',
        'page_title' => 'التحويلات المالية - ' . 'SYSTEM_NAME',
        'page_header' => 'التحويلات المالية',
        'page_subtitle' => 'إدارة التحويلات المحلية والدولية',
        'page_icon' => 'fas fa-paper-plane'
    ],
    'dashboard/accounting.php' => [
        'page_type' => 'default',
        'page_title' => 'المحاسبة - ' . 'SYSTEM_NAME',
        'page_header' => 'المحاسبة',
        'page_subtitle' => 'إدارة الحسابات والقيود المحاسبية',
        'page_icon' => 'fas fa-calculator'
    ],
    'dashboard/users.php' => [
        'page_type' => 'default',
        'page_title' => 'إدارة المستخدمين - ' . 'SYSTEM_NAME',
        'page_header' => 'إدارة المستخدمين',
        'page_subtitle' => 'إضافة وتعديل وإدارة مستخدمي النظام',
        'page_icon' => 'fas fa-users'
    ],
    'dashboard/settings.php' => [
        'page_type' => 'default',
        'page_title' => 'إعدادات النظام - ' . 'SYSTEM_NAME',
        'page_header' => 'إعدادات النظام',
        'page_subtitle' => 'تكوين وإدارة إعدادات النظام',
        'page_icon' => 'fas fa-cog'
    ],
    'dashboard/exchange_rates.php' => [
        'page_type' => 'default',
        'page_title' => 'أسعار الصرف - ' . 'SYSTEM_NAME',
        'page_header' => 'أسعار الصرف',
        'page_subtitle' => 'إدارة وتحديث أسعار صرف العملات',
        'page_icon' => 'fas fa-chart-line'
    ],
    'dashboard/financial_dashboard.php' => [
        'page_type' => 'dashboard',
        'page_title' => 'لوحة الأداء المالي - ' . 'SYSTEM_NAME',
        'page_header' => 'لوحة الأداء المالي',
        'page_subtitle' => 'مراقبة الأداء المالي والإحصائيات',
        'page_icon' => 'fas fa-tachometer-alt'
    ],
    'dashboard/compliance_reports.php' => [
        'page_type' => 'reports',
        'page_title' => 'تقارير الامتثال - ' . 'SYSTEM_NAME',
        'page_header' => 'تقارير الامتثال',
        'page_subtitle' => 'تقارير مكافحة غسل الأموال والامتثال',
        'page_icon' => 'fas fa-shield-alt'
    ]
];

function updatePageHeader($filePath, $pageConfig) {
    if (!file_exists($filePath)) {
        echo "الملف غير موجود: $filePath\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    
    // البحث عن نهاية PHP الأولى
    $phpEndPos = strpos($content, '?>');
    if ($phpEndPos === false) {
        echo "لم يتم العثور على نهاية PHP في: $filePath\n";
        return false;
    }
    
    // إضافة إعدادات الصفحة قبل نهاية PHP
    $pageSettings = "\n// إعدادات الصفحة\n";
    $pageSettings .= "\$page_type = '{$pageConfig['page_type']}';\n";
    $pageSettings .= "\$page_title = '{$pageConfig['page_title']}';\n";
    $pageSettings .= "\$page_header = '{$pageConfig['page_header']}';\n";
    $pageSettings .= "\$page_subtitle = '{$pageConfig['page_subtitle']}';\n";
    $pageSettings .= "\$page_icon = '{$pageConfig['page_icon']}';\n";
    $pageSettings .= "\$show_breadcrumb = true;\n";
    
    // إدراج الإعدادات قبل ?>
    $newContent = substr($content, 0, $phpEndPos) . $pageSettings . substr($content, $phpEndPos);
    
    // استبدال HTML القديم
    $patterns = [
        // استبدال DOCTYPE والرأس
        '/<!DOCTYPE html>.*?<\/head>/s' => '',
        // استبدال body tag
        '/<body[^>]*>/' => '',
        // استبدال نهاية HTML
        '/<\/body>\s*<\/html>/' => '',
        // استبدال روابط CSS
        '/<link[^>]*href=["\'][^"\']*bootstrap[^"\']*["\'][^>]*>/i' => '',
        '/<link[^>]*href=["\'][^"\']*font-awesome[^"\']*["\'][^>]*>/i' => '',
        '/<link[^>]*href=["\'][^"\']*google[^"\']*fonts[^"\']*["\'][^>]*>/i' => '',
        // استبدال روابط JavaScript
        '/<script[^>]*src=["\'][^"\']*bootstrap[^"\']*["\'][^>]*><\/script>/i' => '',
        // استبدال CSS المضمن
        '/<style[^>]*>.*?<\/style>/s' => '',
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $newContent = preg_replace($pattern, $replacement, $newContent);
    }
    
    // إضافة include للـ header
    $headerInclude = "\ninclude '../includes/header.php';\n?>\n\n<!-- محتوى الصفحة -->";
    $newContent = str_replace('?>', $headerInclude, $newContent);
    
    // إضافة include للـ footer في النهاية
    $footerInclude = "\n\n<?php include '../includes/footer.php'; ?>";
    $newContent .= $footerInclude;
    
    // حفظ الملف
    if (file_put_contents($filePath, $newContent)) {
        echo "تم تحديث: $filePath\n";
        return true;
    } else {
        echo "فشل في حفظ: $filePath\n";
        return false;
    }
}

// تشغيل التحديث
echo "بدء تحديث الصفحات...\n\n";

foreach ($pages as $filePath => $pageConfig) {
    echo "تحديث: $filePath\n";
    updatePageHeader($filePath, $pageConfig);
    echo "---\n";
}

echo "\nتم الانتهاء من تحديث الصفحات!\n";

// إنشاء ملف تقرير
$report = "# تقرير تحديث الصفحات\n\n";
$report .= "تاريخ التحديث: " . date('Y-m-d H:i:s') . "\n\n";
$report .= "## الصفحات المحدثة:\n\n";

foreach ($pages as $filePath => $pageConfig) {
    $report .= "- **$filePath**\n";
    $report .= "  - النوع: {$pageConfig['page_type']}\n";
    $report .= "  - العنوان: {$pageConfig['page_header']}\n";
    $report .= "  - الأيقونة: {$pageConfig['page_icon']}\n\n";
}

$report .= "## التغييرات المطبقة:\n\n";
$report .= "1. إضافة متغيرات إعدادات الصفحة\n";
$report .= "2. استبدال HTML القديم بـ include للـ header\n";
$report .= "3. إضافة include للـ footer\n";
$report .= "4. إزالة CSS و JavaScript المضمن\n";
$report .= "5. إزالة روابط CDN المكررة\n\n";

$report .= "## الخطوات التالية:\n\n";
$report .= "1. اختبار جميع الصفحات المحدثة\n";
$report .= "2. التأكد من عمل جميع الوظائف\n";
$report .= "3. إضافة أي CSS أو JavaScript مخصص حسب الحاجة\n";
$report .= "4. تحديث أي روابط أو مسارات مكسورة\n";

file_put_contents('update_report.md', $report);
echo "تم إنشاء تقرير التحديث: update_report.md\n";
?>
