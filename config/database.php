<?php
/**
 * إعدادات قاعدة البيانات - Trust Plus System
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'trust_plus';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    public $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال بقاعدة البيانات: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

// إعدادات النظام العامة
define('SYSTEM_NAME', 'Trust Plus - نظام إدارة التحويلات المالية');
define('SYSTEM_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/Trust Plus/');
define('UPLOAD_PATH', 'uploads/');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('PASSWORD_MIN_LENGTH', 8);

// إعدادات العملة الافتراضية
define('DEFAULT_CURRENCY', 'USD');
define('BASE_CURRENCY_ID', 1);

// إعدادات الصرافة
define('DEFAULT_SPREAD_PERCENTAGE', 0.0050); // 0.5%
define('DEFAULT_COMMISSION_RATE', 0.0025); // 0.25%
define('MIN_EXCHANGE_AMOUNT', 10);
define('MAX_EXCHANGE_AMOUNT', 50000);
define('DAILY_EXCHANGE_LIMIT', 100000);

// إعدادات التقارير
define('REPORTS_PATH', 'reports/');
define('BACKUP_PATH', 'backups/');
?>
