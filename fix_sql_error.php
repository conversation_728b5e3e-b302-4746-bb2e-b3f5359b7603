<?php
/**
 * Trust Plus - SQL Error Fix
 * إصلاح خطأ SQL
 */

echo "🔧 إصلاح خطأ SQL - Trust Plus\n\n";

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // تضمين الملفات
    if (file_exists('config.php')) {
        require_once 'config.php';
        echo "✅ تم تحميل config.php\n";
    }
    
    require_once 'includes/database.php';
    echo "✅ تم تحميل database.php\n";
    
    // الاتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        echo "❌ فشل الاتصال بقاعدة البيانات\n";
        exit();
    }
    
    echo "✅ الاتصال بقاعدة البيانات ناجح\n";
    
    // إنشاء الجداول
    echo "\n🔧 إنشاء الجداول...\n";
    
    // حذف الجداول الموجودة وإعادة إنشائها
    $tables_to_drop = ['audit_logs', 'users', 'roles', 'branches'];
    
    foreach ($tables_to_drop as $table) {
        try {
            $db->exec("DROP TABLE IF EXISTS $table");
            echo "🗑️  تم حذف جدول $table\n";
        } catch (Exception $e) {
            echo "⚠️  لم يتم العثور على جدول $table\n";
        }
    }
    
    // إنشاء جدول الأدوار
    $sql = "CREATE TABLE roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        role_name VARCHAR(50) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "✅ تم إنشاء جدول roles\n";
    
    // إدراج دور افتراضي
    $stmt = $db->prepare("INSERT INTO roles (id, role_name, description) VALUES (1, 'مدير النظام', 'صلاحيات كاملة')");
    $stmt->execute();
    echo "✅ تم إدراج دور افتراضي\n";
    
    // إنشاء جدول الفروع
    $sql = "CREATE TABLE branches (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        address TEXT,
        phone VARCHAR(20),
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "✅ تم إنشاء جدول branches\n";
    
    // إدراج فرع افتراضي
    $stmt = $db->prepare("INSERT INTO branches (id, name, address) VALUES (1, 'الفرع الرئيسي', 'العنوان الرئيسي')");
    $stmt->execute();
    echo "✅ تم إدراج فرع افتراضي\n";
    
    // إنشاء جدول المستخدمين
    $sql = "CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role_id INT DEFAULT 1,
        branch_id INT DEFAULT 1,
        is_active TINYINT(1) DEFAULT 1,
        failed_login_attempts INT DEFAULT 0,
        locked_until DATETIME NULL,
        last_login DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (role_id) REFERENCES roles(id),
        FOREIGN KEY (branch_id) REFERENCES branches(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "✅ تم إنشاء جدول users\n";
    
    // إنشاء المستخدم الافتراضي
    $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $db->prepare("
        INSERT INTO users (username, email, password_hash, full_name, role_id, branch_id) 
        VALUES ('admin', '<EMAIL>', :password_hash, 'مدير النظام', 1, 1)
    ");
    $stmt->bindParam(':password_hash', $password_hash);
    $stmt->execute();
    echo "✅ تم إنشاء المستخدم الافتراضي\n";
    
    // إنشاء جدول سجل التدقيق
    $sql = "CREATE TABLE audit_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(50),
        entity_type VARCHAR(50),
        entity_id INT,
        old_value TEXT,
        new_value TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "✅ تم إنشاء جدول audit_logs\n";
    
    // اختبار تسجيل الدخول
    echo "\n🧪 اختبار تسجيل الدخول...\n";
    
    require_once 'includes/auth.php';
    $auth = new Auth();
    
    $login_result = $auth->login('admin', 'admin123');
    
    if ($login_result['success']) {
        echo "✅ تسجيل الدخول ناجح!\n";
        echo "📝 الرسالة: " . $login_result['message'] . "\n";
        
        $user = $auth->getCurrentUser();
        if ($user) {
            echo "👤 اسم المستخدم: " . $user['username'] . "\n";
            echo "📧 البريد الإلكتروني: " . $user['email'] . "\n";
        }
        
        $auth->logout();
        echo "✅ تم تسجيل الخروج\n";
        
    } else {
        echo "❌ فشل تسجيل الدخول: " . $login_result['message'] . "\n";
    }
    
    echo "\n🎉 تم إصلاح جميع المشاكل بنجاح!\n";
    echo "\n📝 معلومات تسجيل الدخول:\n";
    echo "اسم المستخدم: admin\n";
    echo "كلمة المرور: admin123\n";
    echo "\n🔗 يمكنك الآن الذهاب إلى: auth/login.php\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
}
?>
