<?php
/**
 * Trust Plus - Login Error Fix Script
 * سكريپت إصلاح أخطاء تسجيل الدخول
 */

echo "🔧 إصلاح أخطاء تسجيل الدخول - Trust Plus\n\n";

$fixes_applied = 0;
$errors_found = 0;

// 1. التحقق من ملف config.php
echo "1. فحص ملف التكوين...\n";

if (!file_exists('config.php')) {
    echo "   ❌ ملف config.php غير موجود - إنشاء ملف جديد...\n";
    
    $config_content = '<?php
/**
 * Trust Plus - Configuration File
 */

define("SYSTEM_NAME", "Trust Plus");
define("SYSTEM_VERSION", "2.0.0");
define("SYSTEM_LANGUAGE", "ar");
define("BASE_URL", "/");
define("DEFAULT_CURRENCY", "USD");
define("DATE_FORMAT", "Y-m-d");
define("TIME_FORMAT", "H:i:s");
define("TIMEZONE", "Asia/Riyadh");

// قاعدة البيانات
define("DB_HOST", "localhost");
define("DB_NAME", "trust_plus");
define("DB_USER", "root");
define("DB_PASS", "");

// الأمان
define("SESSION_TIMEOUT", 3600);
define("MAX_LOGIN_ATTEMPTS", 5);
define("PRODUCTION", false);

if (!PRODUCTION) {
    error_reporting(E_ALL);
    ini_set("display_errors", 1);
}

date_default_timezone_set(TIMEZONE);
?>';
    
    file_put_contents('config.php', $config_content);
    echo "   ✅ تم إنشاء ملف config.php\n";
    $fixes_applied++;
} else {
    echo "   ✅ ملف config.php موجود\n";
}

// 2. التحقق من ملف database.php
echo "\n2. فحص ملف قاعدة البيانات...\n";

if (!file_exists('includes/database.php')) {
    echo "   ❌ ملف database.php غير موجود\n";
    $errors_found++;
} else {
    echo "   ✅ ملف database.php موجود\n";
}

// 3. التحقق من ملف auth.php
echo "\n3. فحص ملف المصادقة...\n";

if (!file_exists('includes/auth.php')) {
    echo "   ❌ ملف auth.php غير موجود\n";
    $errors_found++;
} else {
    echo "   ✅ ملف auth.php موجود\n";
}

// 4. اختبار الاتصال بقاعدة البيانات
echo "\n4. اختبار الاتصال بقاعدة البيانات...\n";

try {
    require_once 'config.php';
    require_once 'includes/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "   ✅ الاتصال بقاعدة البيانات ناجح\n";
        
        // التحقق من وجود الجداول
        $tables = ['users', 'roles', 'branches'];
        $missing_tables = [];
        
        foreach ($tables as $table) {
            try {
                $stmt = $db->prepare("SHOW TABLES LIKE '$table'");
                $stmt->execute();
                if ($stmt->rowCount() == 0) {
                    $missing_tables[] = $table;
                }
            } catch (Exception $e) {
                $missing_tables[] = $table;
            }
        }
        
        if (!empty($missing_tables)) {
            echo "   ⚠️  الجداول المفقودة: " . implode(', ', $missing_tables) . "\n";
            echo "   🔧 إنشاء الجداول المفقودة...\n";
            
            if ($database->createBasicTables()) {
                echo "   ✅ تم إنشاء الجداول بنجاح\n";
                $fixes_applied++;
            } else {
                echo "   ❌ فشل في إنشاء الجداول\n";
                $errors_found++;
            }
        } else {
            echo "   ✅ جميع الجداول الأساسية موجودة\n";
        }
        
    } else {
        echo "   ❌ فشل الاتصال بقاعدة البيانات\n";
        echo "   💡 تأكد من:\n";
        echo "      - تشغيل خادم MySQL\n";
        echo "      - صحة إعدادات قاعدة البيانات في config.php\n";
        echo "      - وجود قاعدة البيانات trust_plus\n";
        $errors_found++;
    }
    
} catch (Exception $e) {
    echo "   ❌ خطأ: " . $e->getMessage() . "\n";
    $errors_found++;
}

// 5. اختبار نظام المصادقة
echo "\n5. اختبار نظام المصادقة...\n";

try {
    require_once 'includes/auth.php';
    
    $auth = new Auth();
    echo "   ✅ تم تحميل فئة Auth بنجاح\n";
    
    // اختبار تسجيل دخول وهمي
    $login_result = $auth->login('admin', 'admin123');
    
    if ($login_result['success']) {
        echo "   ✅ نظام المصادقة يعمل بشكل صحيح\n";
        $auth->logout();
    } else {
        echo "   ⚠️  رسالة تسجيل الدخول: " . $login_result['message'] . "\n";
        
        // التحقق من وجود المستخدم الافتراضي
        if ($db) {
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result['count'] == 0) {
                echo "   🔧 إنشاء المستخدم الافتراضي...\n";
                $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $db->prepare("
                    INSERT INTO users (username, email, password_hash, full_name, role_id) 
                    VALUES ('admin', '<EMAIL>', :password_hash, 'مدير النظام', 1)
                ");
                $stmt->bindParam(':password_hash', $password_hash);
                
                if ($stmt->execute()) {
                    echo "   ✅ تم إنشاء المستخدم الافتراضي\n";
                    $fixes_applied++;
                } else {
                    echo "   ❌ فشل في إنشاء المستخدم الافتراضي\n";
                    $errors_found++;
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ خطأ في نظام المصادقة: " . $e->getMessage() . "\n";
    $errors_found++;
}

// 6. فحص ملفات النظام الأساسية
echo "\n6. فحص ملفات النظام...\n";

$required_files = [
    'includes/assets.php',
    'includes/header.php',
    'includes/footer.php',
    'auth/login.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "   ✅ $file موجود\n";
    } else {
        echo "   ❌ $file مفقود\n";
        $errors_found++;
    }
}

// 7. إنشاء صفحة اختبار تسجيل الدخول
echo "\n7. إنشاء صفحة اختبار...\n";

$test_login_content = '<?php
// تضمين ملف التكوين
if (file_exists("config.php")) {
    require_once "config.php";
}

$page_type = "default";
$page_title = "اختبار تسجيل الدخول";
$hide_sidebar = true;
$hide_navbar = true;
$hide_footer = true;

include "includes/header.php";
?>

<div class="container-fluid p-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">اختبار تسجيل الدخول</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6>معلومات تسجيل الدخول الافتراضية:</h6>
                        <p><strong>اسم المستخدم:</strong> admin</p>
                        <p><strong>كلمة المرور:</strong> admin123</p>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <a href="auth/login.php" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            الذهاب لصفحة تسجيل الدخول
                        </a>
                        <a href="setup_database.php" class="btn btn-secondary">
                            <i class="fas fa-database me-2"></i>
                            إعداد قاعدة البيانات
                        </a>
                        <a href="debug.php" class="btn btn-info">
                            <i class="fas fa-bug me-2"></i>
                            تشخيص النظام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include "includes/footer.php"; ?>
';

file_put_contents('test_login.php', $test_login_content);
echo "   ✅ تم إنشاء صفحة test_login.php\n";
$fixes_applied++;

// تقرير نهائي
echo "\n" . str_repeat("=", 50) . "\n";
echo "📋 تقرير الإصلاح:\n";
echo str_repeat("=", 50) . "\n";
echo "✅ الإصلاحات المطبقة: $fixes_applied\n";
echo "❌ الأخطاء المتبقية: $errors_found\n";

if ($errors_found === 0) {
    echo "\n🎉 تم إصلاح جميع المشاكل بنجاح!\n";
    echo "\n📝 الخطوات التالية:\n";
    echo "1. افتح test_login.php في المتصفح\n";
    echo "2. اذهب لصفحة تسجيل الدخول\n";
    echo "3. استخدم: admin / admin123\n";
    echo "4. إذا لم يعمل، شغل setup_database.php\n";
} else {
    echo "\n⚠️ لا تزال هناك بعض المشاكل:\n";
    echo "1. تأكد من تشغيل خادم MySQL\n";
    echo "2. تحقق من إعدادات قاعدة البيانات\n";
    echo "3. شغل setup_database.php لإعداد قاعدة البيانات\n";
}

echo "\n🔗 روابط مفيدة:\n";
echo "- اختبار تسجيل الدخول: test_login.php\n";
echo "- إعداد قاعدة البيانات: setup_database.php\n";
echo "- تشخيص النظام: debug.php\n";
echo "- صفحة تسجيل الدخول: auth/login.php\n";

echo "\n✨ انتهى الإصلاح!\n";
?>
