<?php
/**
 * Trust Plus - Bulk Page Update Script
 * سكريبت تحديث جماعي للصفحات
 */

// قائمة الصفحات والإعدادات
$pages_config = [
    'dashboard/accounting.php' => [
        'page_type' => 'default',
        'page_title' => 'المحاسبة - SYSTEM_NAME',
        'page_header' => 'المحاسبة',
        'page_subtitle' => 'إدارة الحسابات والقيود المحاسبية',
        'page_icon' => 'fas fa-calculator'
    ],
    'dashboard/users.php' => [
        'page_type' => 'default',
        'page_title' => 'إدارة المستخدمين - SYSTEM_NAME',
        'page_header' => 'إدارة المستخدمين',
        'page_subtitle' => 'إضافة وتعديل وإدارة مستخدمي النظام',
        'page_icon' => 'fas fa-users'
    ],
    'dashboard/settings.php' => [
        'page_type' => 'default',
        'page_title' => 'إعدادات النظام - SYSTEM_NAME',
        'page_header' => 'إعدادات النظام',
        'page_subtitle' => 'تكوين وإدارة إعدادات النظام',
        'page_icon' => 'fas fa-cog'
    ],
    'dashboard/exchange_rates.php' => [
        'page_type' => 'default',
        'page_title' => 'أسعار الصرف - SYSTEM_NAME',
        'page_header' => 'أسعار الصرف',
        'page_subtitle' => 'إدارة وتحديث أسعار صرف العملات',
        'page_icon' => 'fas fa-chart-line'
    ],
    'dashboard/financial_dashboard.php' => [
        'page_type' => 'dashboard',
        'page_title' => 'لوحة الأداء المالي - SYSTEM_NAME',
        'page_header' => 'لوحة الأداء المالي',
        'page_subtitle' => 'مراقبة الأداء المالي والإحصائيات',
        'page_icon' => 'fas fa-tachometer-alt'
    ],
    'dashboard/compliance_reports.php' => [
        'page_type' => 'reports',
        'page_title' => 'تقارير الامتثال - SYSTEM_NAME',
        'page_header' => 'تقارير الامتثال',
        'page_subtitle' => 'تقارير مكافحة غسل الأموال والامتثال',
        'page_icon' => 'fas fa-shield-alt'
    ]
];

function updatePage($filePath, $config) {
    if (!file_exists($filePath)) {
        echo "❌ الملف غير موجود: $filePath\n";
        return false;
    }
    
    echo "🔄 تحديث: $filePath\n";
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // 1. إضافة إعدادات الصفحة بعد $current_user
    $pageSettings = "\n// إعدادات الصفحة\n";
    $pageSettings .= "\$page_type = '{$config['page_type']}';\n";
    $pageSettings .= "\$page_title = '{$config['page_title']}';\n";
    $pageSettings .= "\$page_header = '{$config['page_header']}';\n";
    $pageSettings .= "\$page_subtitle = '{$config['page_subtitle']}';\n";
    $pageSettings .= "\$page_icon = '{$config['page_icon']}';\n";
    $pageSettings .= "\$show_breadcrumb = true;\n";
    
    // البحث عن $current_user وإضافة الإعدادات بعدها
    $pattern = '/(\$current_user\s*=\s*\$auth->getCurrentUser\(\);)/';
    if (preg_match($pattern, $content)) {
        $content = preg_replace($pattern, '$1' . $pageSettings, $content);
        echo "✅ تم إضافة إعدادات الصفحة\n";
    } else {
        echo "⚠️  لم يتم العثور على \$current_user\n";
    }
    
    // 2. استبدال DOCTYPE والرأس بـ include header
    $patterns = [
        '/<!DOCTYPE html>.*?<\/head>/s' => '',
        '/<body[^>]*>/' => '',
        '/<\/body>\s*<\/html>/' => '',
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $content = preg_replace($pattern, $replacement, $content);
    }
    
    // 3. استبدال روابط CSS و JS
    $content = preg_replace('/<link[^>]*href=["\'][^"\']*bootstrap[^"\']*["\'][^>]*>/i', '', $content);
    $content = preg_replace('/<link[^>]*href=["\'][^"\']*font-awesome[^"\']*["\'][^>]*>/i', '', $content);
    $content = preg_replace('/<link[^>]*href=["\'][^"\']*google[^"\']*fonts[^"\']*["\'][^>]*>/i', '', $content);
    $content = preg_replace('/<script[^>]*src=["\'][^"\']*bootstrap[^"\']*["\'][^>]*><\/script>/i', '', $content);
    
    // 4. إزالة CSS المضمن
    $content = preg_replace('/<style[^>]*>.*?<\/style>/s', '', $content);
    
    // 5. إضافة include للـ header
    $content = preg_replace('/\?>\s*$/', "include '../includes/header.php';\n?>\n\n<!-- محتوى الصفحة -->", $content);
    
    // 6. إضافة include للـ footer في النهاية
    $content = rtrim($content) . "\n\n<?php include '../includes/footer.php'; ?>";
    
    // 7. تنظيف المحتوى
    $content = preg_replace('/\n{3,}/', "\n\n", $content); // إزالة الأسطر الفارغة الزائدة
    
    // حفظ الملف
    if (file_put_contents($filePath, $content)) {
        echo "✅ تم حفظ الملف بنجاح\n";
        return true;
    } else {
        echo "❌ فشل في حفظ الملف\n";
        return false;
    }
}

// تشغيل التحديث
echo "🚀 بدء تحديث الصفحات...\n\n";

$updated = 0;
$failed = 0;

foreach ($pages_config as $filePath => $config) {
    echo "📄 معالجة: $filePath\n";
    
    if (updatePage($filePath, $config)) {
        $updated++;
        echo "✅ تم التحديث بنجاح\n";
    } else {
        $failed++;
        echo "❌ فشل التحديث\n";
    }
    
    echo str_repeat("-", 50) . "\n";
}

echo "\n📊 ملخص التحديث:\n";
echo "✅ تم تحديث: $updated صفحة\n";
echo "❌ فشل: $failed صفحة\n";

if ($updated > 0) {
    echo "\n🎉 تم الانتهاء من تحديث الصفحات بنجاح!\n";
    echo "\n📝 الخطوات التالية:\n";
    echo "1. اختبار جميع الصفحات المحدثة\n";
    echo "2. التأكد من عمل جميع الوظائف\n";
    echo "3. إضافة أي CSS أو JavaScript مخصص حسب الحاجة\n";
    echo "4. تحديث أي روابط أو مسارات مكسورة\n";
}

// إنشاء تقرير مفصل
$report = "# تقرير تحديث الصفحات - " . date('Y-m-d H:i:s') . "\n\n";
$report .= "## الإحصائيات:\n";
$report .= "- الصفحات المحدثة: $updated\n";
$report .= "- الصفحات الفاشلة: $failed\n";
$report .= "- إجمالي الصفحات: " . count($pages_config) . "\n\n";

$report .= "## الصفحات المحدثة:\n\n";
foreach ($pages_config as $filePath => $config) {
    $status = file_exists($filePath) ? "✅" : "❌";
    $report .= "- $status **$filePath**\n";
    $report .= "  - النوع: {$config['page_type']}\n";
    $report .= "  - العنوان: {$config['page_header']}\n";
    $report .= "  - الأيقونة: {$config['page_icon']}\n\n";
}

$report .= "## التغييرات المطبقة:\n\n";
$report .= "1. ✅ إضافة متغيرات إعدادات الصفحة\n";
$report .= "2. ✅ استبدال HTML القديم بـ include للـ header\n";
$report .= "3. ✅ إضافة include للـ footer\n";
$report .= "4. ✅ إزالة CSS و JavaScript المضمن\n";
$report .= "5. ✅ إزالة روابط CDN المكررة\n";
$report .= "6. ✅ تنظيف وتنسيق المحتوى\n\n";

$report .= "## ملاحظات:\n\n";
$report .= "- جميع الصفحات تستخدم الآن النظام الموحد للأصول\n";
$report .= "- تم الحفاظ على الوظائف الأساسية لكل صفحة\n";
$report .= "- يُنصح بإجراء اختبار شامل للتأكد من عمل جميع الميزات\n";

file_put_contents('bulk_update_report.md', $report);
echo "\n📄 تم إنشاء تقرير مفصل: bulk_update_report.md\n";
?>
