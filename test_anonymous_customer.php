<?php
/**
 * Trust Plus - Test Anonymous Customer Feature
 * اختبار ميزة العميل المجهول
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 اختبار ميزة العميل المجهول</h1>";
echo "<hr>";

try {
    // تحميل الملفات المطلوبة
    require_once 'config.php';
    require_once 'includes/database.php';
    require_once 'includes/customer_manager.php';
    
    // بدء الجلسة
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    $customerManager = new CustomerManager();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<h2>🔍 اختبار إنشاء عميل مجهول</h2>";
    
    // اختبار إنشاء عميل مجهول
    $result = $customerManager->createAnonymousCustomer();
    
    if ($result['success']) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
        echo "<h3>✅ تم إنشاء عميل مجهول بنجاح!</h3>";
        echo "<p><strong>معرف العميل:</strong> " . $result['customer_id'] . "</p>";
        echo "<p><strong>الرسالة:</strong> " . htmlspecialchars($result['message']) . "</p>";
        echo "</div>";
        
        $anonymous_customer_id = $result['customer_id'];
        
        // جلب بيانات العميل المجهول
        echo "<h3>📋 بيانات العميل المجهول</h3>";
        $stmt = $db->prepare("SELECT * FROM customers WHERE id = :customer_id");
        $stmt->bindParam(':customer_id', $anonymous_customer_id);
        $stmt->execute();
        $customer_data = $stmt->fetch();
        
        if ($customer_data) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 10px;'>الحقل</th>";
            echo "<th style='padding: 10px;'>القيمة</th>";
            echo "</tr>";
            
            $fields = [
                'id' => 'معرف العميل',
                'full_name' => 'الاسم الكامل',
                'id_number' => 'رقم الهوية',
                'customer_type' => 'نوع العميل',
                'risk_level' => 'مستوى المخاطر',
                'is_active' => 'نشط',
                'created_at' => 'تاريخ الإنشاء',
                'notes' => 'ملاحظات'
            ];
            
            foreach ($fields as $field => $label) {
                echo "<tr>";
                echo "<td style='padding: 8px; font-weight: bold;'>$label</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($customer_data[$field] ?? 'غير محدد') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // اختبار التحقق من كون العميل مجهول
        echo "<h3>🔍 اختبار التحقق من العميل المجهول</h3>";
        $is_anonymous = $customerManager->isAnonymousCustomer($anonymous_customer_id);
        
        if ($is_anonymous) {
            echo "<p>✅ تم التحقق بنجاح: العميل مجهول</p>";
        } else {
            echo "<p>❌ خطأ في التحقق: العميل ليس مجهول</p>";
        }
        
        // اختبار عميل عادي
        echo "<h3>🧑‍💼 اختبار عميل عادي</h3>";
        $stmt = $db->prepare("SELECT id FROM customers WHERE id_number NOT LIKE 'ANON_%' LIMIT 1");
        $stmt->execute();
        $normal_customer = $stmt->fetch();
        
        if ($normal_customer) {
            $is_normal_anonymous = $customerManager->isAnonymousCustomer($normal_customer['id']);
            if (!$is_normal_anonymous) {
                echo "<p>✅ تم التحقق بنجاح: العميل العادي ليس مجهول</p>";
            } else {
                echo "<p>❌ خطأ في التحقق: العميل العادي يظهر كمجهول</p>";
            }
        } else {
            echo "<p>⚠️ لا يوجد عملاء عاديون للاختبار</p>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #f5c6cb;'>";
        echo "<h3>❌ فشل في إنشاء عميل مجهول</h3>";
        echo "<p><strong>الرسالة:</strong> " . htmlspecialchars($result['message']) . "</p>";
        echo "</div>";
    }
    
    // عرض جميع العملاء المجهولين
    echo "<hr>";
    echo "<h2>👥 جميع العملاء المجهولين</h2>";
    
    $stmt = $db->prepare("
        SELECT id, full_name, id_number, created_at 
        FROM customers 
        WHERE id_number LIKE 'ANON_%' 
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $anonymous_customers = $stmt->fetchAll();
    
    if (count($anonymous_customers) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>المعرف</th>";
        echo "<th style='padding: 10px;'>الاسم</th>";
        echo "<th style='padding: 10px;'>رقم الهوية</th>";
        echo "<th style='padding: 10px;'>تاريخ الإنشاء</th>";
        echo "<th style='padding: 10px;'>الإجراءات</th>";
        echo "</tr>";
        
        foreach ($anonymous_customers as $customer) {
            echo "<tr>";
            echo "<td style='padding: 8px; text-align: center;'>" . $customer['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['full_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($customer['id_number']) . "</td>";
            echo "<td style='padding: 8px;'>" . $customer['created_at'] . "</td>";
            echo "<td style='padding: 8px; text-align: center;'>";
            echo "<button onclick=\"deleteAnonymousCustomer(" . $customer['id'] . ")\" style='background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;'>حذف</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>إجمالي العملاء المجهولين:</strong> " . count($anonymous_customers) . "</p>";
    } else {
        echo "<p>لا يوجد عملاء مجهولين</p>";
    }
    
    // إحصائيات العملاء
    echo "<hr>";
    echo "<h2>📊 إحصائيات العملاء</h2>";
    
    // إجمالي العملاء
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM customers WHERE is_active = 1");
    $stmt->execute();
    $total_customers = $stmt->fetch()['total'];
    
    // العملاء المجهولين
    $stmt = $db->prepare("SELECT COUNT(*) as anonymous FROM customers WHERE id_number LIKE 'ANON_%' AND is_active = 1");
    $stmt->execute();
    $anonymous_count = $stmt->fetch()['anonymous'];
    
    // العملاء العاديين
    $normal_count = $total_customers - $anonymous_count;
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid #bbdefb;'>";
    echo "<h3 style='margin: 0; color: #1976d2;'>$total_customers</h3>";
    echo "<p style='margin: 5px 0; color: #1976d2;'>إجمالي العملاء</p>";
    echo "</div>";
    
    echo "<div style='background: #fff3e0; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid #ffcc02;'>";
    echo "<h3 style='margin: 0; color: #f57c00;'>$anonymous_count</h3>";
    echo "<p style='margin: 5px 0; color: #f57c00;'>عملاء مجهولين</p>";
    echo "</div>";
    
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center; border: 1px solid #c8e6c9;'>";
    echo "<h3 style='margin: 0; color: #388e3c;'>$normal_count</h3>";
    echo "<p style='margin: 5px 0; color: #388e3c;'>عملاء عاديين</p>";
    echo "</div>";
    
    echo "</div>";
    
    // نسبة العملاء المجهولين
    $anonymous_percentage = $total_customers > 0 ? round(($anonymous_count / $total_customers) * 100, 2) : 0;
    echo "<p><strong>نسبة العملاء المجهولين:</strong> $anonymous_percentage%</p>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #28a745;'>";
    echo "<h2>🎉 اختبار ميزة العميل المجهول مكتمل!</h2>";
    echo "<h3>✅ النتائج:</h3>";
    echo "<ul>";
    echo "<li>✅ إنشاء عميل مجهول يعمل بشكل صحيح</li>";
    echo "<li>✅ التحقق من العميل المجهول يعمل بشكل صحيح</li>";
    echo "<li>✅ عرض العملاء المجهولين يعمل بشكل صحيح</li>";
    echo "<li>✅ الإحصائيات تعمل بشكل صحيح</li>";
    echo "</ul>";
    
    echo "<h3>🔗 اختبر النظام الآن:</h3>";
    echo "<p><a href='dashboard/exchange.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>💱 عمليات الصرافة</a></p>";
    echo "<p><a href='dashboard/customers.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>👥 إدارة العملاء</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في الاختبار</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<script>
function deleteAnonymousCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل المجهول؟')) {
        // يمكن إضافة AJAX لحذف العميل
        alert('ميزة الحذف ستتم إضافتها لاحقاً');
    }
}
</script>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

table {
    font-size: 0.9em;
}

th {
    background: #f8f9fa !important;
    font-weight: bold;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    line-height: 1.6;
}
</style>
