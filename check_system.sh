#!/bin/bash

echo "🔍 فحص نظام Trust Plus..."
echo ""

# متغيرات العد
total_files=0
existing_files=0
missing_files=()

# قائمة الملفات المطلوبة
declare -A required_files=(
    ["includes/assets.php"]="فئة إدارة الأصول"
    ["includes/header.php"]="قالب الرأس"
    ["includes/footer.php"]="قالب التذييل"
    ["includes/sidebar.php"]="الشريط الجانبي"
    ["includes/navbar.php"]="شريط التنقل"
    ["includes/breadcrumb.php"]="مسار التنقل"
    ["assets/css/main.css"]="الأنماط الرئيسية"
    ["assets/css/dashboard.css"]="أنماط لوحة التحكم"
    ["assets/css/reports.css"]="أنماط التقارير"
    ["assets/js/main.js"]="JavaScript الرئيسي"
    ["assets/js/dashboard.js"]="JavaScript لوحة التحكم"
    ["assets/js/reports.js"]="JavaScript التقارير"
    ["assets/README.md"]="دليل نظام الأصول"
    ["SYSTEM_UPDATE_REPORT.md"]="تقرير التحديث"
    ["QUICK_START_GUIDE.md"]="دليل البدء السريع"
)

echo "📁 فحص الملفات الجديدة:"
echo "--------------------------------------------------"

for file in "${!required_files[@]}"; do
    total_files=$((total_files + 1))
    if [ -f "$file" ]; then
        existing_files=$((existing_files + 1))
        size=$(stat -c%s "$file" 2>/dev/null || echo "0")
        echo "✅ $file (${required_files[$file]}) - ${size} bytes"
    else
        missing_files+=("$file")
        echo "❌ $file (${required_files[$file]}) - مفقود"
    fi
done

echo ""
echo "📄 فحص الصفحات المحدثة:"
echo "--------------------------------------------------"

# قائمة الصفحات المحدثة
declare -A updated_pages=(
    ["auth/login.php"]="صفحة تسجيل الدخول"
    ["dashboard/index.php"]="لوحة التحكم الرئيسية"
    ["dashboard/customers.php"]="إدارة العملاء"
    ["dashboard/exchange.php"]="عمليات الصرافة"
    ["dashboard/transfers.php"]="التحويلات المالية"
    ["dashboard/reports.php"]="التقارير المالية"
    ["dashboard/settings.php"]="إعدادات النظام"
    ["dashboard/exchange_rates.php"]="أسعار الصرف"
    ["dashboard/financial_dashboard.php"]="لوحة الأداء المالي"
    ["dashboard/compliance_reports.php"]="تقارير الامتثال"
)

updated_count=0
total_pages=${#updated_pages[@]}

for page in "${!updated_pages[@]}"; do
    if [ -f "$page" ]; then
        if grep -q "include '../includes/header.php'" "$page"; then
            updated_count=$((updated_count + 1))
            echo "✅ $page (${updated_pages[$page]}) - محدث"
        else
            echo "⚠️  $page (${updated_pages[$page]}) - يحتاج تحديث"
        fi
    else
        echo "❌ $page (${updated_pages[$page]}) - غير موجود"
    fi
done

echo ""
echo "📊 ملخص الفحص:"
echo "=================================================="
echo "📁 الملفات الجديدة: $existing_files/$total_files"
echo "📄 الصفحات المحدثة: $updated_count/$total_pages"

if [ ${#missing_files[@]} -eq 0 ]; then
    echo "🎉 جميع الملفات موجودة!"
else
    echo "⚠️  الملفات المفقودة:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
fi

echo ""
echo "🔐 فحص الأذونات:"
echo "--------------------------------------------------"

directories=("assets" "assets/css" "assets/js" "includes")
for dir in "${directories[@]}"; do
    if [ -d "$dir" ]; then
        perms=$(stat -c "%a" "$dir" 2>/dev/null || echo "000")
        if [ "$perms" -ge "755" ]; then
            echo "✅ $dir - أذونات صحيحة ($perms)"
        else
            echo "⚠️  $dir - أذونات قد تحتاج تعديل ($perms)"
        fi
    else
        echo "❌ $dir - مجلد غير موجود"
    fi
done

echo ""
echo "============================================================"
echo "📋 التقرير النهائي:"
echo "============================================================"

# حساب النقاط
file_score=$((existing_files * 100 / total_files))
page_score=$((updated_count * 100 / total_pages))
total_score=$(((file_score + page_score) / 2))

echo "📁 الملفات: $file_score%"
echo "📄 الصفحات: $page_score%"
echo ""
echo "🎯 النقاط الإجمالية: $total_score%"

if [ $total_score -ge 90 ]; then
    echo "🎉 ممتاز! النظام جاهز للاستخدام!"
elif [ $total_score -ge 70 ]; then
    echo "👍 جيد! بعض التحسينات مطلوبة."
else
    echo "⚠️  يحتاج المزيد من العمل."
fi

echo ""
echo "📝 التوصيات:"
if [ ${#missing_files[@]} -gt 0 ]; then
    echo "- إنشاء الملفات المفقودة"
fi
if [ $updated_count -lt $total_pages ]; then
    echo "- تحديث الصفحات المتبقية"
fi

echo ""
echo "🔗 الموارد المفيدة:"
echo "- دليل البدء السريع: QUICK_START_GUIDE.md"
echo "- تقرير التحديث: SYSTEM_UPDATE_REPORT.md"
echo "- دليل الأصول: assets/README.md"

echo ""
echo "✨ انتهى الفحص!"
