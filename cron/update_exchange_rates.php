<?php
/**
 * Trust Plus - Auto Exchange Rates Update Script
 * يتم استدعاء هذا الملف من خلال جدول كرون (Cron Job) لتحديث أسعار الصرف تلقائياً
 * 
 * مثال استخدام جدول كرون: 
 * 0 4 * * * /usr/bin/php /opt/lampp/htdocs/Trust\ Plus/cron/update_exchange_rates.php >> /var/log/exchange-rates.log 2>&1
 */

// تعيين المسار المطلق للمجلد الجذري
$root_path = dirname(dirname(__FILE__));

// تحديد المسار للملفات المطلوبة
require_once $root_path . '/config.php';
require_once $root_path . '/includes/database.php';
require_once $root_path . '/includes/exchange_rate_api.php';

// تسجيل الوقت
echo "[" . date('Y-m-d H:i:s') . "] بدء تحديث أسعار الصرف التلقائي\n";

// تهيئة كائن API
$exchangeRateAPI = new ExchangeRateAPI();

// تعيين معرف المستخدم النظام (يمكن استخدام معرف مدير النظام أو إنشاء مستخدم خاص للعمليات التلقائية)
$system_user_id = 1; // يمكن تغييره حسب معرف مدير النظام

try {
    // محاولة التحديث من مصادر مختلفة، بدءًا بالمصدر الافتراضي
    echo "[" . date('Y-m-d H:i:s') . "] محاولة تحديث الأسعار من المصدر: openexchangerates\n";
    $exchangeRateAPI->setProvider('openexchangerates');
    $result = $exchangeRateAPI->updateRatesFromAPI($system_user_id);
    
    // إذا فشل المصدر الأول، نجرب مصدر آخر
    if (!$result['success']) {
        echo "[" . date('Y-m-d H:i:s') . "] فشل التحديث من المصدر الأول. المحاولة مع المصدر: frankfurter\n";
        $exchangeRateAPI->setProvider('frankfurter');
        $result = $exchangeRateAPI->updateRatesFromAPI($system_user_id);
        
        // إذا فشل المصدر الثاني، نجرب المصدر الثالث
        if (!$result['success']) {
            echo "[" . date('Y-m-d H:i:s') . "] فشل التحديث من المصدر الثاني. المحاولة مع المصدر: exchangerate-api\n";
            $exchangeRateAPI->setProvider('exchangerate-api');
            $result = $exchangeRateAPI->updateRatesFromAPI($system_user_id);
        }
    }
    
    // عرض النتيجة
    if ($result['success']) {
        echo "[" . date('Y-m-d H:i:s') . "] نجاح! " . $result['message'] . "\n";
        echo "[" . date('Y-m-d H:i:s') . "] تم تحديث " . $result['updated_pairs'] . " زوج من العملات\n";
        echo "[" . date('Y-m-d H:i:s') . "] آخر تحديث: " . $result['last_update'] . "\n";
    } else {
        echo "[" . date('Y-m-d H:i:s') . "] فشل في تحديث أسعار الصرف: " . $result['message'] . "\n";
    }
    
} catch (Exception $e) {
    echo "[" . date('Y-m-d H:i:s') . "] حدث خطأ أثناء تحديث أسعار الصرف: " . $e->getMessage() . "\n";
}

echo "[" . date('Y-m-d H:i:s') . "] انتهى تنفيذ تحديث أسعار الصرف التلقائي\n";
?> 