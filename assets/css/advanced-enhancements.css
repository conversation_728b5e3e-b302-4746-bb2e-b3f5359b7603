/* Trust Plus - Advanced Dashboard Enhancements */
/* تحسينات متقدمة للوحة التحكم */

/* تحسينات متقدمة للرسوم البيانية */
.chart-container {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-title {
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
}

.chart-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.chart-filter-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    background: white;
    color: var(--dark-color);
    font-weight: 600;
    transition: var(--transition);
}

.chart-filter-btn:hover,
.chart-filter-btn.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

/* تحسينات متقدمة للجداول */
.advanced-table {
    width: 100%;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.advanced-table thead {
    background: var(--primary-gradient);
}

.advanced-table thead th {
    color: white;
    font-weight: 600;
    padding: 1.25rem 1rem;
    border: none;
}

.advanced-table tbody tr {
    transition: var(--transition);
}

.advanced-table tbody tr:hover {
    background: rgba(67, 97, 238, 0.05);
}

.advanced-table tbody td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #f0f0f0;
}

/* تحسينات متقدمة للبطاقات */
.info-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-gradient);
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.info-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.info-card-title {
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
}

.info-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    color: white;
    font-size: 1.25rem;
}

/* تحسينات متقدمة للنماذج */
.advanced-form {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--card-shadow);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    display: block;
}

.advanced-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    transition: var(--transition);
}

.advanced-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.advanced-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    background: white;
    color: var(--dark-color);
    transition: var(--transition);
}

.advanced-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* تحسينات متقدمة للتنبيهات */
.advanced-alert {
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: none;
}

.advanced-alert-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.advanced-alert-content {
    flex: 1;
}

.advanced-alert-title {
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.advanced-alert-message {
    margin: 0;
    opacity: 0.8;
}

.advanced-alert.primary {
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.advanced-alert.success {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.advanced-alert.warning {
    background: rgba(255, 158, 0, 0.1);
    color: var(--warning-color);
}

.advanced-alert.danger {
    background: rgba(247, 37, 133, 0.1);
    color: var(--danger-color);
}

/* تحسينات متقدمة للقوائم */
.advanced-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.advanced-list-item {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.advanced-list-item:last-child {
    border-bottom: none;
}

.advanced-list-item:hover {
    background: rgba(67, 97, 238, 0.05);
}

.advanced-list-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.advanced-list-content {
    flex: 1;
}

.advanced-list-title {
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 0.25rem 0;
}

.advanced-list-subtitle {
    color: #6c757d;
    margin: 0;
    font-size: 0.9rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .chart-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .chart-filters {
        width: 100%;
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }
    
    .advanced-table {
        display: block;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .info-card {
        margin-bottom: 1rem;
    }
    
    .advanced-form {
        padding: 1.5rem;
    }
} 