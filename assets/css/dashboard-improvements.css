/* Trust Plus - Dashboard Comprehensive Improvements */
/* تحسينات شاملة للوحة التحكم */

/* تحسين الخلفية العامة */
body {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

/* تحسين القائمة الجانبية */
.sidebar {
    background: linear-gradient(180deg, #4361ee 0%, #3f37c9 100%);
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-brand {
    background: rgba(0,0,0,0.1);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    padding: 1.5rem;
}

.sidebar-brand img {
    height: 40px;
    filter: brightness(0) invert(1);
}

.sidebar-menu {
    padding: 1rem 0;
}

.sidebar-link {
    margin: 0.25rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sidebar-link:hover,
.sidebar-link.active {
    background: rgba(255,255,255,0.15);
    transform: translateX(-3px);
}

.sidebar-link::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: #fff;
    transform: scaleY(0);
    transition: transform 0.2s;
}

.sidebar-link:hover::before,
.sidebar-link.active::before {
    transform: scaleY(1);
}

.sidebar .nav-item {
    color: #ffffff !important;
    margin: 0.5rem 0;
    padding: 0.25rem 1rem;
}

.sidebar .nav-item:hover {
    background: rgba(255,255,255,0.08);
    border-radius: 8px;
}

/* تحسين رأس الصفحة */
.page-header {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 15px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2b2d42;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 1.25rem;
}

.card-body {
    padding: 1.5rem;
}

/* تحسين الجداول */
.table-container {
    background: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    overflow: hidden;
}

.table {
    width: 100%;
    margin-bottom: 0;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: 1rem;
    border-bottom: 2px solid rgba(0,0,0,0.05);
    color: #2b2d42;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    color: #6c757d;
}

.table tr:hover {
    background: rgba(67,97,238,0.03);
}

/* تحسين النماذج */
.form-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #2b2d42;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.25rem rgba(67,97,238,0.1);
}

/* تحسين الأزرار */
.btn {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(67,97,238,0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3f37c9 0%, #4361ee 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(67,97,238,0.3);
}

/* تحسين الشارات والحالات */
.badge {
    padding: 0.5em 1em;
    font-weight: 600;
    border-radius: 6px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge i {
    font-size: 0.85rem;
}

.status-badge.pending {
    background: rgba(255,193,7,0.1);
    color: #ffc107;
}

.status-badge.approved {
    background: rgba(25,135,84,0.1);
    color: #198754;
}

.status-badge.rejected {
    background: rgba(220,53,69,0.1);
    color: #dc3545;
}

/* تحسين عناصر البحث والتصفية */
.search-filters {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
}

.filter-group {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-item {
    flex: 1;
    min-width: 200px;
}

/* تحسين الإحصائيات والمؤشرات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to left, #4361ee, #3f37c9);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    background: rgba(67,97,238,0.1);
    color: #4361ee;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2b2d42;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* تحسين التنبيهات والرسائل */
.alert {
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
}

.alert-icon {
    font-size: 1.25rem;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

/* تحسين التذييل */
.footer {
    background: white;
    padding: 1.5rem 0;
    margin-top: 3rem;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-text {
    color: #6c757d;
    font-size: 0.9rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .filter-group {
        flex-direction: column;
    }
    
    .filter-item {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .table-container {
        overflow-x: auto;
    }
}

/* تحسين الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-title {
    font-weight: 600;
    color: #2b2d42;
    margin: 0;
}

.chart-legend {
    display: flex;
    gap: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
}

/* تحسين صفحة الملف الشخصي */
.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    color: white;
}

.profile-avatar-container {
    position: relative;
    display: inline-block;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid rgba(255,255,255,0.3);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.profile-name {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.profile-role {
    color: rgba(255,255,255,0.9);
    font-size: 1rem;
    margin-bottom: 1rem;
}

.profile-info {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.profile-stat {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.profile-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: white;
}

.profile-stat-label {
    font-size: 0.85rem;
    color: rgba(255,255,255,0.8);
    font-weight: 500;
}

/* تحسين التبويبات */
.nav-tabs .nav-link {
    border: none;
    border-radius: 10px 10px 0 0;
    margin-left: 5px;
    background: #f8f9fa;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    background: #4361ee;
    color: white;
    border: none;
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.12);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0 !important;
    padding: 1.25rem 1.5rem;
}

/* تحسين النماذج */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.input-group .btn {
    border-radius: 0 10px 10px 0;
    border: 2px solid #e9ecef;
    border-left: none;
}

/* تحسين الأزرار */
.btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
    border: none;
    color: white;
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background: linear-gradient(135deg, #06ffa5 0%, #00d4aa 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #74c0fc 0%, #339af0 100%);
    color: #0c5460;
}

/* تحسين الجدول الزمني */
.timeline-item {
    position: relative;
    padding-right: 2rem;
    margin-bottom: 1.5rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    right: -8px;
    top: 0;
    width: 16px;
    height: 16px;
    background: #4361ee;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.timeline-content {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 3px 15px rgba(0,0,0,0.08);
    border-right: 4px solid #4361ee;
}

.timeline-title {
    color: #2b2d42;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* تحسين مؤشر قوة كلمة المرور */
#password_strength {
    margin-top: 0.5rem;
}

#password_strength span {
    font-weight: 600;
    font-size: 0.9rem;
}

#password_match {
    margin-top: 0.5rem;
}

#password_match span {
    font-weight: 500;
    font-size: 0.9rem;
}

/* تحسين التبديل */
.form-check-input:checked {
    background-color: #4361ee;
    border-color: #4361ee;
}

.form-check-input:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

/* تحسين الشارات */
.badge {
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    font-weight: 500;
}

/* تحسين الحاويات */
.container-fluid {
    padding: 2rem;
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .profile-header {
        padding: 1.5rem;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
    }

    .profile-name {
        font-size: 1.5rem;
    }

    .container-fluid {
        padding: 1rem;
    }

    .card-body {
        padding: 1rem;
    }
}

.profile-stat-label {
    color: #6c757d;
    font-size: 0.8rem;
}

/* تحسين أزرار الإجراءات */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    min-width: 120px;
}

.action-buttons .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    text-align: right;
    white-space: nowrap;
}

.action-buttons .btn:hover {
    transform: translateX(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.action-buttons .btn i {
    font-size: 0.7rem;
}

/* تحسين الأزرار القديمة للتوافق */
.btn-action {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: #f8f9fa;
    color: #6c757d;
    border: none;
    transition: all 0.3s ease;
}

.btn-action:hover {
    background: #4361ee;
    color: white;
    transform: translateY(-2px);
}

/* تحسين جدول العملاء */
.customers-table td {
    vertical-align: middle;
}

.customers-table .action-buttons {
    min-width: 100px;
}

/* تحسين الأزرار للشاشات الصغيرة */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: row;
        flex-wrap: wrap;
        min-width: auto;
    }

    .action-buttons .btn {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .action-buttons .btn span {
        display: none;
    }

    .action-buttons .btn i {
        margin: 0 !important;
    }
}

/* تحسين شريط التقدم */
.progress {
    height: 8px;
    border-radius: 4px;
    background: rgba(0,0,0,0.05);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(to right, #4361ee, #3f37c9);
    transition: width 0.3s ease;
}

/* تحسين التنقل بين الصفحات */
.pagination {
    display: flex;
    gap: 0.5rem;
    margin-top: 2rem;
}

.page-item {
    list-style: none;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: white;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 600;
}

.page-link:hover,
.page-item.active .page-link {
    background: #4361ee;
    color: white;
}

/* --- إصلاح الشريط الجانبي وتداخله مع الصفحة --- */
:root {
    --sidebar-width: 260px;
}

.sidebar {
    width: var(--sidebar-width) !important;
    min-width: var(--sidebar-width) !important;
    max-width: var(--sidebar-width) !important;
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    z-index: 1050;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    box-shadow: 2px 0 10px rgba(0,0,0,0.08);
    border-left: none;
    border-right: none;
    direction: rtl;
}

body.rtl .sidebar {
    right: 0;
    left: auto;
}

.main-content {
    margin-right: var(--sidebar-width);
    transition: margin 0.3s cubic-bezier(.4,0,.2,1);
}

@media (max-width: 992px) {
    .sidebar {
        right: -260px;
        box-shadow: none;
        transition: right 0.3s cubic-bezier(.4,0,.2,1);
    }
    .sidebar.show {
        right: 0;
        box-shadow: 2px 0 10px rgba(0,0,0,0.08);
    }
    .main-content {
        margin-right: 0 !important;
    }
}

/* زر إظهار/إخفاء القائمة الجانبية للشاشات الصغيرة */
.menu-toggle {
    display: none;
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 1100;
    background: #fff;
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    color: #4361ee;
    font-size: 1.5rem;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
@media (max-width: 992px) {
    .menu-toggle {
        display: flex;
    }
}

/* إصلاح تراكب الشريط الجانبي فوق الصفحة */
body.sidebar-open {
    overflow: hidden;
}
body.sidebar-open .sidebar {
    right: 0 !important;
}
body.sidebar-open .main-content {
    pointer-events: none;
    filter: blur(2px) grayscale(0.1);
}

/* لون الخط في الشريط الجانبي أبيض متدرج */
.sidebar, .sidebar * {
    color: #ffffff !important;
}

.sidebar .sidebar-link, 
.sidebar .sidebar-brand, 
.sidebar .sidebar-menu, 
.sidebar .nav-item,
.sidebar .nav-link,
.sidebar .sidebar-header,
.sidebar ul,
.sidebar li,
.sidebar a {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.sidebar .nav-item:hover .nav-link,
.sidebar .nav-link:hover,
.sidebar .sidebar-link:hover,
.sidebar .sidebar-link.active {
    color: #ffe082 !important;
    background: rgba(255,255,255,0.08) !important;
    border-radius: 8px;
}

.sidebar .sidebar-link.disabled, 
.sidebar .sidebar-link[aria-disabled="true"],
.sidebar .nav-link.disabled {
    color: rgba(255,255,255,0.5) !important;
} 