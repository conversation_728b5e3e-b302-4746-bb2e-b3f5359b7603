/* Trust Plus - Dashboard Design Fixes */
/* إصلاحات تصميم لوحة التحكم */

/* إصلاح القائمة الجانبية */
.sidebar {
    position: fixed;
    right: 0;
    top: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background: var(--primary-gradient);
    color: white;
    z-index: 1000;
    padding: 0;
    transition: var(--transition);
}

.sidebar-brand {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand img {
    height: 35px;
}

.sidebar-menu {
    padding: 1rem 0;
}

.sidebar-link {
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

.sidebar-link:hover,
.sidebar-link.active {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-link i {
    width: 24px;
    margin-left: 0.75rem;
    font-size: 1.25rem;
}

/* إصلاح المحتوى الرئيسي */
.main-content {
    margin-right: var(--sidebar-width);
    padding: 2rem;
    min-height: 100vh;
    background: #f5f7fb;
}

/* إصلاح شريط البحث */
.search-bar {
    background: white;
    border-radius: 10px;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
}

.search-input {
    border: none;
    background: none;
    padding: 0.5rem;
    width: 100%;
    color: var(--dark-color);
    font-size: 0.95rem;
}

.search-input:focus {
    outline: none;
}

.search-input::placeholder {
    color: #6c757d;
}

/* إصلاح رأس الصفحة */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 0;
    background: transparent;
    box-shadow: none;
}

.header-title h1 {
    font-size: 1.75rem;
    color: var(--dark-color);
    margin: 0;
    font-weight: 700;
}

.header-subtitle {
    color: #6c757d;
    margin-top: 0.25rem;
    font-size: 0.95rem;
}

/* إصلاح البطاقات الإحصائية */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    position: relative;
    overflow: hidden;
    border: none;
}

.stat-icon {
    margin-left: 1rem;
    margin-bottom: 0;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

/* إصلاح الإشعارات */
.notifications {
    position: fixed;
    left: 1rem;
    bottom: 1rem;
    z-index: 1000;
}

.notification {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 0.5rem;
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* إصلاح الجداول */
.data-table {
    width: 100%;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.data-table th {
    background: var(--primary-gradient);
    color: white;
    font-weight: 600;
    padding: 1rem 1.5rem;
    text-align: right;
}

.data-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f0f0f0;
    color: var(--dark-color);
}

.data-table tbody tr:hover {
    background: rgba(67, 97, 238, 0.05);
}

/* إصلاح النماذج */
.form-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--card-shadow);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* إصلاحات للشاشات الصغيرة */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .search-bar {
        margin-top: 1rem;
    }
    
    .data-table {
        display: block;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* إصلاح زر القائمة للشاشات الصغيرة */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--dark-color);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
}

@media (max-width: 992px) {
    .menu-toggle {
        display: block;
    }
} 