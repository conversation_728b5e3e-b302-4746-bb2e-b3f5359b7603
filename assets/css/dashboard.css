/* Trust Plus - Dashboard Specific CSS */
/* ملف CSS خاص بلوحة التحكم */

/* استيراد التحسينات */
@import url('modern-dashboard.css');
@import url('dashboard-improvements.css');
@import url('dashboard-fixes.css');

/* تخصيص إضافي للتصميم */
:root {
    /* تخصيص الألوان حسب هوية Trust Plus */
    --bs-primary: #4361ee;
    --bs-secondary: #3f37c9;
    --bs-success: #4caf50;
    --bs-info: #4cc9f0;
    --bs-warning: #ff9e00;
    --bs-danger: #f72585;
    
    /* تحديث التدرجات اللونية */
    --bs-primary-rgb: 67, 97, 238;
    --bs-secondary-rgb: 63, 55, 201;
    
    /* المتغيرات الأساسية */
    --sidebar-width: 280px;
    --header-height: 70px;
    --footer-height: 60px;
}

/* تخصيص القائمة الجانبية */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
}

.sidebar-brand {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid rgba(255, 255, 255, .1);
}

/* تخصيص المحتوى الرئيسي */
.main-content {
    min-height: calc(100vh - var(--header-height) - var(--footer-height));
    margin-right: var(--sidebar-width);
    padding: 2rem;
    background: #f5f7fb;
}

/* تخصيص الجداول */
.data-table {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    overflow: hidden;
}

.data-table thead th {
    background: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: 1rem;
    color: var(--bs-dark);
}

/* تخصيص الأزرار */
.btn-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(67,97,238,0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--bs-secondary) 0%, var(--bs-primary) 100%);
    transform: translateY(-2px);
}

/* تخصيص حالات KYC */
.kyc-status {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.kyc-status.pending {
    background: rgba(255,193,7,0.1);
    color: #ffc107;
}

.kyc-status.approved {
    background: rgba(25,135,84,0.1);
    color: #198754;
}

.kyc-status.rejected {
    background: rgba(220,53,69,0.1);
    color: #dc3545;
}

/* تخصيص مستوى المخاطر */
.risk-level {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.risk-level.high {
    background: rgba(220,53,69,0.1);
    color: #dc3545;
}

.risk-level.medium {
    background: rgba(255,193,7,0.1);
    color: #ffc107;
}

.risk-level.low {
    background: rgba(25,135,84,0.1);
    color: #198754;
}

/* تخصيص أزرار الإجراءات */
.action-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: #f8f9fa;
    color: #6c757d;
    border: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.action-button:hover {
    background: var(--bs-primary);
    color: white;
    transform: translateY(-2px);
}

/* تخصيص شريط البحث */
.search-bar {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.search-input {
    flex: 1;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.25rem rgba(67,97,238,0.1);
}

/* تخصيص الفلاتر */
.filter-dropdown {
    position: relative;
}

.filter-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: #f8f9fa;
    border: none;
    border-radius: 8px;
    color: #6c757d;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-button:hover {
    background: #e9ecef;
}

.filter-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    padding: 0.5rem;
    min-width: 200px;
    z-index: 1000;
}

.filter-item {
    padding: 0.75rem 1rem;
    color: #6c757d;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.filter-item:hover {
    background: #f8f9fa;
    color: var(--bs-primary);
}

/* تخصيص الإحصائيات */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-box {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.stat-box::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to left, var(--bs-primary), var(--bs-secondary));
}

/* تخصيص التنبيهات */
.alert-box {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
}

.alert-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--bs-dark);
    margin-bottom: 1rem;
}

.alert-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.alert-icon.warning {
    background: rgba(255,193,7,0.1);
    color: #ffc107;
}

.alert-icon.danger {
    background: rgba(220,53,69,0.1);
    color: #dc3545;
}

.alert-content {
    flex: 1;
}

.alert-message {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

/* تخصيص التذييل */
.footer {
    background: white;
    padding: 1.5rem 2rem;
    border-top: 1px solid rgba(0,0,0,0.05);
    margin-right: var(--sidebar-width);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 992px) {
    :root {
        --sidebar-width: 0;
    }
    
    .sidebar {
        transform: translateX(280px);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content,
    .footer {
        margin-right: 0;
    }
    
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
    }
    
    .search-bar {
        flex-direction: column;
    }
    
    .filter-dropdown {
        width: 100%;
    }
}
