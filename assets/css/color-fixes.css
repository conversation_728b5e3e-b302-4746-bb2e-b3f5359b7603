/* Trust Plus - Color Fixes CSS */
/* ملف CSS لإصلاح مشاكل الألوان */

/* إعادة تعريف الألوان الأساسية */
:root {
    /* الألوان الأساسية */
    --primary-color: #667eea !important;
    --secondary-color: #764ba2 !important;
    --success-color: #28a745 !important;
    --danger-color: #dc3545 !important;
    --warning-color: #ffc107 !important;
    --info-color: #17a2b8 !important;
    --light-color: #f8f9fa !important;
    --dark-color: #343a40 !important;
    
    /* التدرجات اللونية */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    --secondary-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
    
    /* الخصائص الأخرى */
    --border-radius: 15px !important;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08) !important;
    --transition: all 0.3s ease !important;
}

/* إصلاح فئات التدرجات */
.bg-primary-gradient,
.stat-icon.bg-primary-gradient,
.activity-icon.bg-primary-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

.bg-success-gradient,
.stat-icon.bg-success-gradient,
.activity-icon.bg-success-gradient {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    color: white !important;
}

.bg-warning-gradient,
.stat-icon.bg-warning-gradient,
.activity-icon.bg-warning-gradient {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    color: white !important;
}

.bg-info-gradient,
.stat-icon.bg-info-gradient,
.activity-icon.bg-info-gradient {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    color: white !important;
}

.bg-secondary-gradient,
.stat-icon.bg-secondary-gradient,
.activity-icon.bg-secondary-gradient {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
    color: white !important;
}

.bg-danger-gradient,
.stat-icon.bg-danger-gradient,
.activity-icon.bg-danger-gradient {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
    color: white !important;
}

/* إصلاح ألوان النصوص */
.text-primary {
    color: #667eea !important;
}

.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-secondary {
    color: #6c757d !important;
}

/* إصلاح ألوان الخلفيات */
.bg-primary {
    background-color: #667eea !important;
    color: white !important;
}

.bg-success {
    background-color: #28a745 !important;
    color: white !important;
}

.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
    color: white !important;
}

.bg-danger {
    background-color: #dc3545 !important;
    color: white !important;
}

.bg-light {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

.bg-dark {
    background-color: #343a40 !important;
    color: white !important;
}

/* إصلاح ألوان الحدود */
.border-primary {
    border-color: #667eea !important;
}

.border-success {
    border-color: #28a745 !important;
}

.border-warning {
    border-color: #ffc107 !important;
}

.border-info {
    border-color: #17a2b8 !important;
}

.border-danger {
    border-color: #dc3545 !important;
}

/* إصلاح ألوان الأزرار */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    color: white !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
    border: none !important;
    color: white !important;
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    border: none !important;
    color: white !important;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    border: none !important;
    color: white !important;
}

.btn-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border: none !important;
    color: white !important;
}

.btn-danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
    border: none !important;
    color: white !important;
}

/* إصلاح ألوان الأزرار المحددة */
.btn-outline-primary {
    color: #667eea !important;
    border-color: #667eea !important;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-color: #667eea !important;
    color: white !important;
}

.btn-outline-success {
    color: #28a745 !important;
    border-color: #28a745 !important;
}

.btn-outline-success:hover {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    border-color: #28a745 !important;
    color: white !important;
}

/* إصلاح ألوان التنبيهات */
.alert-primary {
    background-color: rgba(102, 126, 234, 0.1) !important;
    border-color: rgba(102, 126, 234, 0.2) !important;
    color: #667eea !important;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-color: rgba(40, 167, 69, 0.2) !important;
    color: #28a745 !important;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-color: rgba(255, 193, 7, 0.2) !important;
    color: #856404 !important;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
    border-color: rgba(23, 162, 184, 0.2) !important;
    color: #17a2b8 !important;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.2) !important;
    color: #dc3545 !important;
}

/* إصلاح ألوان الشارات */
.badge-primary {
    background-color: #667eea !important;
    color: white !important;
}

.badge-success {
    background-color: #28a745 !important;
    color: white !important;
}

.badge-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge-info {
    background-color: #17a2b8 !important;
    color: white !important;
}

.badge-danger {
    background-color: #dc3545 !important;
    color: white !important;
}

/* إصلاح ألوان شريط التقدم */
.progress-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.progress-bar-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
}

.progress-bar-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.progress-bar-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.progress-bar-danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
}

/* إصلاح ألوان الروابط */
a {
    color: #667eea !important;
    text-decoration: none !important;
}

a:hover,
a:focus {
    color: #5a6fd8 !important;
    text-decoration: underline !important;
}

/* إصلاح ألوان النماذج */
.form-control:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}

.form-select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}

/* إصلاح ألوان الجداول */
.table-primary {
    background-color: rgba(102, 126, 234, 0.1) !important;
}

.table-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

.table-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
}

.table-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
}

/* إصلاح ألوان خاصة بالنظام */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* إصلاح ألوان الأيقونات */
.fas, .far, .fab {
    color: inherit !important;
}

/* إصلاح ألوان الحالات */
.stat-change.positive {
    color: #28a745 !important;
}

.stat-change.negative {
    color: #dc3545 !important;
}

/* إصلاح ألوان الخلفيات الشفافة */
.bg-light-primary {
    background-color: rgba(102, 126, 234, 0.1) !important;
}

.bg-light-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.bg-light-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

.bg-light-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
}

.bg-light-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
}
