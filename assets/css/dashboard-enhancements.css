/* Trust Plus - Dashboard Enhancements CSS */
/* تحسينات تصميم لوحة التحكم */

:root {
    /* الألوان الأساسية المحسنة */
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --success-color: #4caf50;
    --danger-color: #f72585;
    --warning-color: #ff9e00;
    --info-color: #4cc9f0;
    --light-color: #f8f9fa;
    --dark-color: #2b2d42;
    
    /* التدرجات اللونية المحسنة */
    --primary-gradient: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    --success-gradient: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    --warning-gradient: linear-gradient(135deg, #ff9e00 0%, #ff7300 100%);
    --info-gradient: linear-gradient(135deg, #4cc9f0 0%, #00b4d8 100%);
    --danger-gradient: linear-gradient(135deg, #f72585 0%, #b5179e 100%);
    
    /* الظلال والتأثيرات */
    --card-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 8px 35px 0 rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
}

/* تحسينات عامة */
body {
    background: #f5f7fb;
    font-family: 'Cairo', sans-serif;
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

/* تحسين رأس لوحة التحكم */
.dashboard-header {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
}

.dashboard-title {
    color: var(--dark-color);
    font-weight: 700;
    margin: 0;
    font-size: 1.75rem;
}

/* تحسين بطاقات الإحصائيات */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.stat-label {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 0.75rem;
}

/* تحسين الجداول */
.table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #f0f0f0;
}

/* تحسين الأزرار */
.btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
}

.btn-success {
    background: var(--success-gradient);
    border: none;
}

.btn-warning {
    background: var(--warning-gradient);
    border: none;
    color: white;
}

.btn-danger {
    background: var(--danger-gradient);
    border: none;
}

.btn-info {
    background: var(--info-gradient);
    border: none;
    color: white;
}

/* تحسين القوائم الجانبية */
.sidebar {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
}

.sidebar-link {
    color: var(--dark-color);
    padding: 0.75rem 1rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: var(--transition);
}

.sidebar-link:hover,
.sidebar-link.active {
    background: var(--primary-gradient);
    color: white;
    text-decoration: none;
}

.sidebar-link i {
    font-size: 1.25rem;
}

/* تحسين النماذج */
.form-control {
    border-radius: 10px;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.5rem;
}

.alert-primary {
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.alert-success {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.alert-warning {
    background: rgba(255, 158, 0, 0.1);
    color: var(--warning-color);
}

.alert-danger {
    background: rgba(247, 37, 133, 0.1);
    color: var(--danger-color);
}

/* تحسين الشارات */
.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-weight: 600;
}

.badge-primary {
    background: var(--primary-gradient);
}

.badge-success {
    background: var(--success-gradient);
}

.badge-warning {
    background: var(--warning-gradient);
    color: white;
}

.badge-danger {
    background: var(--danger-gradient);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .dashboard-header {
        padding: 1rem;
    }
    
    .table-responsive {
        border-radius: 15px;
        overflow: hidden;
    }
    
    .sidebar {
        margin-bottom: 1rem;
    }
} 