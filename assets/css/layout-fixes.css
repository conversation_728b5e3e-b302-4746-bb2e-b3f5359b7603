/* Trust Plus - Layout Fixes CSS */
/* ملف CSS لإصلاح مشاكل التخطيط */

/* إعادة تعريف المتغيرات للتأكد من توفرها */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --secondary-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    
    --border-radius: 15px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}

/* إصلاح التخطيط العام */
.app-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* إصلاح فئات التدرجات */
.bg-primary-gradient {
    background: var(--primary-gradient) !important;
}

.bg-success-gradient {
    background: var(--success-gradient) !important;
}

.bg-warning-gradient {
    background: var(--warning-gradient) !important;
}

.bg-info-gradient {
    background: var(--info-gradient) !important;
}

.bg-secondary-gradient {
    background: var(--secondary-gradient) !important;
}

.bg-danger-gradient {
    background: var(--danger-gradient) !important;
}

/* إصلاح الشريط الجانبي المحسن */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background: var(--primary-gradient);
    z-index: 1000;
    overflow-y: auto;
    overflow-x: hidden;
    transition: all 0.3s ease;
    box-shadow: -2px 0 15px rgba(0,0,0,0.1);
    border-left: 1px solid rgba(255,255,255,0.1);
}

/* رأس الشريط الجانبي */
.sidebar-header {
    position: sticky;
    top: 0;
    background: rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    z-index: 10;
}

.sidebar-header h5 {
    font-weight: 700;
    font-size: 1.1rem;
    letter-spacing: 0.5px;
}

.sidebar-header i {
    font-size: 1.3rem;
    color: rgba(255,255,255,0.9);
}

/* قائمة التنقل */
.sidebar .nav {
    padding: 1rem 0;
    margin: 0;
    list-style: none;
}

.sidebar .nav-item {
    margin: 0.25rem 0;
}

.sidebar .nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.5rem;
    color: rgba(255, 255, 255, 0.85);
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: right;
    position: relative;
    font-weight: 500;
    border-radius: 0 25px 25px 0;
    margin-left: 0.5rem;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    background: white;
    border-radius: 0 15px 15px 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover::before,
.sidebar .nav-link.active::before {
    width: 4px;
    height: 60%;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateX(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.sidebar .nav-link i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-left: 0.875rem;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border-radius: 6px;
    background: rgba(255,255,255,0.1);
}

.sidebar .nav-link:hover i,
.sidebar .nav-link.active i {
    background: rgba(255,255,255,0.2);
    transform: scale(1.1);
}

/* نص الشريط الجانبي */
.sidebar-text {
    transition: all 0.3s ease;
    font-size: 0.95rem;
    font-weight: 500;
}

/* الشريط الجانبي المطوي */
.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed .sidebar-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
    margin: 0;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.875rem;
    margin-left: 0;
    border-radius: 0;
}

.sidebar.collapsed .nav-link i {
    margin: 0;
}

/* تذييل الشريط الجانبي */
.sidebar-footer {
    position: sticky;
    bottom: 0;
    background: rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    margin-top: auto;
}

.sidebar-footer small {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* إصلاح المحتوى الرئيسي المحسن */
.main-content {
    flex: 1;
    margin-right: var(--sidebar-width);
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    transition: all 0.3s ease;
    position: relative;
}

.main-content.no-sidebar {
    margin-right: 0;
}

.main-content.sidebar-collapsed {
    margin-right: var(--sidebar-collapsed-width);
}

/* شريط التنقل العلوي */
.navbar {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.navbar-nav .nav-link {
    color: var(--dark-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

/* إصلاح محتوى الصفحة */
.page-content {
    padding: 2rem;
    min-height: calc(100vh - 140px);
}

/* التذييل المحسن */
.page-footer {
    background: white;
    border-top: 1px solid rgba(0,0,0,0.05);
    padding: 1.5rem 2rem;
    margin-top: auto;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

.page-footer p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* زر تبديل الشريط الجانبي */
.sidebar-toggle {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1001;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-gradient);
    border: none;
    color: white;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    display: none;
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* إصلاح بطاقات الإحصائيات */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* إصلاح الإجراءات السريعة */
.quick-actions {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    min-height: 120px;
    text-align: center;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
    text-decoration: none;
}

.action-btn i {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    display: block;
}

.action-btn span {
    font-weight: 600;
    font-size: 0.9rem;
    line-height: 1.2;
}

/* إصلاح الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.chart-canvas {
    position: relative;
    height: 300px;
    width: 100%;
}

/* إصلاح الأنشطة الأخيرة */
.recent-activities {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    height: fit-content;
}

.activities-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: white;
    margin-left: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.activity-time {
    font-size: 0.8rem;
    color: #6c757d;
}

/* التصميم المتجاوب المحسن */
@media (max-width: 1400px) {
    .action-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 1200px) {
    .main-content {
        padding: 1.5rem;
    }

    .page-content {
        padding: 1.5rem;
    }

    .action-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 1rem;
    }

    .stat-value {
        font-size: 2rem;
    }
}

@media (max-width: 992px) {
    /* الشريط الجانبي في الأجهزة اللوحية */
    .sidebar {
        transform: translateX(100%);
        width: 320px;
        box-shadow: -5px 0 20px rgba(0,0,0,0.3);
        border-left: none;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0 !important;
    }

    .sidebar-toggle {
        display: flex !important;
    }

    /* تحسين البطاقات */
    .stat-card {
        text-align: center;
        padding: 1.25rem;
    }

    .stat-icon {
        margin: 0 auto 1rem auto;
    }

    /* تحسين الإجراءات السريعة */
    .action-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 1rem;
    }

    .action-btn {
        min-height: 120px;
        padding: 1.5rem 1rem;
    }

    .action-btn i {
        font-size: 2rem;
    }

    /* تحسين شريط التنقل */
    .navbar {
        padding: 0.75rem 1.5rem;
    }

    .page-header {
        padding: 1.25rem;
        text-align: center;
    }
}

@media (max-width: 768px) {
    /* الشريط الجانبي في الهواتف */
    .sidebar {
        width: 100%;
        border-radius: 0;
    }

    .page-content {
        padding: 1rem;
    }

    /* تحسين البطاقات للهواتف */
    .stat-card {
        margin-bottom: 1rem;
    }

    .stat-value {
        font-size: 1.75rem;
    }

    /* تحسين الإجراءات السريعة */
    .action-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .action-btn {
        min-height: 100px;
        padding: 1.25rem 0.75rem;
    }

    .action-btn i {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }

    .action-btn span {
        font-size: 0.85rem;
    }

    /* تحسين الرسوم البيانية */
    .chart-canvas {
        height: 250px;
    }

    /* تحسين التذييل */
    .page-footer {
        padding: 1rem;
        text-align: center;
    }

    .page-footer .row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .page-footer .col-md-6 {
        text-align: center !important;
    }
}

@media (max-width: 576px) {
    /* تحسينات للهواتف الصغيرة */
    .page-content {
        padding: 0.75rem;
    }

    /* البطاقات */
    .stat-card {
        padding: 1rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.85rem;
    }

    /* الإجراءات السريعة */
    .action-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .action-btn {
        min-height: 80px;
        padding: 1rem;
        flex-direction: row;
        justify-content: flex-start;
        text-align: right;
    }

    .action-btn i {
        font-size: 1.5rem;
        margin-bottom: 0;
        margin-left: 1rem;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        background: rgba(102, 126, 234, 0.1);
    }

    .action-btn span {
        font-size: 0.9rem;
        flex: 1;
    }

    /* الأنشطة */
    .activity-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
        gap: 0.75rem;
    }

    .activity-icon {
        margin: 0;
        align-self: center;
    }

    /* الرسوم البيانية */
    .chart-canvas {
        height: 200px;
    }

    /* رأس الصفحة */
    .page-header {
        padding: 1rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .page-subtitle {
        font-size: 0.85rem;
    }

    /* شريط التنقل */
    .navbar {
        padding: 0.5rem 1rem;
    }

    .navbar-brand {
        font-size: 1rem;
    }
}
