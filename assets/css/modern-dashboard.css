/* Trust Plus - Modern Dashboard Design */
/* تصميم حديث للوحة التحكم */

:root {
    /* الألوان الرئيسية الجديدة */
    --bs-primary: #0d6efd;
    --bs-secondary: #6c757d;
    --bs-success: #198754;
    --bs-info: #0dcaf0;
    --bs-warning: #ffc107;
    --bs-danger: #dc3545;
    --bs-light: #f8f9fa;
    --bs-dark: #212529;
    
    /* تدرجات لونية جديدة */
    --bs-primary-rgb: 13, 110, 253;
    --bs-secondary-rgb: 108, 117, 125;
    --bs-success-rgb: 25, 135, 84;
    --bs-info-rgb: 13, 202, 240;
    --bs-warning-rgb: 255, 193, 7;
    --bs-danger-rgb: 220, 53, 69;
    
    /* الظلال والتأثيرات */
    --bs-box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
    --bs-box-shadow-sm: 0 .125rem .25rem rgba(0, 0, 0, .075);
    --bs-box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, .175);
}

/* تحديث القائمة الجانبية */
.sidebar {
    background: var(--bs-dark);
    border-right: 1px solid rgba(255, 255, 255, .1);
}

.sidebar-brand {
    background: rgba(255, 255, 255, .05);
    padding: 1rem 1.5rem;
}

.sidebar-link {
    color: rgba(255, 255, 255, .8);
    padding: .75rem 1.5rem;
    font-size: .9rem;
    border-radius: 0;
    border-right: 3px solid transparent;
}

.sidebar-link:hover,
.sidebar-link.active {
    color: #fff;
    background: rgba(255, 255, 255, .1);
    border-right-color: var(--bs-primary);
}

.sidebar-link i {
    width: 1.5rem;
    font-size: 1.1rem;
    margin-left: .75rem;
    color: rgba(255, 255, 255, .6);
}

/* تحديث المحتوى الرئيسي */
.main-content {
    background: var(--bs-light);
}

/* تحديث شريط البحث */
.search-bar {
    background: #fff;
    border: 1px solid rgba(0, 0, 0, .1);
    border-radius: .375rem;
    box-shadow: var(--bs-box-shadow-sm);
}

.search-input {
    font-size: .875rem;
}

.search-input::placeholder {
    color: var(--bs-secondary);
}

/* تحديث البطاقات الإحصائية */
.stat-card {
    background: #fff;
    border: 0;
    border-radius: .375rem;
    box-shadow: var(--bs-box-shadow-sm);
    transition: transform .2s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--bs-box-shadow);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: .375rem;
    background: rgba(var(--bs-primary-rgb), .1);
    color: var(--bs-primary);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--bs-dark);
}

.stat-label {
    font-size: .875rem;
    color: var(--bs-secondary);
}

/* تحديث الجداول */
.table {
    --bs-table-bg: transparent;
    --bs-table-striped-bg: rgba(0, 0, 0, .02);
    --bs-table-hover-bg: rgba(0, 0, 0, .03);
    margin-bottom: 0;
}

.table thead th {
    background: var(--bs-light);
    font-weight: 600;
    text-transform: uppercase;
    font-size: .75rem;
    letter-spacing: .5px;
    padding: 1rem;
    border-bottom: 2px solid rgba(0, 0, 0, .05);
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0, 0, 0, .05);
}

/* تحديث الأزرار */
.btn {
    --bs-btn-padding-x: 1rem;
    --bs-btn-padding-y: .5rem;
    --bs-btn-font-size: .875rem;
    --bs-btn-font-weight: 500;
    --bs-btn-border-radius: .375rem;
    --bs-btn-transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.btn-primary {
    --bs-btn-color: #fff;
    --bs-btn-bg: var(--bs-primary);
    --bs-btn-border-color: var(--bs-primary);
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #0b5ed7;
    --bs-btn-hover-border-color: #0a58ca;
}

/* تحديث النماذج */
.form-control {
    --bs-form-control-bg: #fff;
    --bs-form-control-disabled-bg: #e9ecef;
    --bs-form-control-border-color: #dee2e6;
    --bs-form-control-border-radius: .375rem;
    padding: .5rem .75rem;
    font-size: .875rem;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 .25rem rgba(13, 110, 253, .25);
}

/* تحديث البطاقات */
.card {
    --bs-card-border-radius: .375rem;
    --bs-card-box-shadow: var(--bs-box-shadow-sm);
    border: 0;
    box-shadow: var(--bs-card-box-shadow);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, .05);
    padding: 1rem 1.25rem;
}

/* تحديث الشارات */
.badge {
    --bs-badge-padding-x: .65em;
    --bs-badge-padding-y: .35em;
    --bs-badge-font-size: .75em;
    --bs-badge-font-weight: 500;
    --bs-badge-border-radius: .375rem;
}

/* تحديث التنبيهات */
.alert {
    --bs-alert-border-radius: .375rem;
    --bs-alert-padding-x: 1rem;
    --bs-alert-padding-y: .75rem;
    border: 0;
    box-shadow: var(--bs-box-shadow-sm);
}

/* تحديث القوائم */
.list-group {
    --bs-list-group-border-radius: .375rem;
    --bs-list-group-item-padding-x: 1rem;
    --bs-list-group-item-padding-y: .75rem;
}

.list-group-item {
    border-left: 0;
    border-right: 0;
    border-color: rgba(0, 0, 0, .05);
}

/* تحديث التذييل */
.footer {
    padding: 1rem 0;
    font-size: .875rem;
    color: var(--bs-secondary);
    border-top: 1px solid rgba(0, 0, 0, .05);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 992px) {
    .sidebar {
        margin-right: -100%;
    }
    
    .sidebar.show {
        margin-right: 0;
    }
    
    .main-content {
        margin-right: 0;
    }
}

/* تحديث الرموز والأيقونات */
.icon-shape {
    width: 3rem;
    height: 3rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: .375rem;
}

.icon-shape i {
    font-size: 1.25rem;
    color: #fff;
}

/* تحديث المخططات البيانية */
.chart-container {
    position: relative;
    margin: auto;
    height: 300px;
    background: #fff;
    border-radius: .375rem;
    padding: 1rem;
    box-shadow: var(--bs-box-shadow-sm);
}

/* تحديث لوحات المعلومات */
.info-card {
    background: #fff;
    border-radius: .375rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--bs-box-shadow-sm);
}

.info-card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--bs-dark);
}

/* تحديث شريط التقدم */
.progress {
    --bs-progress-height: .5rem;
    --bs-progress-border-radius: 1rem;
    background-color: rgba(0, 0, 0, .05);
}

.progress-bar {
    background-color: var(--bs-primary);
} 