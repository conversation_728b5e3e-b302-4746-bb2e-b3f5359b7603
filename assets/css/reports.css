/* Trust Plus - Reports Specific CSS */
/* ملف CSS خاص بالتقارير */

/* التقارير الرئيسية */
.reports-container {
    padding: 2rem;
}

.report-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 1.5rem;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.report-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: var(--transition);
}

.report-card:hover::before {
    transform: scaleX(1);
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.report-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin: 0 auto 1rem;
}

.report-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.report-description {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.4;
}

/* نافذة إعدادات التقرير */
.report-modal .modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.report-modal .modal-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: none;
}

.report-filters {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-group:last-child {
    margin-bottom: 0;
}

/* عرض التقرير */
.report-content {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.report-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.report-title-main {
    font-size: 1.75rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.report-subtitle {
    color: #6c757d;
    font-size: 1rem;
}

.report-period {
    background: var(--primary-gradient);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    display: inline-block;
    margin-top: 0.5rem;
}

/* جداول التقارير */
.report-table {
    margin-bottom: 2rem;
}

.report-table .table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.report-table .table thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.report-table .table tbody td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
}

.report-table .table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.table-total {
    background: #f8f9fa !important;
    font-weight: bold;
}

.table-subtotal {
    background: #e9ecef !important;
    font-weight: 600;
}

/* بطاقات الملخص */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.summary-card.revenue::before {
    background: var(--success-gradient);
}

.summary-card.expense::before {
    background: var(--danger-gradient);
}

.summary-card.profit::before {
    background: var(--primary-gradient);
}

.summary-card.margin::before {
    background: var(--info-gradient);
}

.summary-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.summary-value.positive {
    color: var(--success-color);
}

.summary-value.negative {
    color: var(--danger-color);
}

.summary-label {
    color: #6c757d;
    font-size: 0.9rem;
}

/* الرسوم البيانية في التقارير */
.chart-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-canvas {
    position: relative;
    height: 400px;
}

/* التقارير المحفوظة */
.saved-reports {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.saved-report-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    transition: var(--transition);
}

.saved-report-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.saved-report-item:last-child {
    margin-bottom: 0;
}

.report-item-header {
    display: flex;
    justify-content: between;
    align-items: start;
    margin-bottom: 0.5rem;
}

.report-item-title {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.report-type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    background: var(--primary-color);
    color: white;
}

.report-item-meta {
    font-size: 0.8rem;
    color: #6c757d;
}

/* أزرار التقارير */
.report-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.report-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: none;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.report-btn.primary {
    background: var(--primary-color);
    color: white;
}

.report-btn.success {
    background: var(--success-color);
    color: white;
}

.report-btn.info {
    background: var(--info-color);
    color: white;
}

.report-btn.secondary {
    background: #6c757d;
    color: white;
}

.report-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* مؤشرات التحميل */
.report-loading {
    text-align: center;
    padding: 3rem;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.loading-text {
    color: #6c757d;
    font-size: 1rem;
}

/* حالات التقرير */
.report-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.report-status.generating {
    background: #fff3cd;
    color: #856404;
}

.report-status.completed {
    background: #d4edda;
    color: #155724;
}

.report-status.failed {
    background: #f8d7da;
    color: #721c24;
}

/* طباعة التقارير */
@media print {
    .report-actions,
    .report-filters,
    .no-print {
        display: none !important;
    }
    
    .report-content {
        box-shadow: none;
        margin: 0;
        padding: 1rem;
    }
    
    .report-card {
        break-inside: avoid;
    }
    
    .chart-section {
        break-inside: avoid;
    }
}

/* التصميم المتجاوب للتقارير */
@media (max-width: 768px) {
    .reports-container {
        padding: 1rem;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .chart-controls {
        margin-top: 1rem;
    }
    
    .report-actions {
        flex-direction: column;
    }
    
    .report-item-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .report-type-badge {
        margin-top: 0.5rem;
    }
}

@media (max-width: 576px) {
    .report-content {
        padding: 1rem;
    }
    
    .chart-canvas {
        height: 250px;
    }
    
    .summary-value {
        font-size: 1.5rem;
    }
    
    .report-table .table {
        font-size: 0.9rem;
    }
}
