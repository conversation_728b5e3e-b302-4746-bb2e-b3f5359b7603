/* تنسيقات صفحة المساعدة - Trust Plus */

/* البطاقة الرئيسية للبحث */
.help-search-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    color: white;
}

.help-search-card .form-control {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.help-search-card .input-group-text {
    background: white;
    border: none;
    border-radius: 10px 0 0 10px;
    color: #667eea;
    font-size: 1.2rem;
}

.help-quick-stats .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* شريط التنقل الجانبي */
.help-navigation {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    position: sticky;
    top: 2rem;
}

.help-navigation .list-group-item {
    border: none;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
    border-radius: 0;
}

.help-navigation .list-group-item:first-child {
    border-radius: 0 0 0 0;
}

.help-navigation .list-group-item:last-child {
    border-radius: 0 0 15px 15px;
}

.help-navigation .list-group-item:hover {
    background: #f8f9fa;
    transform: translateX(-3px);
    box-shadow: 3px 0 10px rgba(0,0,0,0.1);
}

.help-navigation .list-group-item.active {
    background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    color: white;
    border-color: transparent;
}

.help-navigation .list-group-item.active:hover {
    background: linear-gradient(135deg, #3f37c9 0%, #4361ee 100%);
    transform: translateX(-5px);
}

.help-navigation .list-group-item i {
    width: 20px;
    text-align: center;
}

/* محتوى المساعدة */
.help-content {
    min-height: 600px;
}

.help-content .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.help-content .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.12);
}

.help-content .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0 !important;
    padding: 1.5rem;
}

.help-content .card-body {
    padding: 2rem;
}

/* بطاقات الوحدات */
.module-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.module-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.module-card .card-body {
    padding: 2rem;
}

.module-card i {
    transition: all 0.3s ease;
}

.module-card:hover i {
    transform: scale(1.1);
}

/* خطوات البدء السريع */
.step-card {
    border: none;
    border-radius: 12px;
    border-right: 4px solid #4361ee;
    box-shadow: 0 3px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.step-card:hover {
    transform: translateX(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    color: white;
    border-radius: 50%;
    font-weight: bold;
    font-size: 1.1rem;
    margin-left: 15px;
    box-shadow: 0 3px 10px rgba(67, 97, 238, 0.3);
}

/* الأسئلة الشائعة */
.accordion-item {
    border: none;
    border-radius: 10px !important;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.accordion-button {
    border-radius: 10px !important;
    border: none;
    background: #f8f9fa;
    color: #2b2d42;
    font-weight: 500;
    padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
    background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.accordion-body {
    padding: 1.5rem;
    background: white;
    border-radius: 0 0 10px 10px;
}

/* تمييز نتائج البحث */
.search-highlight {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
    animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
    0% { background: #fff3cd; }
    50% { background: #ffeaa7; }
    100% { background: #fff3cd; }
}

/* أزرار الفئات */
.btn-group .btn {
    border-radius: 20px;
    margin: 0 2px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-group .btn.active {
    background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    border-color: #4361ee;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

/* صفحة الاتصال */
.contact-info .contact-item {
    padding: 1rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.contact-info .contact-item:hover {
    background: #f8f9fa;
    transform: translateX(-3px);
}

.contact-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 50%;
    margin-left: 1rem;
    font-size: 1.3rem;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.support-type-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.support-type-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.support-type-card .card-body {
    padding: 2rem 1.5rem;
}

.support-type-card i {
    transition: all 0.3s ease;
}

.support-type-card:hover i {
    transform: scale(1.1) rotate(5deg);
}

/* مستوى الخدمة */
.sla-item {
    padding: 1.5rem;
    border-radius: 12px;
    background: white;
    margin: 0.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.sla-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.12);
}

.sla-item i {
    transition: all 0.3s ease;
}

.sla-item:hover i {
    transform: scale(1.2);
}

/* نماذج الإدخال */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* الأزرار */
.btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    border: none;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
    border-color: #4361ee;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 12px;
    padding: 1.25rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

/* الشارات */
.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

/* التصميم المتجاوب */
@media (max-width: 992px) {
    .help-navigation {
        position: static;
        margin-bottom: 2rem;
    }
    
    .help-search-card .row {
        text-align: center;
    }
    
    .help-quick-stats {
        margin-top: 1rem;
    }
    
    .help-quick-stats .badge {
        display: block;
        margin: 0.25rem 0;
    }
}

@media (max-width: 768px) {
    .help-content .card-body {
        padding: 1.5rem;
    }
    
    .module-card .card-body {
        padding: 1.5rem;
    }
    
    .step-number {
        width: 30px;
        height: 30px;
        font-size: 1rem;
        margin-left: 10px;
    }
    
    .contact-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }
    
    .sla-item {
        padding: 1rem;
        margin: 0.25rem;
    }
    
    .btn-group .btn {
        font-size: 0.875rem;
        padding: 0.4rem 1rem;
        margin: 2px;
    }
}

/* تأثيرات إضافية */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* تحسينات إمكانية الوصول */
.help-navigation .list-group-item:focus {
    outline: 2px solid #4361ee;
    outline-offset: 2px;
}

.btn:focus {
    outline: 2px solid #4361ee;
    outline-offset: 2px;
}

.form-control:focus, .form-select:focus {
    outline: none;
}

/* طباعة */
@media print {
    .help-navigation,
    .help-search-card,
    .btn,
    .alert {
        display: none !important;
    }
    
    .help-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
