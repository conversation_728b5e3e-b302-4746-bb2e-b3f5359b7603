/**
 * Trust Plus - Error Handler JavaScript
 * ملف JavaScript لمعالجة الأخطاء
 */

// معالج الأخطاء العام
window.addEventListener('error', function(event) {
    console.warn('JavaScript Error caught:', event.error);
    // لا نعرض الأخطاء للمستخدم لتجنب الإزعاج
});

// معالج الأخطاء غير المعالجة
window.addEventListener('unhandledrejection', function(event) {
    console.warn('Unhandled Promise Rejection:', event.reason);
    // منع عرض الخطأ في وحدة التحكم
    event.preventDefault();
});

// معالج أخطاء Chrome Extension
if (typeof chrome !== 'undefined' && chrome.runtime) {
    // تجاهل أخطاء Chrome Extension
    const originalError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');
        if (message.includes('runtime.lastError') || 
            message.includes('message channel closed') ||
            message.includes('No tab with id')) {
            // تجاهل هذه الأخطاء
            return;
        }
        originalError.apply(console, args);
    };
}

// تحسين معالجة Fetch API
const originalFetch = window.fetch;
window.fetch = function(...args) {
    return originalFetch.apply(this, args)
        .catch(error => {
            // معالجة أخطاء الشبكة بصمت
            console.warn('Network error handled:', error.message);
            return Promise.reject(error);
        });
};

// تحسين معالجة AJAX
if (typeof $ !== 'undefined') {
    $(document).ajaxError(function(event, xhr, settings, thrownError) {
        console.warn('AJAX Error handled:', thrownError);
    });
}

// معالج أخطاء Bootstrap
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود Bootstrap
    if (typeof bootstrap === 'undefined') {
        console.warn('Bootstrap not loaded, using fallback methods');
        
        // إنشاء بدائل بسيطة لـ Bootstrap
        window.bootstrap = {
            Modal: function(element) {
                return {
                    show: function() {
                        if (element) {
                            element.style.display = 'block';
                            element.classList.add('show');
                        }
                    },
                    hide: function() {
                        if (element) {
                            element.style.display = 'none';
                            element.classList.remove('show');
                        }
                    }
                };
            },
            Tooltip: function(element) {
                return {
                    show: function() { /* fallback */ },
                    hide: function() { /* fallback */ }
                };
            },
            Dropdown: function(element) {
                return {
                    show: function() { /* fallback */ },
                    hide: function() { /* fallback */ }
                };
            }
        };
    }
    
    // معالجة أخطاء الصور
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.addEventListener('error', function() {
            this.style.display = 'none';
        });
    });
    
    // معالجة أخطاء الخطوط
    if (document.fonts) {
        document.fonts.ready.then(function() {
            console.log('Fonts loaded successfully');
        }).catch(function(error) {
            console.warn('Font loading error:', error);
        });
    }
});

// تحسين أداء الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير تحميل الصور غير المرئية
    const lazyImages = document.querySelectorAll('img[data-src]');
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const image = entry.target;
                    image.src = image.dataset.src;
                    image.classList.remove('lazy');
                    imageObserver.unobserve(image);
                }
            });
        });
        
        lazyImages.forEach(function(image) {
            imageObserver.observe(image);
        });
    } else {
        // Fallback للمتصفحات القديمة
        lazyImages.forEach(function(image) {
            image.src = image.dataset.src;
        });
    }
});

// تحسين معالجة الأحداث
function safeEventListener(element, event, handler) {
    if (element && typeof handler === 'function') {
        try {
            element.addEventListener(event, handler);
        } catch (error) {
            console.warn('Event listener error:', error);
        }
    }
}

// تحسين معالجة DOM
function safeQuerySelector(selector) {
    try {
        return document.querySelector(selector);
    } catch (error) {
        console.warn('Query selector error:', error);
        return null;
    }
}

function safeQuerySelectorAll(selector) {
    try {
        return document.querySelectorAll(selector);
    } catch (error) {
        console.warn('Query selector all error:', error);
        return [];
    }
}

// تحسين معالجة Local Storage
const SafeStorage = {
    set: function(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.warn('LocalStorage set error:', error);
            return false;
        }
    },
    
    get: function(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.warn('LocalStorage get error:', error);
            return defaultValue;
        }
    },
    
    remove: function(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.warn('LocalStorage remove error:', error);
            return false;
        }
    }
};

// تحسين معالجة الطلبات
async function safeApiCall(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.warn('API call error:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// تحسين معالجة النماذج
function safeFormSubmit(form, handler) {
    if (form && typeof handler === 'function') {
        safeEventListener(form, 'submit', function(event) {
            event.preventDefault();
            try {
                handler(event);
            } catch (error) {
                console.warn('Form submit error:', error);
            }
        });
    }
}

// تحسين معالجة الرسوم البيانية
function safeChartInit(canvasId, config) {
    const canvas = safeQuerySelector(canvasId);
    if (!canvas) {
        console.warn('Chart canvas not found:', canvasId);
        return null;
    }
    
    try {
        if (typeof Chart !== 'undefined') {
            return new Chart(canvas, config);
        } else {
            console.warn('Chart.js not loaded, creating placeholder');
            createPlaceholderChart(canvas.parentElement);
            return null;
        }
    } catch (error) {
        console.warn('Chart initialization error:', error);
        createPlaceholderChart(canvas.parentElement);
        return null;
    }
}

// إنشاء رسم بياني بديل
function createPlaceholderChart(container) {
    if (!container) return;
    
    const placeholder = document.createElement('div');
    placeholder.className = 'chart-placeholder';
    placeholder.style.cssText = `
        width: 100%;
        height: 300px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 1.1rem;
        font-weight: 600;
        border: 2px dashed #dee2e6;
    `;
    placeholder.innerHTML = '<i class="fas fa-chart-line me-2"></i>رسم بياني تجريبي';
    
    // إزالة أي محتوى موجود
    container.innerHTML = '';
    container.appendChild(placeholder);
}

// تصدير الدوال للاستخدام العام
window.TrustPlusErrorHandler = {
    safeEventListener,
    safeQuerySelector,
    safeQuerySelectorAll,
    SafeStorage,
    safeApiCall,
    safeFormSubmit,
    safeChartInit,
    createPlaceholderChart
};

console.log('Trust Plus Error Handler loaded successfully');
