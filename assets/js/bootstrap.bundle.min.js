/* Bootstrap 5.3.0 JavaScript Bundle - يتم تحميله من CDN أو ملف محلي */
/* هذا ملف placeholder - يجب استبداله بملف Bootstrap JavaScript الفعلي */

/* يمكن تحميل Bootstrap JS من: https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js */
/* أو تحميل الملف المضغوط من موقع Bootstrap الرسمي */

/* للاستخدام المحلي، قم بنسخ محتوى Bootstrap JavaScript هنا */

// تحميل Bootstrap من CDN كبديل
if (typeof bootstrap === 'undefined') {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js';
    script.async = true;
    document.head.appendChild(script);
}
