/**
 * Trust Plus - Main JavaScript File
 * ملف JavaScript الرئيسي لنظام Trust Plus
 */

// متغيرات عامة
const TrustPlus = {
    // إعدادات النظام
    settings: {
        language: 'ar',
        direction: 'rtl',
        currency: 'USD',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: '24h'
    },
    
    // حالة النظام
    state: {
        currentUser: null,
        currentPage: null,
        sidebarCollapsed: false,
        notifications: []
    },
    
    // دوال مساعدة
    utils: {},
    
    // دوال واجهة المستخدم
    ui: {},
    
    // دوال API
    api: {},
    
    // دوال التحقق
    validation: {}
};

/**
 * دوال مساعدة عامة
 */
TrustPlus.utils = {
    /**
     * تنسيق الأرقام
     */
    formatNumber: function(number, decimals = 2) {
        if (isNaN(number)) return '0.00';
        return parseFloat(number).toLocaleString('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },

    /**
     * تنسيق العملة
     */
    formatCurrency: function(amount, currency = 'USD', decimals = 2) {
        if (isNaN(amount)) return '$0.00';
        
        const symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'SAR': 'ر.س',
            'AED': 'د.إ',
            'EGP': 'ج.م'
        };
        
        const symbol = symbols[currency] || currency;
        const formatted = this.formatNumber(amount, decimals);
        
        return `${symbol}${formatted}`;
    },

    /**
     * تنسيق التاريخ
     */
    formatDate: function(date, format = 'YYYY-MM-DD') {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        
        switch (format) {
            case 'YYYY-MM-DD':
                return `${year}-${month}-${day}`;
            case 'DD/MM/YYYY':
                return `${day}/${month}/${year}`;
            case 'YYYY-MM-DD HH:mm':
                return `${year}-${month}-${day} ${hours}:${minutes}`;
            default:
                return d.toLocaleDateString('ar-SA');
        }
    },

    /**
     * تنسيق الوقت النسبي
     */
    timeAgo: function(date) {
        if (!date) return '';
        
        const now = new Date();
        const past = new Date(date);
        const diffMs = now - past;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffMins < 1) return 'الآن';
        if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
        if (diffHours < 24) return `منذ ${diffHours} ساعة`;
        if (diffDays < 7) return `منذ ${diffDays} يوم`;
        
        return this.formatDate(date);
    },

    /**
     * إنشاء معرف فريد
     */
    generateId: function() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    /**
     * نسخ النص للحافظة
     */
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showToast('تم نسخ النص', 'success');
            });
        } else {
            // fallback للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showToast('تم نسخ النص', 'success');
        }
    },

    /**
     * تحويل البيانات إلى CSV
     */
    exportToCSV: function(data, filename = 'export.csv') {
        if (!data || !data.length) return;
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    },

    /**
     * عرض رسالة تنبيه
     */
    showToast: function(message, type = 'info', duration = 3000) {
        // التحقق من وجود Bootstrap
        if (typeof bootstrap === 'undefined' || !bootstrap.Toast) {
            // استخدام alert كبديل
            alert(message);
            return;
        }

        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        const container = document.getElementById('toast-container') || this.createToastContainer();
        container.appendChild(toast);

        try {
            const bsToast = new bootstrap.Toast(toast, { delay: duration });
            bsToast.show();
        } catch (error) {
            console.error('Toast error:', error);
            // إزالة Toast وعرض alert كبديل
            container.removeChild(toast);
            alert(message);
        }
        
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    },

    /**
     * إنشاء حاوية التنبيهات
     */
    createToastContainer: function() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    }
};

/**
 * دوال واجهة المستخدم
 */
TrustPlus.ui = {
    /**
     * تبديل الشريط الجانبي
     */
    toggleSidebar: function() {
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar && mainContent) {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            TrustPlus.state.sidebarCollapsed = sidebar.classList.contains('collapsed');
            
            // حفظ الحالة في localStorage
            localStorage.setItem('sidebarCollapsed', TrustPlus.state.sidebarCollapsed);
        }
    },

    /**
     * استعادة حالة الشريط الجانبي
     */
    restoreSidebarState: function() {
        const collapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        if (collapsed) {
            this.toggleSidebar();
        }
    },

    /**
     * إظهار مؤشر التحميل
     */
    showLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.classList.add('loading');
            element.style.pointerEvents = 'none';
        }
    },

    /**
     * إخفاء مؤشر التحميل
     */
    hideLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            element.classList.remove('loading');
            element.style.pointerEvents = '';
        }
    },

    /**
     * تأكيد الحذف
     */
    confirmDelete: function(message = 'هل أنت متأكد من الحذف؟') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تأكيد الحذف</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
            
            modal.querySelector('#confirmDeleteBtn').addEventListener('click', () => {
                bsModal.hide();
                resolve(true);
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
                resolve(false);
            });
        });
    },

    /**
     * تحديث شريط التقدم
     */
    updateProgress: function(element, percentage) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (element) {
            const progressBar = element.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.width = `${percentage}%`;
                progressBar.setAttribute('aria-valuenow', percentage);
                progressBar.textContent = `${Math.round(percentage)}%`;
            }
        }
    },

    /**
     * تحديث العداد
     */
    animateCounter: function(element, targetValue, duration = 1000) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (!element) return;
        
        const startValue = 0;
        const increment = targetValue / (duration / 16);
        let currentValue = startValue;
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= targetValue) {
                currentValue = targetValue;
                clearInterval(timer);
            }
            element.textContent = TrustPlus.utils.formatNumber(currentValue, 0);
        }, 16);
    }
};

/**
 * دوال API
 */
TrustPlus.api = {
    /**
     * إرسال طلب AJAX
     */
    request: function(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const config = { ...defaults, ...options };
        
        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('API Error:', error);
                TrustPlus.utils.showToast('خطأ في الاتصال بالخادم', 'danger');
                throw error;
            });
    },

    /**
     * طلب GET
     */
    get: function(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = urlParams.toString() ? `${url}?${urlParams}` : url;
        return this.request(fullUrl);
    },

    /**
     * طلب POST
     */
    post: function(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },

    /**
     * طلب PUT
     */
    put: function(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },

    /**
     * طلب DELETE
     */
    delete: function(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
};

/**
 * دوال التحقق
 */
TrustPlus.validation = {
    /**
     * التحقق من البريد الإلكتروني
     */
    isValidEmail: function(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    },

    /**
     * التحقق من رقم الهاتف
     */
    isValidPhone: function(phone) {
        const regex = /^[\+]?[1-9][\d]{0,15}$/;
        return regex.test(phone.replace(/\s/g, ''));
    },

    /**
     * التحقق من قوة كلمة المرور
     */
    checkPasswordStrength: function(password) {
        const checks = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            number: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };
        
        const score = Object.values(checks).filter(Boolean).length;
        
        return {
            score: score,
            strength: score < 3 ? 'ضعيف' : score < 4 ? 'متوسط' : 'قوي',
            checks: checks
        };
    },

    /**
     * التحقق من صحة النموذج
     */
    validateForm: function(form) {
        if (typeof form === 'string') {
            form = document.querySelector(form);
        }
        
        if (!form) return false;
        
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                this.showFieldError(input, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(input);
            }
        });
        
        return isValid;
    },

    /**
     * إظهار خطأ في الحقل
     */
    showFieldError: function(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('is-invalid');
        
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = message;
        
        field.parentNode.appendChild(feedback);
    },

    /**
     * إزالة خطأ الحقل
     */
    clearFieldError: function(field) {
        field.classList.remove('is-invalid');
        
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }
};

/**
 * تهيئة النظام عند تحميل الصفحة
 */
document.addEventListener('DOMContentLoaded', function() {
    // استعادة حالة الشريط الجانبي
    TrustPlus.ui.restoreSidebarState();
    
    // إضافة مستمعي الأحداث
    const sidebarToggle = document.querySelector('[data-sidebar-toggle]');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', TrustPlus.ui.toggleSidebar);
    }
    
    // تفعيل tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // إخفاء التنبيهات تلقائياً
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(alert => {
            if (alert.classList.contains('fade')) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            } else {
                alert.remove();
            }
        });
    }, 5000);
    
    console.log('Trust Plus System Initialized');
});

// تصدير للاستخدام العام
window.TrustPlus = TrustPlus;
