/**
 * Trust Plus - Reports JavaScript
 * ملف JavaScript خاص بالتقارير
 */

const Reports = {
    // بيانات التقارير
    data: {
        currentReport: null,
        savedReports: [],
        filters: {}
    },
    
    // الرسوم البيانية
    charts: {},
    
    /**
     * تهيئة نظام التقارير
     */
    init: function() {
        this.bindEvents();
        this.loadSavedReports();
        this.initDatePickers();
        
        console.log('Reports system initialized');
    },
    
    /**
     * ربط الأحداث
     */
    bindEvents: function() {
        // فتح نافذة إعدادات التقرير
        document.querySelectorAll('.report-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const reportType = e.currentTarget.dataset.reportType;
                if (reportType) {
                    this.openReportModal(reportType);
                }
            });
        });
        
        // إنشاء التقرير
        const generateBtn = document.querySelector('#generateReportBtn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateReport();
            });
        }
        
        // تصدير التقرير
        const exportBtn = document.querySelector('#exportReportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportReport();
            });
        }
        
        // طباعة التقرير
        const printBtn = document.querySelector('#printReportBtn');
        if (printBtn) {
            printBtn.addEventListener('click', () => {
                this.printReport();
            });
        }
        
        // حفظ التقرير
        const saveBtn = document.querySelector('#saveReportBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveReport();
            });
        }
    },
    
    /**
     * تهيئة منتقي التواريخ
     */
    initDatePickers: function() {
        // تعيين التواريخ الافتراضية
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        
        const dateFromInput = document.querySelector('#dateFrom');
        const dateToInput = document.querySelector('#dateTo');
        
        if (dateFromInput) {
            dateFromInput.value = firstDayOfMonth.toISOString().split('T')[0];
        }
        
        if (dateToInput) {
            dateToInput.value = today.toISOString().split('T')[0];
        }
    },
    
    /**
     * فتح نافذة إعدادات التقرير
     */
    openReportModal: function(reportType) {
        const reportTitles = {
            'profit_loss': 'تقرير الأرباح والخسائر',
            'balance_sheet': 'الميزانية العمومية',
            'cash_flow': 'تقرير التدفق النقدي',
            'trial_balance': 'ميزان المراجعة',
            'operations_summary': 'ملخص العمليات',
            'compliance_report': 'تقرير الامتثال'
        };
        
        const modal = document.querySelector('#reportModal');
        const titleElement = modal.querySelector('#reportModalTitle');
        const typeInput = modal.querySelector('#reportType');
        
        if (titleElement) {
            titleElement.textContent = reportTitles[reportType] || 'إنشاء تقرير';
        }
        
        if (typeInput) {
            typeInput.value = reportType;
        }
        
        // إظهار/إخفاء حقول حسب نوع التقرير
        this.toggleReportFields(reportType);
        
        // إظهار النافذة
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    },
    
    /**
     * تبديل حقول التقرير حسب النوع
     */
    toggleReportFields: function(reportType) {
        const periodFields = document.querySelector('.period-fields');
        const snapshotFields = document.querySelector('.snapshot-fields');
        
        const periodReports = ['profit_loss', 'cash_flow', 'operations_summary'];
        const snapshotReports = ['balance_sheet', 'trial_balance'];
        
        if (periodFields && snapshotFields) {
            if (periodReports.includes(reportType)) {
                periodFields.style.display = 'block';
                snapshotFields.style.display = 'none';
            } else if (snapshotReports.includes(reportType)) {
                periodFields.style.display = 'none';
                snapshotFields.style.display = 'block';
            } else {
                periodFields.style.display = 'block';
                snapshotFields.style.display = 'none';
            }
        }
    },
    
    /**
     * إنشاء التقرير
     */
    generateReport: function() {
        const form = document.querySelector('#reportForm');
        if (!form) return;
        
        const formData = new FormData(form);
        const reportType = formData.get('report_type');
        
        if (!reportType) {
            TrustPlus.utils.showToast('يرجى اختيار نوع التقرير', 'warning');
            return;
        }
        
        // إظهار مؤشر التحميل
        const generateBtn = document.querySelector('#generateReportBtn');
        const originalText = generateBtn.innerHTML;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
        generateBtn.disabled = true;
        
        // إرسال الطلب
        fetch('generate_report.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.data.currentReport = data.data;
                this.displayReport(reportType, data.data);
                
                // إخفاء نافذة الإعدادات وإظهار نافذة العرض
                bootstrap.Modal.getInstance(document.querySelector('#reportModal')).hide();
                
                const viewModal = new bootstrap.Modal(document.querySelector('#reportViewModal'));
                viewModal.show();
                
                TrustPlus.utils.showToast('تم إنشاء التقرير بنجاح', 'success');
            } else {
                TrustPlus.utils.showToast('خطأ في إنشاء التقرير: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            TrustPlus.utils.showToast('خطأ في الاتصال بالخادم', 'danger');
        })
        .finally(() => {
            generateBtn.innerHTML = originalText;
            generateBtn.disabled = false;
        });
    },
    
    /**
     * عرض التقرير
     */
    displayReport: function(reportType, data) {
        const reportTitles = {
            'profit_loss': 'تقرير الأرباح والخسائر',
            'balance_sheet': 'الميزانية العمومية',
            'cash_flow': 'تقرير التدفق النقدي',
            'trial_balance': 'ميزان المراجعة',
            'operations_summary': 'ملخص العمليات',
            'compliance_report': 'تقرير الامتثال'
        };
        
        const titleElement = document.querySelector('#reportViewTitle');
        if (titleElement) {
            titleElement.textContent = reportTitles[reportType] || 'عرض التقرير';
        }
        
        let content = '';
        
        switch (reportType) {
            case 'profit_loss':
                content = this.generateProfitLossHTML(data);
                break;
            case 'balance_sheet':
                content = this.generateBalanceSheetHTML(data);
                break;
            case 'cash_flow':
                content = this.generateCashFlowHTML(data);
                break;
            case 'trial_balance':
                content = this.generateTrialBalanceHTML(data);
                break;
            case 'operations_summary':
                content = this.generateOperationsSummaryHTML(data);
                break;
            case 'compliance_report':
                content = this.generateComplianceReportHTML(data);
                break;
            default:
                content = '<div class="alert alert-warning">نوع التقرير غير مدعوم</div>';
        }
        
        const contentElement = document.querySelector('#reportContent');
        if (contentElement) {
            contentElement.innerHTML = content;
        }
        
        // إنشاء الرسوم البيانية إذا كانت متاحة
        this.initReportCharts(reportType, data);
    },
    
    /**
     * إنشاء HTML لتقرير الأرباح والخسائر
     */
    generateProfitLossHTML: function(data) {
        return `
            <div class="report-header text-center mb-4">
                <h3>تقرير الأرباح والخسائر</h3>
                <p class="text-muted">من ${data.period.from} إلى ${data.period.to}</p>
            </div>
            
            <div class="summary-cards">
                <div class="summary-card revenue">
                    <div class="summary-value positive">$${TrustPlus.utils.formatNumber(data.total_revenue)}</div>
                    <div class="summary-label">إجمالي الإيرادات</div>
                </div>
                <div class="summary-card expense">
                    <div class="summary-value negative">$${TrustPlus.utils.formatNumber(data.total_expenses)}</div>
                    <div class="summary-label">إجمالي المصروفات</div>
                </div>
                <div class="summary-card profit">
                    <div class="summary-value ${data.net_profit >= 0 ? 'positive' : 'negative'}">$${TrustPlus.utils.formatNumber(data.net_profit)}</div>
                    <div class="summary-label">الربح الصافي</div>
                </div>
                <div class="summary-card margin">
                    <div class="summary-value">${TrustPlus.utils.formatNumber(data.profit_margin)}%</div>
                    <div class="summary-label">هامش الربح</div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="report-table">
                        <h5 class="text-success mb-3">الإيرادات</h5>
                        <table class="table">
                            <tbody>
                                <tr><td>عمولات الصرافة</td><td class="text-end">$${TrustPlus.utils.formatNumber(data.revenue.exchange_commission)}</td></tr>
                                <tr><td>أرباح فوارق الصرف</td><td class="text-end">$${TrustPlus.utils.formatNumber(data.revenue.exchange_spread)}</td></tr>
                                <tr><td>رسوم التحويلات</td><td class="text-end">$${TrustPlus.utils.formatNumber(data.revenue.transfer_fees)}</td></tr>
                                <tr><td>أرباح صرف التحويلات</td><td class="text-end">$${TrustPlus.utils.formatNumber(data.revenue.transfer_exchange_profit)}</td></tr>
                                <tr><td>إيرادات أخرى</td><td class="text-end">$${TrustPlus.utils.formatNumber(data.revenue.other_revenue)}</td></tr>
                                <tr class="table-total"><th>إجمالي الإيرادات</th><th class="text-end">$${TrustPlus.utils.formatNumber(data.total_revenue)}</th></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="report-table">
                        <h5 class="text-danger mb-3">المصروفات</h5>
                        <table class="table">
                            <tbody>
                                <tr><td>مصروفات تشغيلية</td><td class="text-end">$${TrustPlus.utils.formatNumber(data.expenses.operational_expenses)}</td></tr>
                                <tr><td>مصروفات إدارية</td><td class="text-end">$${TrustPlus.utils.formatNumber(data.expenses.administrative_expenses)}</td></tr>
                                <tr><td>مصروفات أخرى</td><td class="text-end">$${TrustPlus.utils.formatNumber(data.expenses.other_expenses)}</td></tr>
                                <tr class="table-total"><th>إجمالي المصروفات</th><th class="text-end">$${TrustPlus.utils.formatNumber(data.total_expenses)}</th></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="chart-section">
                <canvas id="profitLossChart" width="400" height="200"></canvas>
            </div>
        `;
    },
    
    /**
     * إنشاء HTML للميزانية العمومية
     */
    generateBalanceSheetHTML: function(data) {
        let assetsHTML = data.assets.map(asset => 
            `<tr><td>${asset.account_code}</td><td>${asset.account_name}</td><td class="text-end">$${TrustPlus.utils.formatNumber(asset.balance)}</td></tr>`
        ).join('');
        
        let liabilitiesHTML = data.liabilities.map(liability => 
            `<tr><td>${liability.account_code}</td><td>${liability.account_name}</td><td class="text-end">$${TrustPlus.utils.formatNumber(liability.balance)}</td></tr>`
        ).join('');
        
        let equityHTML = data.equity.map(equity => 
            `<tr><td>${equity.account_code}</td><td>${equity.account_name}</td><td class="text-end">$${TrustPlus.utils.formatNumber(equity.balance)}</td></tr>`
        ).join('');
        
        return `
            <div class="report-header text-center mb-4">
                <h3>الميزانية العمومية</h3>
                <p class="text-muted">كما في ${data.as_of_date}</p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="report-table">
                        <h5 class="text-primary mb-3">الأصول</h5>
                        <table class="table">
                            <thead><tr><th>الرمز</th><th>اسم الحساب</th><th class="text-end">الرصيد</th></tr></thead>
                            <tbody>
                                ${assetsHTML}
                                <tr class="table-total"><th colspan="2">إجمالي الأصول</th><th class="text-end">$${TrustPlus.utils.formatNumber(data.totals.assets)}</th></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="report-table">
                        <h5 class="text-warning mb-3">الخصوم</h5>
                        <table class="table">
                            <thead><tr><th>الرمز</th><th>اسم الحساب</th><th class="text-end">الرصيد</th></tr></thead>
                            <tbody>
                                ${liabilitiesHTML}
                                <tr class="table-subtotal"><th colspan="2">إجمالي الخصوم</th><th class="text-end">$${TrustPlus.utils.formatNumber(data.totals.liabilities)}</th></tr>
                            </tbody>
                        </table>
                        
                        <h5 class="text-success mt-4 mb-3">حقوق الملكية</h5>
                        <table class="table">
                            <thead><tr><th>الرمز</th><th>اسم الحساب</th><th class="text-end">الرصيد</th></tr></thead>
                            <tbody>
                                ${equityHTML}
                                <tr class="table-subtotal"><th colspan="2">إجمالي حقوق الملكية</th><th class="text-end">$${TrustPlus.utils.formatNumber(data.totals.equity)}</th></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="alert ${data.balanced ? 'alert-success' : 'alert-danger'} mt-4">
                <h5><strong>إجمالي الخصوم وحقوق الملكية: $${TrustPlus.utils.formatNumber(data.total_liabilities_equity)}</strong></h5>
                <p class="mb-0">${data.balanced ? 'الميزانية متوازنة ✓' : 'الميزانية غير متوازنة ✗'}</p>
            </div>
        `;
    },
    
    /**
     * تهيئة الرسوم البيانية للتقارير
     */
    initReportCharts: function(reportType, data) {
        if (reportType === 'profit_loss') {
            this.initProfitLossChart(data);
        }
    },
    
    /**
     * رسم بياني لتقرير الأرباح والخسائر
     */
    initProfitLossChart: function(data) {
        const ctx = document.getElementById('profitLossChart');
        if (!ctx) return;
        
        this.charts.profitLoss = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['الإيرادات', 'المصروفات', 'الربح الصافي'],
                datasets: [{
                    data: [data.total_revenue, data.total_expenses, data.net_profit],
                    backgroundColor: ['#28a745', '#dc3545', '#667eea'],
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return TrustPlus.utils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    },
    
    /**
     * تصدير التقرير
     */
    exportReport: function() {
        if (!this.data.currentReport) {
            TrustPlus.utils.showToast('لا يوجد تقرير لتصديره', 'warning');
            return;
        }
        
        // يمكن تطوير هذه الدالة لتصدير التقرير بصيغ مختلفة
        TrustPlus.utils.showToast('ميزة التصدير قيد التطوير', 'info');
    },
    
    /**
     * طباعة التقرير
     */
    printReport: function() {
        window.print();
    },
    
    /**
     * حفظ التقرير
     */
    saveReport: function() {
        if (!this.data.currentReport) {
            TrustPlus.utils.showToast('لا يوجد تقرير لحفظه', 'warning');
            return;
        }
        
        // يمكن تطوير هذه الدالة لحفظ التقرير
        TrustPlus.utils.showToast('ميزة حفظ التقرير قيد التطوير', 'info');
    },
    
    /**
     * تحميل التقارير المحفوظة
     */
    loadSavedReports: function() {
        TrustPlus.api.get('api/saved_reports.php')
            .then(response => {
                if (response.success) {
                    this.data.savedReports = response.data;
                    this.updateSavedReportsList();
                }
            })
            .catch(error => {
                console.error('Error loading saved reports:', error);
            });
    },
    
    /**
     * تحديث قائمة التقارير المحفوظة
     */
    updateSavedReportsList: function() {
        const container = document.querySelector('.saved-reports-list');
        if (!container) return;
        
        container.innerHTML = '';
        
        this.data.savedReports.forEach(report => {
            const item = document.createElement('div');
            item.className = 'saved-report-item';
            item.innerHTML = `
                <div class="report-item-header">
                    <div>
                        <div class="report-item-title">${report.report_name}</div>
                        <span class="report-type-badge">${this.getReportTypeLabel(report.report_type)}</span>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="Reports.viewSavedReport(${report.id})">
                                <i class="fas fa-eye me-2"></i>عرض
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="Reports.downloadSavedReport(${report.id})">
                                <i class="fas fa-download me-2"></i>تحميل
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="Reports.deleteSavedReport(${report.id})">
                                <i class="fas fa-trash me-2"></i>حذف
                            </a></li>
                        </ul>
                    </div>
                </div>
                <div class="report-item-meta">
                    <div>بواسطة: ${report.generated_by_name}</div>
                    <div>${TrustPlus.utils.formatDate(report.generated_at, 'YYYY-MM-DD HH:mm')}</div>
                </div>
            `;
            container.appendChild(item);
        });
    },
    
    /**
     * الحصول على تسمية نوع التقرير
     */
    getReportTypeLabel: function(type) {
        const labels = {
            'profit_loss': 'أرباح وخسائر',
            'balance_sheet': 'ميزانية عمومية',
            'cash_flow': 'تدفق نقدي',
            'trial_balance': 'ميزان مراجعة',
            'operations_summary': 'ملخص عمليات',
            'compliance_report': 'تقرير امتثال'
        };
        return labels[type] || type;
    },
    
    /**
     * عرض تقرير محفوظ
     */
    viewSavedReport: function(reportId) {
        TrustPlus.utils.showToast('ميزة عرض التقرير المحفوظ قيد التطوير', 'info');
    },
    
    /**
     * تحميل تقرير محفوظ
     */
    downloadSavedReport: function(reportId) {
        TrustPlus.utils.showToast('ميزة تحميل التقرير قيد التطوير', 'info');
    },
    
    /**
     * حذف تقرير محفوظ
     */
    deleteSavedReport: function(reportId) {
        TrustPlus.ui.confirmDelete('هل أنت متأكد من حذف هذا التقرير؟')
            .then(confirmed => {
                if (confirmed) {
                    TrustPlus.utils.showToast('ميزة حذف التقرير قيد التطوير', 'info');
                }
            });
    },
    
    /**
     * تنظيف الموارد
     */
    destroy: function() {
        // تدمير الرسوم البيانية
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        
        this.charts = {};
        this.data = { currentReport: null, savedReports: [], filters: {} };
    }
};

// تهيئة نظام التقارير عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.reports-container')) {
        Reports.init();
    }
});

// تنظيف الموارد عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    if (typeof Reports !== 'undefined') {
        Reports.destroy();
    }
});

// تصدير للاستخدام العام
window.Reports = Reports;
