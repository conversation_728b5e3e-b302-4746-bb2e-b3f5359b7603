/**
 * JavaScript لصفحة المساعدة والدعم الفني
 * Trust Plus System
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeHelpPage();
});

/**
 * تهيئة صفحة المساعدة
 */
function initializeHelpPage() {
    initializeSearch();
    initializeNavigation();
    initializeFAQ();
    initializeContactForm();
    initializeVideoModals();
    initializeTooltips();
    initializeScrollSpy();
}

/**
 * تهيئة البحث في المساعدة
 */
function initializeSearch() {
    const searchInput = document.getElementById('helpSearch');
    if (!searchInput) return;

    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            clearSearchHighlights();
            return;
        }
        
        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, 300);
    });

    // البحث عند الضغط على Enter
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const query = this.value.trim();
            if (query.length >= 2) {
                performSearch(query);
            }
        }
    });
}

/**
 * تنفيذ البحث
 */
function performSearch(query) {
    const searchableElements = document.querySelectorAll(
        '.help-content h1, .help-content h2, .help-content h3, ' +
        '.help-content h4, .help-content h5, .help-content h6, ' +
        '.help-content p, .help-content li, .accordion-body'
    );
    
    let results = [];
    const queryLower = query.toLowerCase();
    
    // مسح التمييز السابق
    clearSearchHighlights();
    
    searchableElements.forEach(element => {
        const text = element.textContent.toLowerCase();
        if (text.includes(queryLower)) {
            results.push({
                element: element,
                text: element.textContent,
                relevance: calculateRelevance(text, queryLower)
            });
        }
    });
    
    // ترتيب النتائج حسب الصلة
    results.sort((a, b) => b.relevance - a.relevance);
    
    if (results.length > 0) {
        highlightSearchResults(results, query);
        scrollToFirstResult(results[0].element);
        showSearchStats(results.length, query);
    } else {
        showNoResultsMessage(query);
    }
}

/**
 * حساب مدى الصلة
 */
function calculateRelevance(text, query) {
    let relevance = 0;
    
    // تطابق كامل
    if (text.includes(query)) {
        relevance += 10;
    }
    
    // تطابق في بداية النص
    if (text.startsWith(query)) {
        relevance += 5;
    }
    
    // عدد مرات التكرار
    const matches = (text.match(new RegExp(query, 'g')) || []).length;
    relevance += matches * 2;
    
    // طول النص (النصوص الأقصر أكثر صلة)
    relevance += Math.max(0, 100 - text.length) / 10;
    
    return relevance;
}

/**
 * تمييز نتائج البحث
 */
function highlightSearchResults(results, query) {
    results.forEach(result => {
        const element = result.element;
        const originalText = element.textContent;
        const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
        
        // تمييز النص
        element.classList.add('search-highlight');
        
        // إضافة تأثير بصري
        element.style.transition = 'all 0.3s ease';
        element.style.transform = 'scale(1.02)';
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 300);
    });
}

/**
 * مسح تمييز البحث
 */
function clearSearchHighlights() {
    document.querySelectorAll('.search-highlight').forEach(element => {
        element.classList.remove('search-highlight');
        element.style.transform = '';
    });
}

/**
 * التمرير إلى أول نتيجة
 */
function scrollToFirstResult(element) {
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
    });
}

/**
 * عرض إحصائيات البحث
 */
function showSearchStats(count, query) {
    const existingStats = document.querySelector('.search-stats');
    if (existingStats) {
        existingStats.remove();
    }
    
    const statsDiv = document.createElement('div');
    statsDiv.className = 'alert alert-info search-stats fade-in';
    statsDiv.innerHTML = `
        <i class="fas fa-search me-2"></i>
        تم العثور على <strong>${count}</strong> نتيجة للبحث عن "<strong>${escapeHtml(query)}</strong>"
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    const helpContent = document.querySelector('.help-content');
    helpContent.insertBefore(statsDiv, helpContent.firstChild);
}

/**
 * عرض رسالة عدم وجود نتائج
 */
function showNoResultsMessage(query) {
    const existingStats = document.querySelector('.search-stats');
    if (existingStats) {
        existingStats.remove();
    }
    
    const noResultsDiv = document.createElement('div');
    noResultsDiv.className = 'alert alert-warning search-stats fade-in';
    noResultsDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        لم يتم العثور على نتائج للبحث عن "<strong>${escapeHtml(query)}</strong>"
        <br><small>جرب كلمات مختلفة أو تصفح الأقسام المختلفة</small>
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    const helpContent = document.querySelector('.help-content');
    helpContent.insertBefore(noResultsDiv, helpContent.firstChild);
}

/**
 * تهيئة التنقل
 */
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.help-navigation .list-group-item');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // إضافة تأثير التحميل
            if (this.href) {
                this.innerHTML += ' <i class="fas fa-spinner fa-spin"></i>';
            }
        });
    });
}

/**
 * تهيئة الأسئلة الشائعة
 */
function initializeFAQ() {
    // تبديل فئات الأسئلة
    const categoryButtons = document.querySelectorAll('[data-category]');
    const faqCategories = document.querySelectorAll('.faq-category');
    
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // تحديث الأزرار
            categoryButtons.forEach(btn => {
                btn.classList.remove('active');
                btn.style.transform = '';
            });
            this.classList.add('active');
            this.style.transform = 'translateY(-2px)';
            
            // إظهار/إخفاء الفئات مع تأثير
            faqCategories.forEach(cat => {
                if (cat.dataset.category === category) {
                    cat.classList.remove('d-none');
                    cat.classList.add('fade-in');
                } else {
                    cat.classList.add('d-none');
                    cat.classList.remove('fade-in');
                }
            });
        });
    });
    
    // تحسين الأكورديون
    const accordionButtons = document.querySelectorAll('.accordion-button');
    accordionButtons.forEach(button => {
        button.addEventListener('click', function() {
            // إضافة تأثير بصري
            setTimeout(() => {
                const target = document.querySelector(this.dataset.bsTarget);
                if (target && target.classList.contains('show')) {
                    target.style.animation = 'slideInRight 0.3s ease-out';
                }
            }, 100);
        });
    });
}

/**
 * تهيئة نموذج الاتصال
 */
function initializeContactForm() {
    const contactForm = document.getElementById('contactForm');
    if (!contactForm) return;
    
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validateContactForm()) {
            submitContactForm();
        }
    });
    
    // التحقق المباشر من الحقول
    const inputs = contactForm.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

/**
 * التحقق من صحة نموذج الاتصال
 */
function validateContactForm() {
    const form = document.getElementById('contactForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * التحقق من صحة حقل واحد
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // التحقق من الحقول المطلوبة
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب';
    }
    
    // التحقق من البريد الإلكتروني
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = 'يرجى إدخال بريد إلكتروني صحيح';
        }
    }
    
    // عرض/إخفاء رسالة الخطأ
    if (isValid) {
        clearFieldError(field);
    } else {
        showFieldError(field, errorMessage);
    }
    
    return isValid;
}

/**
 * عرض خطأ الحقل
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('is-invalid');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * مسح خطأ الحقل
 */
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * إرسال نموذج الاتصال
 */
function submitContactForm() {
    const form = document.getElementById('contactForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // تعطيل الزر وإظهار التحميل
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    
    // محاكاة الإرسال
    setTimeout(() => {
        // إظهار رسالة النجاح
        showSuccessMessage('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
        
        // إعادة تعيين النموذج
        form.reset();
        
        // إعادة تفعيل الزر
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
        
        // التمرير إلى أعلى الصفحة
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
    }, 2000);
}

/**
 * عرض رسالة النجاح
 */
function showSuccessMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * تهيئة النوافذ المنبثقة للفيديوهات
 */
function initializeVideoModals() {
    const videoLinks = document.querySelectorAll('.video-link');
    
    videoLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const videoUrl = this.dataset.video;
            if (videoUrl) {
                showVideoModal(videoUrl);
            }
        });
    });
}

/**
 * عرض نافذة الفيديو
 */
function showVideoModal(videoUrl) {
    const modal = document.getElementById('videoModal');
    const iframe = document.getElementById('videoFrame');
    
    if (modal && iframe) {
        iframe.src = videoUrl;
        
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // تنظيف الفيديو عند الإغلاق
        modal.addEventListener('hidden.bs.modal', function() {
            iframe.src = '';
        });
    }
}

/**
 * تهيئة التلميحات
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تهيئة مراقب التمرير
 */
function initializeScrollSpy() {
    const navLinks = document.querySelectorAll('.help-navigation .list-group-item');
    
    window.addEventListener('scroll', function() {
        const scrollPosition = window.scrollY + 100;
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.startsWith('#')) {
                const target = document.querySelector(href);
                if (target) {
                    const targetTop = target.offsetTop;
                    const targetBottom = targetTop + target.offsetHeight;
                    
                    if (scrollPosition >= targetTop && scrollPosition < targetBottom) {
                        navLinks.forEach(l => l.classList.remove('active'));
                        link.classList.add('active');
                    }
                }
            }
        });
    });
}

/**
 * دوال مساعدة
 */
function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * مفاتيح الاختصار
 */
document.addEventListener('keydown', function(e) {
    // Ctrl+F للبحث
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        const searchInput = document.getElementById('helpSearch');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }
    
    // Escape لمسح البحث
    if (e.key === 'Escape') {
        const searchInput = document.getElementById('helpSearch');
        if (searchInput && searchInput.value) {
            searchInput.value = '';
            clearSearchHighlights();
            
            const searchStats = document.querySelector('.search-stats');
            if (searchStats) {
                searchStats.remove();
            }
        }
    }
});

/**
 * تحسين الأداء - تأخير التحميل للصور
 */
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// تهيئة التحميل المتأخر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeLazyLoading);
