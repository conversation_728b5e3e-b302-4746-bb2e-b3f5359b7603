/**
 * Trust Plus - Dashboard JavaScript
 * ملف JavaScript خاص بلوحة التحكم
 */

const Dashboard = {
    // بيانات لوحة التحكم
    data: {
        stats: {},
        charts: {},
        activities: []
    },
    
    // الرسوم البيانية
    charts: {},
    
    // المؤقتات
    timers: {},
    
    /**
     * تهيئة لوحة التحكم
     */
    init: function() {
        this.loadStats();
        this.initCharts();
        this.loadRecentActivities();
        this.startAutoRefresh();
        this.bindEvents();
        
        console.log('Dashboard initialized');
    },
    
    /**
     * تحميل الإحصائيات
     */
    loadStats: function() {
        // التحقق من وجود TrustPlus.ui
        if (typeof TrustPlus !== 'undefined' && TrustPlus.ui) {
            TrustPlus.ui.showLoading('.quick-stats');
        }

        // تحديد المسار الصحيح
        let statsPath = 'api/dashboard_stats.php';
        if (window.location.pathname.includes('/dashboard/')) {
            statsPath = 'api/dashboard_stats.php';
        }

        // استخدام fetch مباشرة إذا لم يكن TrustPlus.api متاحاً
        const fetchStats = typeof TrustPlus !== 'undefined' && TrustPlus.api ?
            TrustPlus.api.get(statsPath) :
            fetch(statsPath, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            }).then(response => response.json());

        fetchStats
            .then(response => {
                if (response.success) {
                    this.updateStats(response.data);
                } else {
                    console.warn('Stats loading failed:', response.message);
                }
            })
            .catch(error => {
                console.error('Error loading stats:', error);
            })
            .finally(() => {
                if (typeof TrustPlus !== 'undefined' && TrustPlus.ui) {
                    TrustPlus.ui.hideLoading('.quick-stats');
                }
            });
    },
    
    /**
     * تحديث الإحصائيات
     */
    updateStats: function(stats) {
        this.data.stats = stats;

        // تحديث بطاقات الإحصائيات
        Object.keys(stats).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                const statData = stats[key];
                const value = typeof statData === 'object' ? statData.value : statData;
                const change = typeof statData === 'object' ? statData.change : 0;

                // تحديث القيمة
                const valueElement = element.querySelector('.stat-value');
                if (valueElement) {
                    // استخدام تأثير العداد إذا كان متاحاً
                    if (typeof TrustPlus !== 'undefined' && TrustPlus.ui && TrustPlus.ui.animateCounter) {
                        TrustPlus.ui.animateCounter(valueElement, value);
                    } else {
                        // تحديث مباشر
                        if (typeof value === 'number') {
                            valueElement.textContent = this.formatNumber(value);
                        } else {
                            valueElement.textContent = value;
                        }
                    }
                }

                // تحديث نسبة التغيير
                const changeElement = element.querySelector('.stat-change');
                if (changeElement && change !== undefined) {
                    const changeText = `${change > 0 ? '+' : ''}${change.toFixed(1)}%`;
                    const changeIcon = change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

                    changeElement.innerHTML = `<i class="fas ${changeIcon}"></i> ${changeText}`;
                    changeElement.className = `stat-change ${change >= 0 ? 'positive' : 'negative'}`;
                }
            }
        });
    },

    /**
     * تنسيق الأرقام
     */
    formatNumber: function(num) {
        if (typeof num !== 'number') return num;

        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toLocaleString();
    },
    
    /**
     * تهيئة الرسوم البيانية
     */
    initCharts: function() {
        this.initRevenueChart();
        this.initTransactionChart();
        this.initCurrencyChart();
    },
    
    /**
     * رسم بياني للإيرادات
     */
    initRevenueChart: function() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;
        
        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات',
                    data: [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return TrustPlus.utils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    },
    
    /**
     * رسم بياني للمعاملات
     */
    initTransactionChart: function() {
        const ctx = document.getElementById('transactionChart');
        if (!ctx) return;
        
        this.charts.transaction = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['صرافة', 'تحويلات', 'أخرى'],
                datasets: [{
                    data: [60, 30, 10],
                    backgroundColor: ['#667eea', '#764ba2', '#f093fb'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    },
    
    /**
     * رسم بياني للعملات
     */
    initCurrencyChart: function() {
        const ctx = document.getElementById('currencyChart');
        if (!ctx) return;
        
        this.charts.currency = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['USD', 'EUR', 'GBP', 'SAR', 'AED'],
                datasets: [{
                    label: 'حجم التداول',
                    data: [45000, 32000, 28000, 25000, 18000],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(118, 75, 162, 0.8)',
                        'rgba(240, 147, 251, 0.8)',
                        'rgba(86, 171, 47, 0.8)',
                        'rgba(255, 193, 7, 0.8)'
                    ],
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return TrustPlus.utils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    },
    
    /**
     * تحميل الأنشطة الأخيرة
     */
    loadRecentActivities: function() {
        // تحديد المسار الصحيح
        let activitiesPath = 'api/recent_activities.php';

        // استخدام fetch مباشرة إذا لم يكن TrustPlus.api متاحاً
        const fetchActivities = typeof TrustPlus !== 'undefined' && TrustPlus.api ?
            TrustPlus.api.get(activitiesPath) :
            fetch(activitiesPath, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            }).then(response => response.json());

        fetchActivities
            .then(response => {
                if (response.success) {
                    this.updateActivities(response.data);
                } else {
                    console.warn('Activities loading failed:', response.message);
                }
            })
            .catch(error => {
                console.error('Error loading activities:', error);
            });
    },
    
    /**
     * تحديث الأنشطة
     */
    updateActivities: function(activities) {
        this.data.activities = activities;

        const container = document.querySelector('.recent-activities .activities-list');
        if (!container) {
            console.warn('Activities container not found');
            return;
        }

        container.innerHTML = '';

        if (!activities || activities.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-info-circle mb-2"></i>
                    <p>لا توجد أنشطة حديثة</p>
                </div>
            `;
            return;
        }

        activities.forEach(activity => {
            const item = document.createElement('div');
            item.className = 'activity-item';

            const timeAgo = typeof TrustPlus !== 'undefined' && TrustPlus.utils && TrustPlus.utils.timeAgo ?
                TrustPlus.utils.timeAgo(activity.created_at) :
                this.formatTimeAgo(activity.created_at);

            item.innerHTML = `
                <div class="activity-icon bg-${this.getActivityColor(activity.type)}">
                    <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${this.escapeHtml(activity.title || 'نشاط غير محدد')}</div>
                    <div class="activity-time">${timeAgo}</div>
                </div>
            `;
            container.appendChild(item);
        });
    },

    /**
     * تنسيق الوقت المنقضي
     */
    formatTimeAgo: function(dateString) {
        try {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'منذ لحظات';
            if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
            if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
            return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;
        } catch (error) {
            return 'وقت غير محدد';
        }
    },

    /**
     * تأمين النص من HTML
     */
    escapeHtml: function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },
    
    /**
     * الحصول على لون النشاط
     */
    getActivityColor: function(type) {
        const colors = {
            'exchange': 'primary',
            'transfer': 'success',
            'customer': 'info',
            'user': 'warning',
            'system': 'secondary'
        };
        return colors[type] || 'secondary';
    },
    
    /**
     * الحصول على أيقونة النشاط
     */
    getActivityIcon: function(type) {
        const icons = {
            'exchange': 'exchange-alt',
            'transfer': 'paper-plane',
            'customer': 'user',
            'user': 'user-cog',
            'system': 'cog'
        };
        return icons[type] || 'circle';
    },
    
    /**
     * بدء التحديث التلقائي
     */
    startAutoRefresh: function() {
        // تحديث الإحصائيات كل 5 دقائق
        this.timers.stats = setInterval(() => {
            this.loadStats();
        }, 5 * 60 * 1000);
        
        // تحديث الأنشطة كل دقيقة
        this.timers.activities = setInterval(() => {
            this.loadRecentActivities();
        }, 60 * 1000);
    },
    
    /**
     * إيقاف التحديث التلقائي
     */
    stopAutoRefresh: function() {
        Object.values(this.timers).forEach(timer => {
            clearInterval(timer);
        });
        this.timers = {};
    },
    
    /**
     * ربط الأحداث
     */
    bindEvents: function() {
        // زر التحديث اليدوي
        const refreshBtn = document.querySelector('[data-action="refresh"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refresh();
            });
        }
        
        // تبديل فترة الرسم البياني
        const periodButtons = document.querySelectorAll('[data-period]');
        periodButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const period = e.target.dataset.period;
                this.changePeriod(period);
            });
        });
        
        // تصدير البيانات
        const exportBtn = document.querySelector('[data-action="export"]');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportData();
            });
        }
    },
    
    /**
     * تحديث البيانات
     */
    refresh: function() {
        TrustPlus.utils.showToast('جاري تحديث البيانات...', 'info');
        
        Promise.all([
            this.loadStats(),
            this.loadRecentActivities()
        ]).then(() => {
            TrustPlus.utils.showToast('تم تحديث البيانات بنجاح', 'success');
        }).catch(() => {
            TrustPlus.utils.showToast('خطأ في تحديث البيانات', 'danger');
        });
    },
    
    /**
     * تغيير فترة الرسم البياني
     */
    changePeriod: function(period) {
        // تحديث الرسوم البيانية حسب الفترة المختارة
        console.log('Changing period to:', period);
        
        // تحديث البيانات من الخادم
        TrustPlus.api.get('api/chart_data.php', { period: period })
            .then(response => {
                if (response.success) {
                    this.updateChartData(response.data);
                }
            });
    },
    
    /**
     * تحديث بيانات الرسوم البيانية
     */
    updateChartData: function(data) {
        if (this.charts.revenue && data.revenue) {
            this.charts.revenue.data.labels = data.revenue.labels;
            this.charts.revenue.data.datasets[0].data = data.revenue.data;
            this.charts.revenue.update();
        }
        
        if (this.charts.transaction && data.transaction) {
            this.charts.transaction.data.datasets[0].data = data.transaction.data;
            this.charts.transaction.update();
        }
        
        if (this.charts.currency && data.currency) {
            this.charts.currency.data.labels = data.currency.labels;
            this.charts.currency.data.datasets[0].data = data.currency.data;
            this.charts.currency.update();
        }
    },
    
    /**
     * تصدير البيانات
     */
    exportData: function() {
        const data = [
            { metric: 'إجمالي الإيرادات', value: this.data.stats.total_revenue || 0 },
            { metric: 'عدد المعاملات', value: this.data.stats.total_transactions || 0 },
            { metric: 'عدد العملاء', value: this.data.stats.total_customers || 0 },
            { metric: 'متوسط المعاملة', value: this.data.stats.avg_transaction || 0 }
        ];
        
        TrustPlus.utils.exportToCSV(data, `dashboard_stats_${TrustPlus.utils.formatDate(new Date())}.csv`);
    },
    
    /**
     * تنظيف الموارد
     */
    destroy: function() {
        this.stopAutoRefresh();
        
        // تدمير الرسوم البيانية
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        
        this.charts = {};
        this.data = { stats: {}, charts: {}, activities: [] };
    }
};

// تهيئة لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.dashboard-container')) {
        Dashboard.init();
    }
});

// تنظيف الموارد عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    if (typeof Dashboard !== 'undefined') {
        Dashboard.destroy();
    }
});

// تصدير للاستخدام العام
window.Dashboard = Dashboard;
