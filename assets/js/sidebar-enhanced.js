/**
 * Trust Plus - Enhanced Sidebar JavaScript
 * ملف JavaScript محسن للشريط الجانبي
 */

const SidebarManager = {
    // إعدادات الشريط الجانبي
    settings: {
        breakpoint: 992,
        animationDuration: 300,
        autoCollapse: false,
        persistState: true
    },
    
    // عناصر DOM
    elements: {
        sidebar: null,
        toggle: null,
        mainContent: null,
        overlay: null
    },
    
    // حالة الشريط الجانبي
    state: {
        isOpen: true,
        isCollapsed: false,
        isMobile: false
    },
    
    /**
     * تهيئة الشريط الجانبي
     */
    init: function() {
        this.findElements();
        this.setupEventListeners();
        this.checkScreenSize();
        this.restoreState();
        this.updateActiveLinks();
        
        console.log('✅ تم تهيئة الشريط الجانبي المحسن');
    },
    
    /**
     * العثور على العناصر
     */
    findElements: function() {
        this.elements.sidebar = document.getElementById('sidebar');
        this.elements.toggle = document.querySelector('.sidebar-toggle');
        this.elements.mainContent = document.querySelector('.main-content');
        
        // إنشاء overlay للهواتف
        this.createOverlay();
        
        // إنشاء زر التبديل إذا لم يكن موجوداً
        if (!this.elements.toggle) {
            this.createToggleButton();
        }
    },
    
    /**
     * إنشاء overlay للهواتف
     */
    createOverlay: function() {
        this.elements.overlay = document.createElement('div');
        this.elements.overlay.className = 'sidebar-overlay';
        this.elements.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        `;
        document.body.appendChild(this.elements.overlay);
    },
    
    /**
     * إنشاء زر التبديل
     */
    createToggleButton: function() {
        this.elements.toggle = document.createElement('button');
        this.elements.toggle.className = 'sidebar-toggle btn btn-primary d-lg-none';
        this.elements.toggle.innerHTML = '<i class="fas fa-bars"></i>';
        this.elements.toggle.style.cssText = `
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1001;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: none;
        `;
        document.body.appendChild(this.elements.toggle);
    },
    
    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // زر التبديل
        if (this.elements.toggle) {
            this.elements.toggle.addEventListener('click', () => {
                this.toggle();
            });
        }
        
        // overlay
        if (this.elements.overlay) {
            this.elements.overlay.addEventListener('click', () => {
                this.close();
            });
        }
        
        // تغيير حجم الشاشة
        window.addEventListener('resize', () => {
            this.checkScreenSize();
        });
        
        // مفاتيح الاختصار
        document.addEventListener('keydown', (e) => {
            // Ctrl + B لتبديل الشريط الجانبي
            if (e.ctrlKey && e.key === 'b') {
                e.preventDefault();
                this.toggle();
            }
            
            // Escape لإغلاق الشريط الجانبي في الهواتف
            if (e.key === 'Escape' && this.state.isMobile && this.state.isOpen) {
                this.close();
            }
        });
        
        // النقر خارج الشريط الجانبي في الهواتف
        document.addEventListener('click', (e) => {
            if (this.state.isMobile && 
                this.state.isOpen && 
                this.elements.sidebar && 
                !this.elements.sidebar.contains(e.target) && 
                !this.elements.toggle?.contains(e.target)) {
                this.close();
            }
        });
        
        // تحديث الروابط النشطة عند التنقل
        window.addEventListener('popstate', () => {
            this.updateActiveLinks();
        });
    },
    
    /**
     * فحص حجم الشاشة
     */
    checkScreenSize: function() {
        const wasMobile = this.state.isMobile;
        this.state.isMobile = window.innerWidth <= this.settings.breakpoint;
        
        if (this.state.isMobile !== wasMobile) {
            this.handleScreenSizeChange();
        }
        
        // إظهار/إخفاء زر التبديل
        if (this.elements.toggle) {
            this.elements.toggle.style.display = this.state.isMobile ? 'flex' : 'none';
        }
    },
    
    /**
     * معالجة تغيير حجم الشاشة
     */
    handleScreenSizeChange: function() {
        if (this.state.isMobile) {
            // التبديل إلى وضع الهاتف
            this.close();
        } else {
            // التبديل إلى وضع سطح المكتب
            this.open();
            this.hideOverlay();
        }
    },
    
    /**
     * تبديل الشريط الجانبي
     */
    toggle: function() {
        if (this.state.isOpen) {
            this.close();
        } else {
            this.open();
        }
    },
    
    /**
     * فتح الشريط الجانبي
     */
    open: function() {
        if (!this.elements.sidebar) return;
        
        this.state.isOpen = true;
        
        if (this.state.isMobile) {
            this.elements.sidebar.classList.add('show');
            this.showOverlay();
            document.body.style.overflow = 'hidden';
        } else {
            this.elements.sidebar.classList.remove('collapsed');
            if (this.elements.mainContent) {
                this.elements.mainContent.classList.remove('sidebar-collapsed');
            }
        }
        
        this.saveState();
        this.updateToggleIcon();
    },
    
    /**
     * إغلاق الشريط الجانبي
     */
    close: function() {
        if (!this.elements.sidebar) return;
        
        this.state.isOpen = false;
        
        if (this.state.isMobile) {
            this.elements.sidebar.classList.remove('show');
            this.hideOverlay();
            document.body.style.overflow = '';
        } else {
            this.elements.sidebar.classList.add('collapsed');
            if (this.elements.mainContent) {
                this.elements.mainContent.classList.add('sidebar-collapsed');
            }
        }
        
        this.saveState();
        this.updateToggleIcon();
    },
    
    /**
     * إظهار overlay
     */
    showOverlay: function() {
        if (this.elements.overlay) {
            this.elements.overlay.style.opacity = '1';
            this.elements.overlay.style.visibility = 'visible';
        }
    },
    
    /**
     * إخفاء overlay
     */
    hideOverlay: function() {
        if (this.elements.overlay) {
            this.elements.overlay.style.opacity = '0';
            this.elements.overlay.style.visibility = 'hidden';
        }
    },
    
    /**
     * تحديث أيقونة زر التبديل
     */
    updateToggleIcon: function() {
        if (this.elements.toggle) {
            const icon = this.elements.toggle.querySelector('i');
            if (icon) {
                icon.className = this.state.isOpen ? 'fas fa-times' : 'fas fa-bars';
            }
        }
    },
    
    /**
     * تحديث الروابط النشطة
     */
    updateActiveLinks: function() {
        const currentPath = window.location.pathname;
        const currentPage = currentPath.split('/').pop().replace('.php', '') || 'index';
        
        const navLinks = document.querySelectorAll('.sidebar .nav-link');
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            link.classList.remove('active');
            
            if (href) {
                const linkPage = href.split('/').pop().replace('.php', '');
                if (linkPage === currentPage || 
                    (currentPage === 'index' && linkPage === 'index') ||
                    href.includes(currentPage)) {
                    link.classList.add('active');
                }
            }
        });
    },
    
    /**
     * حفظ حالة الشريط الجانبي
     */
    saveState: function() {
        if (this.settings.persistState && !this.state.isMobile) {
            try {
                localStorage.setItem('sidebar_state', JSON.stringify({
                    isOpen: this.state.isOpen,
                    isCollapsed: this.state.isCollapsed
                }));
            } catch (error) {
                console.warn('فشل في حفظ حالة الشريط الجانبي:', error);
            }
        }
    },
    
    /**
     * استعادة حالة الشريط الجانبي
     */
    restoreState: function() {
        if (this.settings.persistState && !this.state.isMobile) {
            try {
                const savedState = localStorage.getItem('sidebar_state');
                if (savedState) {
                    const state = JSON.parse(savedState);
                    this.state.isOpen = state.isOpen;
                    this.state.isCollapsed = state.isCollapsed;
                    
                    if (!this.state.isOpen) {
                        this.close();
                    }
                }
            } catch (error) {
                console.warn('فشل في استعادة حالة الشريط الجانبي:', error);
            }
        }
    },
    
    /**
     * تنظيف الموارد
     */
    destroy: function() {
        // إزالة مستمعي الأحداث
        if (this.elements.toggle) {
            this.elements.toggle.remove();
        }
        
        if (this.elements.overlay) {
            this.elements.overlay.remove();
        }
        
        // إعادة تعيين الحالة
        this.state = {
            isOpen: true,
            isCollapsed: false,
            isMobile: false
        };
        
        this.elements = {
            sidebar: null,
            toggle: null,
            mainContent: null,
            overlay: null
        };
    }
};

// تهيئة الشريط الجانبي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    SidebarManager.init();
});

// تنظيف الموارد عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    SidebarManager.destroy();
});

// تصدير للاستخدام العام
window.SidebarManager = SidebarManager;
