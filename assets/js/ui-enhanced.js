/**
 * Trust Plus - Enhanced UI JavaScript
 * ملف JavaScript لتحسين واجهة المستخدم
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedUI();
});

function initializeEnhancedUI() {
    // تحسين الرسوم المتحركة
    initializeAnimations();
    
    // تحسين التفاعلات
    initializeInteractions();
    
    // تحسين النماذج
    initializeForms();
    
    // تحسين الجداول
    initializeTables();
    
    // تحسين الأزرار
    initializeButtons();
    
    // تحسين التنبيهات
    initializeAlerts();
    
    // تحسين التحميل
    initializeLoading();
}

// تحسين الرسوم المتحركة
function initializeAnimations() {
    // إضافة رسوم متحركة للبطاقات
    const cards = document.querySelectorAll('.card, .stat-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // إضافة تأثير hover للبطاقات
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 15px 35px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.08)';
        });
    });
}

// تحسين التفاعلات
function initializeInteractions() {
    // تحسين الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // إضافة تأثير الموجة
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.5);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // إضافة CSS للرسوم المتحركة
    if (!document.querySelector('#ui-enhanced-styles')) {
        const style = document.createElement('style');
        style.id = 'ui-enhanced-styles';
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
            
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            
            .fade-in-up {
                animation: fadeInUp 0.6s ease;
            }
            
            .pulse {
                animation: pulse 2s infinite;
            }
            
            .loading-spinner {
                border: 4px solid #f3f3f3;
                border-top: 4px solid #667eea;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                animation: spin 1s linear infinite;
                margin: 20px auto;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }
}

// تحسين النماذج
function initializeForms() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        // إضافة تحقق مخصص
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    validateField(this);
                }
            });
        });
        
        // تحسين إرسال النموذج
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            let isValid = true;
            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });
            
            if (isValid) {
                showLoadingButton(form.querySelector('button[type="submit"]'));
                // يمكن إضافة منطق الإرسال هنا
            }
        });
    });
}

// التحقق من صحة الحقول
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';
    
    // التحقق من الحقول المطلوبة
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        message = 'هذا الحقل مطلوب';
    }
    
    // التحقق من البريد الإلكتروني
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            message = 'يرجى إدخال بريد إلكتروني صحيح';
        }
    }
    
    // التحقق من رقم الهاتف
    if (field.type === 'tel' && value) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(value)) {
            isValid = false;
            message = 'يرجى إدخال رقم هاتف صحيح';
        }
    }
    
    // عرض النتيجة
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        removeErrorMessage(field);
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
        showErrorMessage(field, message);
    }
    
    return isValid;
}

// عرض رسالة خطأ
function showErrorMessage(field, message) {
    removeErrorMessage(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

// إزالة رسالة خطأ
function removeErrorMessage(field) {
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// تحسين الجداول
function initializeTables() {
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        // إضافة فئات Bootstrap
        table.classList.add('table', 'table-hover');
        
        // إضافة تأثيرات للصفوف
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    });
}

// تحسين الأزرار
function initializeButtons() {
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        // إضافة تأثير التحميل
        if (button.type === 'submit') {
            button.addEventListener('click', function() {
                showLoadingButton(this);
            });
        }
    });
}

// عرض حالة التحميل للزر
function showLoadingButton(button) {
    if (!button) return;
    
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
    
    // إعادة تعيين الزر بعد 3 ثوان (يمكن تخصيصه)
    setTimeout(() => {
        button.disabled = false;
        button.innerHTML = originalText;
    }, 3000);
}

// تحسين التنبيهات
function initializeAlerts() {
    // إضافة تأثيرات للتنبيهات الموجودة
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        alert.classList.add('fade-in-up');
        
        // إضافة زر إغلاق إذا لم يكن موجوداً
        if (!alert.querySelector('.btn-close')) {
            const closeBtn = document.createElement('button');
            closeBtn.type = 'button';
            closeBtn.className = 'btn-close';
            closeBtn.setAttribute('aria-label', 'إغلاق');
            closeBtn.addEventListener('click', function() {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => alert.remove(), 300);
            });
            alert.appendChild(closeBtn);
        }
    });
}

// إنشاء تنبيه مخصص
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.querySelector('.alert-container') || createAlertContainer();
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" aria-label="إغلاق"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // إضافة حدث الإغلاق
    const closeBtn = alert.querySelector('.btn-close');
    closeBtn.addEventListener('click', function() {
        hideAlert(alert);
    });
    
    // إغلاق تلقائي
    if (duration > 0) {
        setTimeout(() => hideAlert(alert), duration);
    }
    
    return alert;
}

// إنشاء حاوية التنبيهات
function createAlertContainer() {
    const container = document.createElement('div');
    container.className = 'alert-container';
    container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
    `;
    document.body.appendChild(container);
    return container;
}

// إخفاء التنبيه
function hideAlert(alert) {
    alert.style.opacity = '0';
    alert.style.transform = 'translateX(100%)';
    setTimeout(() => alert.remove(), 300);
}

// تحسين التحميل
function initializeLoading() {
    // إضافة مؤشر تحميل للصفحة
    window.addEventListener('beforeunload', function() {
        showPageLoader();
    });
    
    // إخفاء مؤشر التحميل عند اكتمال التحميل
    window.addEventListener('load', function() {
        hidePageLoader();
    });
}

// عرض مؤشر تحميل الصفحة
function showPageLoader() {
    const loader = document.createElement('div');
    loader.id = 'page-loader';
    loader.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
    `;
    loader.innerHTML = '<div class="loading-spinner"></div>';
    document.body.appendChild(loader);
}

// إخفاء مؤشر تحميل الصفحة
function hidePageLoader() {
    const loader = document.getElementById('page-loader');
    if (loader) {
        loader.style.opacity = '0';
        setTimeout(() => loader.remove(), 300);
    }
}

// تصدير الدوال للاستخدام العام
window.TrustPlusUI = {
    showAlert,
    hideAlert,
    showLoadingButton,
    showPageLoader,
    hidePageLoader,
    validateField
};

console.log('Trust Plus Enhanced UI loaded successfully');
