# Trust Plus - Assets System Documentation
## نظام إدارة الأصول - Trust Plus

### نظرة عامة
تم تطوير نظام إدارة الأصول الجديد لـ Trust Plus لتحسين الأداء وسهولة الصيانة. يتضمن النظام إدارة متقدمة لملفات CSS و JavaScript والصور مع دعم CDN والتحميل المشروط.

### هيكل الملفات

```
assets/
├── css/
│   ├── bootstrap.min.css          # Bootstrap CSS (placeholder)
│   ├── fontawesome.min.css        # Font Awesome CSS (placeholder)
│   ├── main.css                   # الأنماط الرئيسية للنظام
│   ├── dashboard.css              # أنماط خاصة بلوحة التحكم
│   └── reports.css                # أنماط خاصة بالتقارير
├── js/
│   ├── bootstrap.bundle.min.js    # Bootstrap JavaScript (placeholder)
│   ├── chart.min.js               # Chart.js (placeholder)
│   ├── main.js                    # JavaScript الرئيسي للنظام
│   ├── dashboard.js               # JavaScript خاص بلوحة التحكم
│   └── reports.js                 # JavaScript خاص بالتقارير
├── images/                        # مجلد الصور (سيتم إنشاؤه لاحقاً)
└── README.md                      # هذا الملف
```

### الملفات المضمنة (Includes)

```
includes/
├── assets.php                     # فئة إدارة الأصول الرئيسية
├── header.php                     # قالب الرأس المشترك
├── footer.php                     # قالب التذييل المشترك
├── sidebar.php                    # الشريط الجانبي للتنقل
├── navbar.php                     # شريط التنقل العلوي
└── breadcrumb.php                 # مسار التنقل
```

### كيفية الاستخدام

#### 1. تحميل الأصول الأساسية
```php
<?php
// في بداية الصفحة
$page_type = 'default'; // أو 'dashboard' أو 'reports' أو 'forms'
$page_title = 'عنوان الصفحة';
$page_header = 'رأس الصفحة';
$page_subtitle = 'وصف الصفحة';
$page_icon = 'fas fa-icon';

include '../includes/header.php';
?>

<!-- محتوى الصفحة هنا -->

<?php include '../includes/footer.php'; ?>
```

#### 2. أنواع الصفحات المدعومة
- `default`: الأصول الأساسية فقط
- `dashboard`: أصول لوحة التحكم + Chart.js
- `reports`: أصول التقارير + Chart.js
- `forms`: أصول النماذج + تحقق إضافي

#### 3. إضافة أصول مخصصة
```php
<?php
// إضافة ملف CSS مخصص
Assets::addCSS('path/to/custom.css', 10);

// إضافة ملف JavaScript مخصص
Assets::addJS('path/to/custom.js', 10, true, false);

// إضافة CSS مضمن
Assets::addInlineCSS('
    .custom-class {
        color: red;
    }
');

// إضافة JavaScript مضمن
Assets::addInlineJS('
    console.log("Custom JS loaded");
');
?>
```

#### 4. استخدام الدوال المساعدة
```php
<?php
// روابط الأصول
echo asset('css/custom.css');           // ../assets/css/custom.css
echo css_asset('custom.css');           // ../assets/css/custom.css
echo js_asset('custom.js');             // ../assets/js/custom.js
echo image_asset('logo.png');           // ../assets/images/logo.png
?>
```

### الميزات الرئيسية

#### 1. نظام الأولوية
- يمكن تحديد أولوية تحميل الملفات
- الملفات ذات الأولوية الأقل تُحمل أولاً
- مفيد لضمان تحميل المكتبات الأساسية قبل الملفات المخصصة

#### 2. دعم CDN
- تحميل تلقائي من CDN إذا لم تكن الملفات متوفرة محلياً
- يحسن الأداء ويقلل استهلاك الخادم

#### 3. التحميل المشروط
- تحميل أصول مختلفة حسب نوع الصفحة
- يقلل حجم الملفات المحملة غير الضرورية

#### 4. دعم Defer و Async
- إمكانية تحديد طريقة تحميل JavaScript
- يحسن أداء تحميل الصفحة

### JavaScript الرئيسي (main.js)

#### الكائن الرئيسي TrustPlus
```javascript
// الوصول للدوال المساعدة
TrustPlus.utils.formatCurrency(1500, 'USD');
TrustPlus.utils.formatDate(new Date());
TrustPlus.utils.showToast('رسالة نجاح', 'success');

// دوال واجهة المستخدم
TrustPlus.ui.toggleSidebar();
TrustPlus.ui.showLoading('.element');
TrustPlus.ui.confirmDelete('هل أنت متأكد؟');

// دوال API
TrustPlus.api.get('api/data.php');
TrustPlus.api.post('api/save.php', {data: 'value'});

// دوال التحقق
TrustPlus.validation.isValidEmail('<EMAIL>');
TrustPlus.validation.validateForm('#myForm');
```

### CSS الرئيسي (main.css)

#### المتغيرات العامة
```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --border-radius: 15px;
    --box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    --transition: all 0.3s ease;
}
```

#### الفئات المساعدة
- `.bg-primary-gradient`, `.bg-success-gradient`, إلخ
- `.text-gradient`, `.border-gradient`
- `.shadow-lg`, `.rounded-lg`
- `.fade-in`, `.slide-in`

### التخصيص

#### إضافة أنماط مخصصة
1. أنشئ ملف CSS جديد في `assets/css/`
2. أضفه باستخدام `Assets::addCSS()`
3. أو أضف الأنماط مباشرة باستخدام `Assets::addInlineCSS()`

#### إضافة JavaScript مخصص
1. أنشئ ملف JS جديد في `assets/js/`
2. أضفه باستخدام `Assets::addJS()`
3. أو أضف الكود مباشرة باستخدام `Assets::addInlineJS()`

### الأمان

#### حماية الملفات
- جميع المسارات تُنظف باستخدام `htmlspecialchars()`
- التحقق من صحة الملفات قبل التحميل
- منع تحميل ملفات خطيرة

#### CSP (Content Security Policy)
يُنصح بإضافة CSP headers للحماية الإضافية:
```php
header("Content-Security-Policy: default-src 'self'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net;");
```

### الأداء

#### تحسينات الأداء
- تحميل مشروط للأصول
- استخدام CDN للمكتبات الشائعة
- ضغط الملفات (يُنصح بتفعيل gzip على الخادم)
- تحميل JavaScript مع defer/async

#### مراقبة الأداء
```javascript
// قياس وقت تحميل الصفحة
window.addEventListener('load', function() {
    const loadTime = performance.now();
    console.log('Page loaded in:', loadTime, 'ms');
});
```

### استكشاف الأخطاء

#### مشاكل شائعة
1. **ملفات CSS/JS لا تُحمل**: تحقق من المسارات والصلاحيات
2. **أخطاء JavaScript**: افتح Developer Tools وتحقق من Console
3. **مشاكل التصميم**: تحقق من ترتيب تحميل ملفات CSS

#### تسجيل الأخطاء
```javascript
// تسجيل أخطاء JavaScript
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
});
```

### التطوير المستقبلي

#### ميزات مخططة
- نظام themes متقدم
- دعم RTL محسن
- تحسينات أداء إضافية
- دعم Progressive Web App (PWA)

#### المساهمة
لإضافة ميزات جديدة أو إصلاح مشاكل:
1. أنشئ فرع جديد
2. اختبر التغييرات
3. وثق التغييرات
4. أرسل طلب دمج

---

**ملاحظة**: هذا النظام قيد التطوير المستمر. يُرجى مراجعة هذا الملف بانتظام للحصول على آخر التحديثات.
