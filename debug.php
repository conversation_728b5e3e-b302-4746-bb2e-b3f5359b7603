<?php
/**
 * Trust Plus - Debug Script
 * سكريبت تشخيص الأخطاء
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>🔍 تشخيص أخطاء Trust Plus</h1>";
echo "<hr>";

// 1. فحص إصدار PHP
echo "<h2>📋 معلومات النظام</h2>";
echo "<p><strong>إصدار PHP:</strong> " . phpversion() . "</p>";
echo "<p><strong>نظام التشغيل:</strong> " . php_uname() . "</p>";
echo "<p><strong>المجلد الحالي:</strong> " . __DIR__ . "</p>";
echo "<p><strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "</p>";

// 2. فحص الملفات المطلوبة
echo "<h2>📁 فحص الملفات</h2>";

$required_files = [
    'includes/assets.php',
    'includes/header.php',
    'includes/footer.php',
    'includes/sidebar.php',
    'includes/navbar.php',
    'includes/breadcrumb.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<p>✅ <strong>$file</strong> - موجود (" . filesize($file) . " bytes)</p>";
    } else {
        echo "<p>❌ <strong>$file</strong> - مفقود</p>";
    }
}

// 3. فحص صحة ملفات PHP
echo "<h2>🔍 فحص صحة PHP</h2>";

foreach ($required_files as $file) {
    if (file_exists($file)) {
        $syntax_check = shell_exec("php -l $file 2>&1");
        if (strpos($syntax_check, 'No syntax errors') !== false) {
            echo "<p>✅ <strong>$file</strong> - صحيح</p>";
        } else {
            echo "<p>❌ <strong>$file</strong> - خطأ في الصيغة:</p>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>$syntax_check</pre>";
        }
    }
}

// 4. اختبار تحميل ملف assets.php
echo "<h2>⚙️ اختبار تحميل Assets</h2>";

try {
    require_once 'includes/assets.php';
    echo "<p>✅ تم تحميل assets.php بنجاح</p>";
    
    // اختبار الفئة
    if (class_exists('Assets')) {
        echo "<p>✅ فئة Assets موجودة</p>";
        
        // اختبار الثوابت
        $constants = ['ASSETS_PATH', 'CSS_PATH', 'JS_PATH'];
        foreach ($constants as $const) {
            if (defined("Assets::$const")) {
                echo "<p>✅ الثابت Assets::$const محدد</p>";
            } else {
                echo "<p>❌ الثابت Assets::$const غير محدد</p>";
            }
        }
        
        // اختبار الدوال
        $methods = ['loadCore', 'loadDashboard', 'renderCSS', 'renderJS'];
        foreach ($methods as $method) {
            if (method_exists('Assets', $method)) {
                echo "<p>✅ الدالة Assets::$method موجودة</p>";
            } else {
                echo "<p>❌ الدالة Assets::$method مفقودة</p>";
            }
        }
        
    } else {
        echo "<p>❌ فئة Assets غير موجودة</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في تحميل assets.php:</p>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo htmlspecialchars($e->getMessage());
    echo "</pre>";
}

// 5. اختبار تحميل header.php
echo "<h2>📄 اختبار تحميل Header</h2>";

try {
    // محاكاة متغيرات الصفحة
    $page_type = 'default';
    $page_title = 'اختبار';
    $page_header = 'صفحة اختبار';
    $page_subtitle = 'اختبار النظام';
    $page_icon = 'fas fa-test';
    $show_breadcrumb = false;
    $hide_sidebar = true;
    $hide_navbar = true;
    $hide_footer = true;
    $hide_preloader = true;
    
    ob_start();
    include 'includes/header.php';
    $header_output = ob_get_clean();
    
    echo "<p>✅ تم تحميل header.php بنجاح</p>";
    echo "<p><strong>حجم الإخراج:</strong> " . strlen($header_output) . " حرف</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في تحميل header.php:</p>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo htmlspecialchars($e->getMessage());
    echo "</pre>";
}

// 6. فحص مجلدات الأصول
echo "<h2>📂 فحص مجلدات الأصول</h2>";

$asset_dirs = [
    'assets',
    'assets/css',
    'assets/js',
    'assets/images'
];

foreach ($asset_dirs as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $files = scandir($dir);
        $file_count = count($files) - 2; // استبعاد . و ..
        echo "<p>✅ <strong>$dir</strong> - موجود (أذونات: $perms، ملفات: $file_count)</p>";
    } else {
        echo "<p>❌ <strong>$dir</strong> - مفقود</p>";
    }
}

// 7. فحص ملفات CSS و JS
echo "<h2>🎨 فحص ملفات الأصول</h2>";

$asset_files = [
    'assets/css/main.css',
    'assets/css/dashboard.css',
    'assets/css/reports.css',
    'assets/js/main.js',
    'assets/js/dashboard.js',
    'assets/js/reports.js'
];

foreach ($asset_files as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<p>✅ <strong>$file</strong> - موجود ($size bytes)</p>";
    } else {
        echo "<p>⚠️ <strong>$file</strong> - مفقود (سيتم استخدام CDN)</p>";
    }
}

// 8. اختبار اتصال قاعدة البيانات (إذا كان متاحاً)
echo "<h2>🗄️ اختبار قاعدة البيانات</h2>";

if (file_exists('includes/database.php')) {
    try {
        require_once 'includes/database.php';
        $database = new Database();
        $db = $database->getConnection();
        echo "<p>✅ اتصال قاعدة البيانات ناجح</p>";
    } catch (Exception $e) {
        echo "<p>❌ خطأ في اتصال قاعدة البيانات:</p>";
        echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo htmlspecialchars($e->getMessage());
        echo "</pre>";
    }
} else {
    echo "<p>⚠️ ملف database.php غير موجود</p>";
}

// 9. فحص الإضافات المطلوبة
echo "<h2>🔌 فحص إضافات PHP</h2>";

$required_extensions = [
    'pdo' => 'قاعدة البيانات',
    'pdo_mysql' => 'MySQL',
    'json' => 'JSON',
    'mbstring' => 'النصوص متعددة البايت',
    'curl' => 'cURL',
    'gd' => 'معالجة الصور',
    'zip' => 'ضغط الملفات'
];

foreach ($required_extensions as $ext => $desc) {
    if (extension_loaded($ext)) {
        echo "<p>✅ <strong>$ext</strong> ($desc) - مثبت</p>";
    } else {
        echo "<p>⚠️ <strong>$ext</strong> ($desc) - غير مثبت</p>";
    }
}

// 10. اختبار بسيط للصفحة
echo "<h2>🧪 اختبار بسيط</h2>";

try {
    // محاولة إنشاء صفحة بسيطة
    $test_page = '
    <?php
    $page_type = "default";
    $page_title = "اختبار";
    include "includes/header.php";
    ?>
    <div class="container">
        <h1>اختبار النظام</h1>
        <p>إذا رأيت هذه الرسالة، فالنظام يعمل بشكل صحيح.</p>
    </div>
    <?php include "includes/footer.php"; ?>
    ';
    
    file_put_contents('test_page.php', $test_page);
    echo "<p>✅ تم إنشاء صفحة اختبار: <a href='test_page.php' target='_blank'>test_page.php</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء صفحة الاختبار:</p>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo htmlspecialchars($e->getMessage());
    echo "</pre>";
}

echo "<hr>";
echo "<h2>📋 الخلاصة</h2>";
echo "<p>إذا كانت جميع الاختبارات ناجحة، فالنظام يجب أن يعمل بشكل صحيح.</p>";
echo "<p>إذا كان هناك أخطاء، يرجى مراجعة التفاصيل أعلاه وإصلاحها.</p>";

echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='test_page.php'>صفحة الاختبار</a></li>";
echo "<li><a href='dashboard/index.php'>لوحة التحكم</a></li>";
echo "<li><a href='auth/login.php'>صفحة تسجيل الدخول</a></li>";
echo "</ul>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>
