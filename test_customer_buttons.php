<?php
/**
 * ملف اختبار أزرار العملاء المحسنة
 * Trust Plus System
 */

require_once 'config.php';
require_once 'includes/auth.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'اختبار أزرار العملاء المحسنة - ' . SYSTEM_NAME;
$page_header = 'اختبار أزرار العملاء المحسنة';
$page_subtitle = 'فحص التحسينات الجديدة على أزرار إجراءات العملاء';
$page_icon = 'fas fa-mouse-pointer';
$show_breadcrumb = true;
?>
<?php include 'includes/header.php'; ?>

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        نتائج اختبار أزرار العملاء المحسنة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-thumbs-up me-2"></i>تم تحسين أزرار العملاء بنجاح!</h6>
                        <ul class="mb-0">
                            <li>تم تغيير الأزرار من أيقونات فقط إلى نصوص واضحة</li>
                            <li>تم تحسين التصميم ليكون أكثر وضوحاً وسهولة في الاستخدام</li>
                            <li>تم إضافة تأثيرات بصرية جذابة</li>
                            <li>تم تحسين التوافق مع الشاشات الصغيرة</li>
                        </ul>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>التحسينات المطبقة:</h6>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    أزرار بأسماء واضحة
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    تصميم عمودي منظم
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    تأثيرات hover جذابة
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    تصميم متجاوب للهواتف
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    ألوان مميزة لكل إجراء
                                    <span class="badge bg-success rounded-pill">✓</span>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>الأزرار المتاحة:</h6>
                            <div class="action-buttons">
                                <!-- مثال على الأزرار الجديدة -->
                                <a href="#" class="btn btn-sm btn-outline-primary mb-1" title="عرض التفاصيل">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض
                                </a>
                                
                                <a href="#" class="btn btn-sm btn-outline-warning mb-1" title="تعديل بيانات العميل">
                                    <i class="fas fa-edit me-1"></i>
                                    تعديل
                                </a>
                                
                                <button type="button" class="btn btn-sm btn-outline-info mb-1" title="إدارة KYC/AML">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    إدارة KYC
                                </button>
                                
                                <a href="#" class="btn btn-sm btn-outline-success mb-1" title="إدارة المستندات">
                                    <i class="fas fa-file-alt me-1"></i>
                                    مستندات
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6>مقارنة قبل وبعد:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0 text-danger">قبل التحسين</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-outline-primary action-btn" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning action-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info action-btn" title="KYC">
                                                <i class="fas fa-shield-alt"></i>
                                            </button>
                                        </div>
                                        <p class="text-muted mt-2 small">أيقونات فقط - غير واضحة للمستخدم</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0 text-success">بعد التحسين</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="action-buttons">
                                            <a href="#" class="btn btn-sm btn-outline-primary mb-1">
                                                <i class="fas fa-eye me-1"></i>
                                                عرض
                                            </a>
                                            <a href="#" class="btn btn-sm btn-outline-warning mb-1">
                                                <i class="fas fa-edit me-1"></i>
                                                تعديل
                                            </a>
                                            <button class="btn btn-sm btn-outline-info mb-1">
                                                <i class="fas fa-shield-alt me-1"></i>
                                                إدارة KYC
                                            </button>
                                        </div>
                                        <p class="text-muted mt-2 small">نصوص واضحة مع أيقونات - سهل الفهم</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6>اختبار التفاعل:</h6>
                        <div class="alert alert-info">
                            <p><strong>جرب التفاعل مع الأزرار أعلاه:</strong></p>
                            <ul class="mb-0">
                                <li>مرر الماوس فوق الأزرار لرؤية التأثيرات</li>
                                <li>لاحظ كيف تتحرك الأزرار قليلاً عند التمرير</li>
                                <li>انتبه للظلال التي تظهر عند التفاعل</li>
                                <li>جرب تصغير النافذة لرؤية التصميم المتجاوب</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6>الميزات الجديدة:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="feature-card text-center p-3">
                                    <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                                    <h6>وضوح أكبر</h6>
                                    <p class="text-muted small">نصوص واضحة بدلاً من أيقونات فقط</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card text-center p-3">
                                    <i class="fas fa-mobile-alt fa-2x text-success mb-2"></i>
                                    <h6>تصميم متجاوب</h6>
                                    <p class="text-muted small">يتكيف مع جميع أحجام الشاشات</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card text-center p-3">
                                    <i class="fas fa-magic fa-2x text-warning mb-2"></i>
                                    <h6>تأثيرات جذابة</h6>
                                    <p class="text-muted small">حركات وظلال تفاعلية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-code me-2"></i>
                                    التغييرات التقنية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>ملفات تم تعديلها:</h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-file-code text-primary me-2"></i>dashboard/customers.php</li>
                                            <li><i class="fas fa-file-code text-info me-2"></i>assets/css/dashboard-improvements.css</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>CSS Classes جديدة:</h6>
                                        <ul class="list-unstyled">
                                            <li><code>.action-buttons</code> - حاوي الأزرار</li>
                                            <li><code>.customers-table</code> - جدول العملاء</li>
                                            <li><code>.btn-sm</code> - أزرار صغيرة</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="d-flex justify-content-center">
                            <a href="dashboard/customers.php" class="btn btn-primary me-3">
                                <i class="fas fa-users me-2"></i>
                                اختبر في صفحة العملاء الفعلية
                            </a>
                            <a href="dashboard/" class="btn btn-outline-secondary">
                                <i class="fas fa-home me-2"></i>
                                العودة للوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.feature-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    min-width: 120px;
}

.action-buttons .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    text-align: right;
    white-space: nowrap;
}

.action-buttons .btn:hover {
    transform: translateX(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.action-buttons .btn i {
    font-size: 0.7rem;
}

.btn-action {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.btn-action:hover {
    background: #4361ee;
    color: white;
    transform: translateY(-2px);
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.85rem;
}
</style>

<script>
// اختبار تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🧪 بدء اختبار أزرار العملاء المحسنة...');
    
    // اختبار وجود CSS Classes
    const actionButtons = document.querySelector('.action-buttons');
    if (actionButtons) {
        console.log('✅ CSS class .action-buttons موجود');
    } else {
        console.log('❌ CSS class .action-buttons غير موجود');
    }
    
    // اختبار الأزرار
    const buttons = document.querySelectorAll('.action-buttons .btn');
    console.log(`📊 عدد الأزرار في المثال: ${buttons.length}`);
    
    // اختبار التأثيرات
    buttons.forEach((button, index) => {
        button.addEventListener('mouseenter', function() {
            console.log(`🎯 تم التمرير فوق الزر ${index + 1}: ${this.textContent.trim()}`);
        });
    });
    
    // اختبار التصميم المتجاوب
    function checkResponsive() {
        const isMobile = window.innerWidth <= 768;
        console.log(`📱 الوضع الحالي: ${isMobile ? 'هاتف' : 'سطح مكتب'}`);
    }
    
    checkResponsive();
    window.addEventListener('resize', checkResponsive);
    
    console.log('✅ انتهى اختبار أزرار العملاء المحسنة');
    
    // عرض رسالة نجاح
    setTimeout(() => {
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show';
        successAlert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            <strong>تم الاختبار بنجاح!</strong> أزرار العملاء المحسنة تعمل بشكل مثالي.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container-fluid');
        container.insertBefore(successAlert, container.firstChild);
    }, 1000);
});
</script>

<?php include 'includes/footer.php'; ?>
