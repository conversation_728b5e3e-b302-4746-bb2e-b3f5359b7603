<?php
/**
 * Trust Plus - Assets Loading Test
 * اختبار تحميل الأصول
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/assets.php';

echo "<h1>🧪 اختبار تحميل الأصول</h1>";
echo "<hr>";

// اختبار تحميل الأصول الأساسية
echo "<h2>📋 اختبار تحميل الأصول الأساسية...</h2>";

try {
    Assets::loadCore();
    echo "✅ تم تحميل الأصول الأساسية بنجاح<br>";
    
    // عرض CSS المحمل
    echo "<h3>📄 ملفات CSS المحملة:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<pre>" . htmlspecialchars(Assets::renderCSS()) . "</pre>";
    echo "</div>";
    
    // عرض JavaScript المحمل
    echo "<h3>📄 ملفات JavaScript المحملة:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<pre>" . htmlspecialchars(Assets::renderJS()) . "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ خطأ في تحميل الأصول: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// اختبار تحميل أصول لوحة التحكم
echo "<h2>📊 اختبار تحميل أصول لوحة التحكم...</h2>";

try {
    Assets::reset(); // إعادة تعيين
    Assets::loadDashboard();
    echo "✅ تم تحميل أصول لوحة التحكم بنجاح<br>";
    
    // عرض CSS المحمل
    echo "<h3>📄 ملفات CSS لوحة التحكم:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<pre>" . htmlspecialchars(Assets::renderCSS()) . "</pre>";
    echo "</div>";
    
    // عرض JavaScript المحمل
    echo "<h3>📄 ملفات JavaScript لوحة التحكم:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<pre>" . htmlspecialchars(Assets::renderJS()) . "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ خطأ في تحميل أصول لوحة التحكم: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// فحص وجود الملفات
echo "<h2>🔍 فحص وجود الملفات...</h2>";

$files_to_check = [
    'assets/css/main.css' => 'ملف CSS الرئيسي',
    'assets/css/layout-fixes.css' => 'ملف إصلاحات التخطيط',
    'assets/css/color-fixes.css' => 'ملف إصلاحات الألوان',
    'assets/css/dashboard.css' => 'ملف CSS لوحة التحكم',
    'assets/js/main.js' => 'ملف JavaScript الرئيسي',
    'assets/js/error-handler.js' => 'ملف معالج الأخطاء',
    'assets/js/ui-enhanced.js' => 'ملف تحسينات واجهة المستخدم',
    'assets/js/session-monitor-simple.js' => 'ملف مراقب الجلسة',
    'assets/js/dashboard.js' => 'ملف JavaScript لوحة التحكم'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "✅ $description: <code>$file</code> - حجم الملف: " . number_format($size) . " بايت<br>";
    } else {
        echo "❌ $description: <code>$file</code> - <strong>مفقود</strong><br>";
    }
}

echo "<hr>";

// اختبار عملي للأصول
echo "<h2>🎨 اختبار عملي للأصول...</h2>";

// إعادة تعيين وتحميل أصول لوحة التحكم
Assets::reset();
Assets::loadDashboard();

$page_title = 'اختبار الأصول - Trust Plus';
$page_description = 'صفحة اختبار تحميل الأصول';
$page_keywords = 'اختبار, أصول, CSS, JavaScript';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <?php echo Assets::renderMeta($page_title, $page_description, $page_keywords); ?>
    <?php echo Assets::renderCSS(); ?>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary-gradient text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-test-tube me-2"></i>
                            اختبار الأصول العملي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- بطاقة إحصائية -->
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <div class="stat-icon bg-primary-gradient">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="stat-value">100%</div>
                                    <div class="stat-label">نجح التحميل</div>
                                    <div class="stat-change positive">
                                        <i class="fas fa-arrow-up"></i> +5%
                                    </div>
                                </div>
                            </div>
                            
                            <!-- بطاقة إحصائية أخرى -->
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <div class="stat-icon bg-success-gradient">
                                        <i class="fas fa-palette"></i>
                                    </div>
                                    <div class="stat-value">CSS</div>
                                    <div class="stat-label">الألوان والأشكال</div>
                                    <div class="stat-change positive">
                                        <i class="fas fa-check"></i> جاهز
                                    </div>
                                </div>
                            </div>
                            
                            <!-- بطاقة إحصائية ثالثة -->
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <div class="stat-icon bg-warning-gradient">
                                        <i class="fas fa-code"></i>
                                    </div>
                                    <div class="stat-value">JS</div>
                                    <div class="stat-label">التفاعلات</div>
                                    <div class="stat-change positive">
                                        <i class="fas fa-check"></i> نشط
                                    </div>
                                </div>
                            </div>
                            
                            <!-- بطاقة إحصائية رابعة -->
                            <div class="col-md-3 mb-3">
                                <div class="stat-card">
                                    <div class="stat-icon bg-info-gradient">
                                        <i class="fas fa-mobile-alt"></i>
                                    </div>
                                    <div class="stat-value">RWD</div>
                                    <div class="stat-label">التصميم المتجاوب</div>
                                    <div class="stat-change positive">
                                        <i class="fas fa-check"></i> متوافق
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- أزرار اختبار -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h6>اختبار الأزرار والتفاعلات:</h6>
                                <div class="btn-group me-2 mb-2" role="group">
                                    <button type="button" class="btn btn-primary">أساسي</button>
                                    <button type="button" class="btn btn-success">نجح</button>
                                    <button type="button" class="btn btn-warning">تحذير</button>
                                    <button type="button" class="btn btn-danger">خطر</button>
                                    <button type="button" class="btn btn-info">معلومات</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- تنبيهات اختبار -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>اختبار التنبيهات:</h6>
                                <div class="alert alert-success" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    تم تحميل جميع الأصول بنجاح!
                                </div>
                                <div class="alert alert-info" role="alert">
                                    <i class="fas fa-info-circle me-2"></i>
                                    الألوان والأشكال تعمل بشكل صحيح.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php echo Assets::renderJS(); ?>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ تم تحميل جميع الأصول بنجاح');
            console.log('✅ JavaScript يعمل بشكل صحيح');
            console.log('✅ معالج الأخطاء نشط');
            console.log('✅ تحسينات واجهة المستخدم جاهزة');
            
            // اختبار التفاعلات
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('تم النقر على الزر:', this.textContent);
                });
            });
            
            // عرض رسالة نجح
            setTimeout(() => {
                if (window.TrustPlusUI && window.TrustPlusUI.showAlert) {
                    window.TrustPlusUI.showAlert('تم تحميل جميع الأصول بنجاح! 🎉', 'success');
                }
            }, 1000);
        });
    </script>
</body>
</html>

<?php
Assets::reset();
echo "<div style='margin-top: 30px; padding: 20px; background: #d4edda; border-radius: 5px;'>";
echo "<h3 style='color: #155724;'>🎉 اختبار الأصول مكتمل!</h3>";
echo "<p style='color: #155724;'>إذا كانت الصفحة أعلاه تظهر بشكل صحيح مع الألوان والأشكال، فهذا يعني أن جميع الأصول تعمل بشكل مثالي.</p>";
echo "<p><a href='dashboard/index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 افتح لوحة التحكم</a></p>";
echo "</div>";
?>
