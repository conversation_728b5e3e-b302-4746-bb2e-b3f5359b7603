<?php
/**
 * ملف تهيئة نظام إدارة الصناديق والبنوك
 * Trust Plus System
 */

require_once 'config.php';

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تهيئة نظام إدارة الصناديق والبنوك - Trust Plus</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .setup-container { max-width: 800px; margin: 50px auto; }
        .step { margin-bottom: 20px; padding: 15px; border-radius: 8px; }
        .step.success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .step.error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .step.info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
<div class='container setup-container'>
    <div class='text-center mb-4'>
        <h1><i class='fas fa-university'></i> تهيئة نظام إدارة الصناديق والبنوك</h1>
        <p class='text-muted'>Trust Plus Financial Management System</p>
    </div>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "<div class='step success'>
            <h5><i class='fas fa-check-circle'></i> الاتصال بقاعدة البيانات</h5>
            <p>تم الاتصال بقاعدة البيانات بنجاح</p>
          </div>";
    
    // إنشاء جداول الصناديق والبنوك
    echo "<div class='step info'>
            <h5><i class='fas fa-database'></i> إنشاء جداول الصناديق والبنوك</h5>";
    
    // جدول الصناديق النقدية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS cash_boxes (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            currency_id INT NOT NULL,
            initial_balance DECIMAL(15,2) DEFAULT 0,
            current_balance DECIMAL(15,2) DEFAULT 0,
            branch_id INT NOT NULL,
            responsible_user_id INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (currency_id) REFERENCES currencies(id),
            FOREIGN KEY (branch_id) REFERENCES branches(id),
            FOREIGN KEY (responsible_user_id) REFERENCES users(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<p>✓ تم إنشاء جدول الصناديق النقدية</p>";
    
    // جدول الحسابات البنكية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS bank_accounts (
            id INT PRIMARY KEY AUTO_INCREMENT,
            account_name VARCHAR(100) NOT NULL,
            bank_name VARCHAR(100) NOT NULL,
            account_number VARCHAR(50) NOT NULL,
            iban VARCHAR(50),
            swift_code VARCHAR(20),
            currency_id INT NOT NULL,
            initial_balance DECIMAL(15,2) DEFAULT 0,
            current_balance DECIMAL(15,2) DEFAULT 0,
            branch_id INT NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (currency_id) REFERENCES currencies(id),
            FOREIGN KEY (branch_id) REFERENCES branches(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<p>✓ تم إنشاء جدول الحسابات البنكية</p>";
    
    // جدول حركات الصناديق النقدية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS cash_movements (
            id INT PRIMARY KEY AUTO_INCREMENT,
            cash_box_id INT NOT NULL,
            movement_type ENUM('deposit', 'withdrawal') NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            description TEXT,
            reference_number VARCHAR(50),
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<p>✓ تم إنشاء جدول حركات الصناديق النقدية</p>";
    
    // جدول حركات الحسابات البنكية
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS bank_movements (
            id INT PRIMARY KEY AUTO_INCREMENT,
            bank_account_id INT NOT NULL,
            movement_type ENUM('deposit', 'withdrawal') NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            description TEXT,
            reference_number VARCHAR(50),
            user_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<p>✓ تم إنشاء جدول حركات الحسابات البنكية</p>";
    
    echo "</div>";
    
    // إضافة الصلاحيات المطلوبة
    echo "<div class='step info'>
            <h5><i class='fas fa-key'></i> إضافة صلاحيات إدارة الصناديق والبنوك</h5>";
    
    $permissions = [
        ['cash.view', 'عرض الصناديق والبنوك', 'cash'],
        ['cash.edit', 'تعديل الصناديق والبنوك', 'cash'],
        ['cash.movements', 'إدارة حركات الصناديق والبنوك', 'cash'],
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO permissions (permission_name, description, module) VALUES (?, ?, ?)");
    foreach ($permissions as $permission) {
        $stmt->execute($permission);
    }
    echo "<p>✓ تم إضافة صلاحيات إدارة الصناديق والبنوك</p>";
    
    echo "</div>";
    
    // إدراج بيانات تجريبية
    echo "<div class='step info'>
            <h5><i class='fas fa-plus'></i> إدراج بيانات تجريبية</h5>";
    
    // التحقق من وجود عملات
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM currencies WHERE is_active = 1");
    $stmt->execute();
    $currencies_count = $stmt->fetchColumn();
    
    if ($currencies_count == 0) {
        echo "<p class='text-warning'>⚠️ لا توجد عملات نشطة في النظام. يرجى إضافة العملات أولاً.</p>";
    } else {
        // جلب العملة الأساسية
        $stmt = $pdo->prepare("SELECT id FROM currencies WHERE is_base_currency = 1 LIMIT 1");
        $stmt->execute();
        $base_currency = $stmt->fetch();
        
        if (!$base_currency) {
            $stmt = $pdo->prepare("SELECT id FROM currencies WHERE is_active = 1 LIMIT 1");
            $stmt->execute();
            $base_currency = $stmt->fetch();
        }
        
        // جلب الفرع الرئيسي
        $stmt = $pdo->prepare("SELECT id FROM branches WHERE is_active = 1 LIMIT 1");
        $stmt->execute();
        $main_branch = $stmt->fetch();
        
        if ($base_currency && $main_branch) {
            // إضافة صندوق نقدي تجريبي
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO cash_boxes (id, name, currency_id, initial_balance, current_balance, branch_id, is_active) 
                VALUES (1, 'الصندوق الرئيسي', ?, 10000, 10000, ?, 1)
            ");
            $stmt->execute([$base_currency['id'], $main_branch['id']]);
            echo "<p>✓ تم إضافة صندوق نقدي تجريبي</p>";
            
            // إضافة حساب بنكي تجريبي
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO bank_accounts (id, account_name, bank_name, account_number, currency_id, initial_balance, current_balance, branch_id, is_active) 
                VALUES (1, 'الحساب الجاري الرئيسي', 'البنك الأهلي', '*********', ?, 50000, 50000, ?, 1)
            ");
            $stmt->execute([$base_currency['id'], $main_branch['id']]);
            echo "<p>✓ تم إضافة حساب بنكي تجريبي</p>";
        }
    }
    
    echo "</div>";
    
    // إضافة الصلاحيات لدور المدير
    echo "<div class='step info'>
            <h5><i class='fas fa-user-shield'></i> إضافة الصلاحيات لدور المدير</h5>";
    
    // جلب معرف دور المدير
    $stmt = $pdo->prepare("SELECT id FROM roles WHERE id = 1 LIMIT 1");
    $stmt->execute();
    $admin_role = $stmt->fetch();
    
    if ($admin_role) {
        // جلب معرفات الصلاحيات الجديدة
        $stmt = $pdo->prepare("SELECT id FROM permissions WHERE module = 'cash'");
        $stmt->execute();
        $cash_permissions = $stmt->fetchAll();
        
        foreach ($cash_permissions as $permission) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES (1, ?)");
            $stmt->execute([$permission['id']]);
        }
        
        echo "<p>✓ تم إضافة صلاحيات الصناديق والبنوك لدور المدير</p>";
    }
    
    echo "</div>";
    
    echo "<div class='step success'>
            <h5><i class='fas fa-check-circle'></i> تمت التهيئة بنجاح</h5>
            <p>تم إعداد نظام إدارة الصناديق والبنوك بنجاح</p>
            <div class='mt-3'>
                <a href='dashboard/cash.php?type=cash' class='btn btn-primary me-2'>
                    <i class='fas fa-cash-register'></i> إدارة الصناديق النقدية
                </a>
                <a href='dashboard/cash.php?type=bank' class='btn btn-info me-2'>
                    <i class='fas fa-university'></i> إدارة الحسابات البنكية
                </a>
                <a href='dashboard/index.php' class='btn btn-success'>
                    <i class='fas fa-home'></i> لوحة التحكم
                </a>
            </div>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-triangle'></i> خطأ في قاعدة البيانات</h5>
            <p>حدث خطأ أثناء تهيئة قاعدة البيانات:</p>
            <code>" . htmlspecialchars($e->getMessage()) . "</code>
          </div>";
} catch (Exception $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-triangle'></i> خطأ عام</h5>
            <p>حدث خطأ أثناء التهيئة:</p>
            <code>" . htmlspecialchars($e->getMessage()) . "</code>
          </div>";
}

echo "</div>
</body>
</html>";
?>
