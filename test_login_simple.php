<?php
/**
 * Trust Plus - Simple Login Test
 * اختبار تسجيل دخول بسيط
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 اختبار تسجيل الدخول البسيط</h1>";
echo "<hr>";

try {
    // 1. تضمين الملفات
    echo "<h2>1. تحميل الملفات...</h2>";
    
    if (file_exists('config.php')) {
        require_once 'config.php';
        echo "✅ تم تحميل config.php<br>";
    } else {
        echo "❌ ملف config.php غير موجود<br>";
    }
    
    if (file_exists('includes/database.php')) {
        require_once 'includes/database.php';
        echo "✅ تم تحميل database.php<br>";
    } else {
        echo "❌ ملف database.php غير موجود<br>";
        exit();
    }
    
    if (file_exists('includes/auth.php')) {
        require_once 'includes/auth.php';
        echo "✅ تم تحميل auth.php<br>";
    } else {
        echo "❌ ملف auth.php غير موجود<br>";
        exit();
    }
    
    // 2. اختبار الاتصال بقاعدة البيانات
    echo "<h2>2. اختبار قاعدة البيانات...</h2>";
    
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "✅ الاتصال بقاعدة البيانات ناجح<br>";
        
        // إنشاء الجداول إذا لم تكن موجودة
        $database->createBasicTables();
        echo "✅ تم التحقق من الجداول<br>";
        
    } else {
        echo "❌ فشل الاتصال بقاعدة البيانات<br>";
        exit();
    }
    
    // 3. اختبار نظام المصادقة
    echo "<h2>3. اختبار نظام المصادقة...</h2>";
    
    $auth = new Auth();
    echo "✅ تم إنشاء كائن Auth<br>";
    
    // اختبار تسجيل الدخول
    $login_result = $auth->login('admin', 'admin123');
    
    if ($login_result['success']) {
        echo "✅ تسجيل الدخول ناجح!<br>";
        echo "📝 الرسالة: " . $login_result['message'] . "<br>";
        
        // اختبار الحصول على معلومات المستخدم
        $user = $auth->getCurrentUser();
        if ($user) {
            echo "✅ تم الحصول على معلومات المستخدم<br>";
            echo "👤 اسم المستخدم: " . htmlspecialchars($user['username']) . "<br>";
            echo "📧 البريد الإلكتروني: " . htmlspecialchars($user['email']) . "<br>";
            echo "👨‍💼 الاسم الكامل: " . htmlspecialchars($user['full_name']) . "<br>";
        }
        
        // تسجيل الخروج
        $auth->logout();
        echo "✅ تم تسجيل الخروج<br>";
        
    } else {
        echo "❌ فشل تسجيل الدخول<br>";
        echo "📝 الرسالة: " . $login_result['message'] . "<br>";
        
        // التحقق من وجود المستخدم
        $stmt = $db->prepare("SELECT * FROM users WHERE username = 'admin'");
        $stmt->execute();
        $admin_user = $stmt->fetch();
        
        if ($admin_user) {
            echo "✅ المستخدم admin موجود في قاعدة البيانات<br>";
            echo "🔐 hash كلمة المرور: " . substr($admin_user['password_hash'], 0, 20) . "...<br>";
            
            // اختبار كلمة المرور
            if (password_verify('admin123', $admin_user['password_hash'])) {
                echo "✅ كلمة المرور صحيحة<br>";
            } else {
                echo "❌ كلمة المرور غير صحيحة<br>";
                
                // إعادة تعيين كلمة المرور
                $new_hash = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $db->prepare("UPDATE users SET password_hash = :hash WHERE username = 'admin'");
                $stmt->bindParam(':hash', $new_hash);
                if ($stmt->execute()) {
                    echo "✅ تم إعادة تعيين كلمة المرور<br>";
                }
            }
        } else {
            echo "❌ المستخدم admin غير موجود<br>";
            
            // إنشاء المستخدم
            $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $db->prepare("
                INSERT INTO users (username, email, password_hash, full_name, role_id, branch_id) 
                VALUES ('admin', '<EMAIL>', :password_hash, 'مدير النظام', 1, 1)
            ");
            $stmt->bindParam(':password_hash', $password_hash);
            
            if ($stmt->execute()) {
                echo "✅ تم إنشاء المستخدم admin<br>";
            } else {
                echo "❌ فشل في إنشاء المستخدم<br>";
            }
        }
    }
    
    // 4. اختبار نهائي
    echo "<h2>4. اختبار نهائي...</h2>";
    
    $final_test = $auth->login('admin', 'admin123');
    if ($final_test['success']) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🎉 نجح الاختبار!</h3>";
        echo "<p>نظام تسجيل الدخول يعمل بشكل صحيح الآن.</p>";
        echo "<p><strong>اسم المستخدم:</strong> admin</p>";
        echo "<p><strong>كلمة المرور:</strong> admin123</p>";
        echo "</div>";
        
        $auth->logout();
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ فشل الاختبار</h3>";
        echo "<p>لا يزال هناك مشكلة في نظام تسجيل الدخول.</p>";
        echo "<p><strong>الخطأ:</strong> " . $final_test['message'] . "</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ خطأ في النظام</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🔗 الروابط المفيدة</h2>";
echo "<p><a href='auth/login.php'>صفحة تسجيل الدخول</a></p>";
echo "<p><a href='setup_database.php'>إعداد قاعدة البيانات</a></p>";
echo "<p><a href='debug.php'>تشخيص النظام</a></p>";
echo "<p><a href='test_system.html'>اختبار النظام</a></p>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
