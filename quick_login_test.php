<?php
/**
 * Trust Plus - Quick Login Test
 * اختبار سريع لتسجيل الدخول
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🚀 اختبار سريع لتسجيل الدخول</h1>";
echo "<hr>";

try {
    // تضمين الملفات
    if (file_exists('config.php')) {
        require_once 'config.php';
        echo "✅ تم تحميل config.php<br>";
    }
    
    require_once 'includes/database.php';
    require_once 'includes/auth.php';
    echo "✅ تم تحميل جميع الملفات<br><br>";
    
    // اختبار تسجيل الدخول
    echo "<h2>🔑 اختبار تسجيل الدخول...</h2>";
    
    $auth = new Auth();
    $login_result = $auth->login('admin', 'admin123');
    
    if ($login_result['success']) {
        echo "<div style='color: green; background: #d4edda; padding: 20px; border-radius: 10px; margin: 15px 0; border: 2px solid #28a745;'>";
        echo "<h3>🎉 نجح تسجيل الدخول!</h3>";
        echo "<p><strong>الرسالة:</strong> " . htmlspecialchars($login_result['message']) . "</p>";
        
        // الحصول على معلومات المستخدم
        $user = $auth->getCurrentUser();
        if ($user) {
            echo "<h4>👤 معلومات المستخدم:</h4>";
            echo "<ul>";
            echo "<li><strong>ID:</strong> " . $user['id'] . "</li>";
            echo "<li><strong>اسم المستخدم:</strong> " . htmlspecialchars($user['username']) . "</li>";
            echo "<li><strong>الاسم الكامل:</strong> " . htmlspecialchars($user['full_name']) . "</li>";
            echo "<li><strong>البريد الإلكتروني:</strong> " . htmlspecialchars($user['email']) . "</li>";
            echo "<li><strong>الدور:</strong> " . $user['role_id'] . "</li>";
            echo "</ul>";
        }
        
        echo "<h4>✅ النظام جاهز للاستخدام!</h4>";
        echo "<p><a href='auth/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔑 اذهب لصفحة تسجيل الدخول</a></p>";
        echo "</div>";
        
        // تسجيل الخروج
        $auth->logout();
        echo "<p>✅ تم تسجيل الخروج بنجاح</p>";
        
    } else {
        echo "<div style='color: red; background: #f8d7da; padding: 20px; border-radius: 10px; margin: 15px 0; border: 2px solid #dc3545;'>";
        echo "<h3>❌ فشل تسجيل الدخول</h3>";
        echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($login_result['message']) . "</p>";
        echo "</div>";
        
        // محاولة إصلاح المشكلة
        echo "<h3>🔧 محاولة إصلاح المشكلة...</h3>";
        
        $database = new Database();
        $db = $database->getConnection();
        
        if ($db) {
            // التحقق من وجود المستخدم
            $stmt = $db->prepare("SELECT * FROM users WHERE username = 'admin'");
            $stmt->execute();
            $admin_user = $stmt->fetch();
            
            if ($admin_user) {
                echo "<p>✅ المستخدم admin موجود</p>";
                
                // إعادة تعيين كلمة المرور
                $new_hash = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $db->prepare("UPDATE users SET password_hash = :hash, is_active = 1, failed_login_attempts = 0, locked_until = NULL WHERE username = 'admin'");
                $stmt->bindParam(':hash', $new_hash);
                
                if ($stmt->execute()) {
                    echo "<p>✅ تم إعادة تعيين كلمة المرور</p>";
                    
                    // اختبار مرة أخرى
                    $login_result2 = $auth->login('admin', 'admin123');
                    if ($login_result2['success']) {
                        echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4>🎉 تم إصلاح المشكلة!</h4>";
                        echo "<p>تسجيل الدخول يعمل الآن بشكل صحيح</p>";
                        echo "</div>";
                        $auth->logout();
                    } else {
                        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4>❌ لا تزال المشكلة موجودة</h4>";
                        echo "<p>الخطأ: " . htmlspecialchars($login_result2['message']) . "</p>";
                        echo "</div>";
                    }
                }
            } else {
                echo "<p>❌ المستخدم admin غير موجود - سأقوم بإنشائه</p>";
                
                // إنشاء المستخدم
                $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $db->prepare("
                    INSERT INTO users (username, email, password_hash, full_name, role_id, branch_id, is_active) 
                    VALUES ('admin', '<EMAIL>', :password_hash, 'مدير النظام', 1, 1, 1)
                ");
                $stmt->bindParam(':password_hash', $password_hash);
                
                if ($stmt->execute()) {
                    echo "<p>✅ تم إنشاء المستخدم admin</p>";
                    
                    // اختبار تسجيل الدخول
                    $login_result3 = $auth->login('admin', 'admin123');
                    if ($login_result3['success']) {
                        echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4>🎉 تم إنشاء المستخدم وتسجيل الدخول بنجاح!</h4>";
                        echo "</div>";
                        $auth->logout();
                    }
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ خطأ في النظام</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📝 معلومات تسجيل الدخول:</h3>";
echo "<p><strong>اسم المستخدم:</strong> admin</p>";
echo "<p><strong>كلمة المرور:</strong> admin123</p>";
echo "<p><strong>رابط تسجيل الدخول:</strong> <a href='auth/login.php' target='_blank'>auth/login.php</a></p>";
echo "</div>";

echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='auth/login.php' target='_blank'>🔑 صفحة تسجيل الدخول</a></li>";
echo "<li><a href='dashboard/index.php' target='_blank'>📊 لوحة التحكم</a></li>";
echo "<li><a href='check_password.php' target='_blank'>🔍 فحص كلمة المرور</a></li>";
echo "<li><a href='unlock_account.php' target='_blank'>🔓 إلغاء قفل الحساب</a></li>";
echo "</ul>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
