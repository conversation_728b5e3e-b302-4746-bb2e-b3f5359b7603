<?php
/**
 * ملف تهيئة نظام إدارة المستخدمين والأدوار والصلاحيات
 * Trust Plus System
 */

require_once 'config.php';

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تهيئة نظام إدارة المستخدمين - Trust Plus</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .setup-container { max-width: 800px; margin: 50px auto; }
        .step { margin-bottom: 20px; padding: 15px; border-radius: 8px; }
        .step.success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .step.error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .step.info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
<div class='container setup-container'>
    <div class='text-center mb-4'>
        <h1><i class='fas fa-cogs'></i> تهيئة نظام إدارة المستخدمين</h1>
        <p class='text-muted'>Trust Plus Financial Management System</p>
    </div>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "<div class='step success'>
            <h5><i class='fas fa-check-circle'></i> الاتصال بقاعدة البيانات</h5>
            <p>تم الاتصال بقاعدة البيانات بنجاح</p>
          </div>";
    
    // إنشاء جدول الأدوار
    echo "<div class='step info'>
            <h5><i class='fas fa-user-tag'></i> إنشاء جدول الأدوار</h5>";
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            role_name VARCHAR(50) NOT NULL UNIQUE,
            description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<p>✓ تم إنشاء جدول الأدوار</p>";
    
    // إنشاء جدول الصلاحيات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            permission_name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            module VARCHAR(50) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<p>✓ تم إنشاء جدول الصلاحيات</p>";
    
    // إنشاء جدول ربط الأدوار بالصلاحيات
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS role_permissions (
            role_id INT,
            permission_id INT,
            PRIMARY KEY (role_id, permission_id),
            FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
            FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<p>✓ تم إنشاء جدول ربط الأدوار بالصلاحيات</p>";
    
    // إنشاء جدول الفروع
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS branches (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            address TEXT,
            phone VARCHAR(20),
            email VARCHAR(100),
            manager_id INT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<p>✓ تم إنشاء جدول الفروع</p>";
    
    // تحديث جدول المستخدمين
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            role_id INT DEFAULT 1,
            branch_id INT DEFAULT 1,
            is_active TINYINT(1) DEFAULT 1,
            failed_login_attempts INT DEFAULT 0,
            locked_until DATETIME NULL,
            last_login DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (role_id) REFERENCES roles(id),
            FOREIGN KEY (branch_id) REFERENCES branches(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<p>✓ تم إنشاء/تحديث جدول المستخدمين</p>";
    
    // إنشاء جدول سجل المراجعة
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS audit_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(50) NOT NULL,
            table_name VARCHAR(50) NOT NULL,
            record_id INT NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "<p>✓ تم إنشاء جدول سجل المراجعة</p>
          </div>";
    
    // إدراج البيانات الأساسية
    echo "<div class='step info'>
            <h5><i class='fas fa-database'></i> إدراج البيانات الأساسية</h5>";
    
    // إدراج الأدوار الأساسية
    $roles = [
        [1, 'مدير النظام', 'صلاحيات كاملة للنظام'],
        [2, 'مدير الفرع', 'إدارة العمليات والموظفين'],
        [3, 'محاسب', 'إدارة الحسابات والتقارير المالية'],
        [4, 'أمين الصندوق', 'العمليات النقدية والصرافة'],
        [5, 'موظف تشغيلي', 'العمليات الأساسية']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO roles (id, role_name, description) VALUES (?, ?, ?)");
    foreach ($roles as $role) {
        $stmt->execute($role);
    }
    echo "<p>✓ تم إدراج الأدوار الأساسية</p>";
    
    // إدراج الفرع الرئيسي
    $stmt = $pdo->prepare("INSERT IGNORE INTO branches (id, name) VALUES (1, 'الفرع الرئيسي')");
    $stmt->execute();
    echo "<p>✓ تم إدراج الفرع الرئيسي</p>";
    
    // إدراج الصلاحيات الأساسية
    $permissions = [
        // إدارة المستخدمين
        ['users.view', 'عرض المستخدمين', 'users'],
        ['users.create', 'إضافة مستخدمين', 'users'],
        ['users.edit', 'تعديل المستخدمين', 'users'],
        ['users.delete', 'حذف المستخدمين', 'users'],
        
        // إدارة الأدوار
        ['roles.view', 'عرض الأدوار', 'roles'],
        ['roles.create', 'إضافة أدوار', 'roles'],
        ['roles.edit', 'تعديل الأدوار', 'roles'],
        ['roles.delete', 'حذف الأدوار', 'roles'],
        
        // إدارة الصلاحيات
        ['permissions.view', 'عرض الصلاحيات', 'permissions'],
        ['permissions.create', 'إضافة صلاحيات', 'permissions'],
        ['permissions.edit', 'تعديل الصلاحيات', 'permissions'],
        ['permissions.delete', 'حذف الصلاحيات', 'permissions'],
        
        // إدارة العملاء
        ['customers.view', 'عرض العملاء', 'customers'],
        ['customers.create', 'إضافة عملاء', 'customers'],
        ['customers.edit', 'تعديل العملاء', 'customers'],
        ['customers.delete', 'حذف العملاء', 'customers'],
        
        // عمليات الصرافة
        ['exchange.view', 'عرض عمليات الصرافة', 'exchange'],
        ['exchange.create', 'إنشاء عمليات صرافة', 'exchange'],
        ['exchange.rates', 'إدارة أسعار الصرف', 'exchange'],
        
        // التحويلات المالية
        ['transfers.view', 'عرض التحويلات', 'transfers'],
        ['transfers.create', 'إنشاء تحويلات', 'transfers'],
        ['transfers.approve', 'اعتماد التحويلات', 'transfers'],
        
        // المعاملات المالية
        ['transactions.view', 'عرض المعاملات', 'transactions'],
        ['transactions.edit', 'تعديل المعاملات', 'transactions'],
        
        // المحاسبة
        ['accounting.view', 'عرض المحاسبة', 'accounting'],
        ['accounting.edit', 'تعديل المحاسبة', 'accounting'],
        
        // التقارير
        ['reports.financial', 'التقارير المالية', 'reports'],
        ['reports.compliance', 'تقارير الامتثال', 'reports'],
        
        // الإعدادات
        ['settings.view', 'عرض الإعدادات', 'settings'],
        ['settings.edit', 'تعديل الإعدادات', 'settings'],
        
        // سجل المراجعة
        ['audit.view', 'عرض سجل المراجعة', 'audit'],
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO permissions (permission_name, description, module) VALUES (?, ?, ?)");
    foreach ($permissions as $permission) {
        $stmt->execute($permission);
    }
    echo "<p>✓ تم إدراج الصلاحيات الأساسية</p>";
    
    // التحقق من وجود مستخدم المدير
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin_exists = $stmt->fetchColumn();
    
    if ($admin_exists == 0) {
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password_hash, full_name, role_id, branch_id) 
            VALUES ('admin', '<EMAIL>', ?, 'مدير النظام', 1, 1)
        ");
        $stmt->execute([$password_hash]);
        echo "<p>✓ تم إنشاء حساب المدير الافتراضي</p>";
        echo "<div class='alert alert-warning'>
                <strong>معلومات تسجيل الدخول:</strong><br>
                اسم المستخدم: admin<br>
                كلمة المرور: admin123<br>
                <small>يرجى تغيير كلمة المرور بعد تسجيل الدخول</small>
              </div>";
    } else {
        echo "<p>✓ حساب المدير موجود مسبقاً</p>";
    }
    
    echo "</div>";
    
    echo "<div class='step success'>
            <h5><i class='fas fa-check-circle'></i> تمت التهيئة بنجاح</h5>
            <p>تم إعداد نظام إدارة المستخدمين والأدوار والصلاحيات بنجاح</p>
            <div class='mt-3'>
                <a href='dashboard/users.php' class='btn btn-primary me-2'>
                    <i class='fas fa-users'></i> إدارة المستخدمين
                </a>
                <a href='dashboard/roles.php' class='btn btn-info me-2'>
                    <i class='fas fa-user-tag'></i> إدارة الأدوار
                </a>
                <a href='dashboard/permissions.php' class='btn btn-warning me-2'>
                    <i class='fas fa-key'></i> إدارة الصلاحيات
                </a>
                <a href='auth/login.php' class='btn btn-success'>
                    <i class='fas fa-sign-in-alt'></i> تسجيل الدخول
                </a>
            </div>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-triangle'></i> خطأ في قاعدة البيانات</h5>
            <p>حدث خطأ أثناء تهيئة قاعدة البيانات:</p>
            <code>" . htmlspecialchars($e->getMessage()) . "</code>
          </div>";
} catch (Exception $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-triangle'></i> خطأ عام</h5>
            <p>حدث خطأ أثناء التهيئة:</p>
            <code>" . htmlspecialchars($e->getMessage()) . "</code>
          </div>";
}

echo "</div>
</body>
</html>";
?>
