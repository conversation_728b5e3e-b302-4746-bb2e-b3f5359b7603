<?php
/**
 * Trust Plus - System Check Script
 * سكريبت فحص النظام للتأكد من صحة التحديث
 */

echo "🔍 فحص نظام Trust Plus...\n\n";

// قائمة الملفات المطلوبة
$required_files = [
    // نظام إدارة الأصول
    'includes/assets.php' => 'فئة إدارة الأصول',
    'includes/header.php' => 'قالب الرأس',
    'includes/footer.php' => 'قالب التذييل',
    'includes/sidebar.php' => 'الشريط الجانبي',
    'includes/navbar.php' => 'شريط التنقل',
    'includes/breadcrumb.php' => 'مسار التنقل',
    
    // ملفات CSS
    'assets/css/main.css' => 'الأنماط الرئيسية',
    'assets/css/dashboard.css' => 'أنماط لوحة التحكم',
    'assets/css/reports.css' => 'أنماط التقارير',
    
    // ملفات JavaScript
    'assets/js/main.js' => 'JavaScript الرئيسي',
    'assets/js/dashboard.js' => 'JavaScript لوحة التحكم',
    'assets/js/reports.js' => 'JavaScript التقارير',
    
    // ملفات التوثيق
    'assets/README.md' => 'دليل نظام الأصول',
    'SYSTEM_UPDATE_REPORT.md' => 'تقرير التحديث',
    'QUICK_START_GUIDE.md' => 'دليل البدء السريع'
];

// قائمة الصفحات المحدثة
$updated_pages = [
    'auth/login.php' => 'صفحة تسجيل الدخول',
    'dashboard/index.php' => 'لوحة التحكم الرئيسية',
    'dashboard/customers.php' => 'إدارة العملاء',
    'dashboard/exchange.php' => 'عمليات الصرافة',
    'dashboard/transfers.php' => 'التحويلات المالية',
    'dashboard/reports.php' => 'التقارير المالية',
    'dashboard/settings.php' => 'إعدادات النظام',
    'dashboard/exchange_rates.php' => 'أسعار الصرف',
    'dashboard/financial_dashboard.php' => 'لوحة الأداء المالي',
    'dashboard/compliance_reports.php' => 'تقارير الامتثال'
];

$total_files = 0;
$existing_files = 0;
$missing_files = [];

echo "📁 فحص الملفات الجديدة:\n";
echo str_repeat("-", 50) . "\n";

foreach ($required_files as $file => $description) {
    $total_files++;
    if (file_exists($file)) {
        $existing_files++;
        $size = filesize($file);
        echo "✅ $file ($description) - " . formatBytes($size) . "\n";
    } else {
        $missing_files[] = $file;
        echo "❌ $file ($description) - مفقود\n";
    }
}

echo "\n📄 فحص الصفحات المحدثة:\n";
echo str_repeat("-", 50) . "\n";

$updated_count = 0;
foreach ($updated_pages as $page => $description) {
    if (file_exists($page)) {
        // فحص إذا كانت الصفحة تحتوي على النظام الجديد
        $content = file_get_contents($page);
        if (strpos($content, "include '../includes/header.php'") !== false) {
            $updated_count++;
            echo "✅ $page ($description) - محدث\n";
        } else {
            echo "⚠️  $page ($description) - يحتاج تحديث\n";
        }
    } else {
        echo "❌ $page ($description) - غير موجود\n";
    }
}

echo "\n📊 ملخص الفحص:\n";
echo str_repeat("=", 50) . "\n";
echo "📁 الملفات الجديدة: $existing_files/$total_files\n";
echo "📄 الصفحات المحدثة: $updated_count/" . count($updated_pages) . "\n";

if (empty($missing_files)) {
    echo "🎉 جميع الملفات موجودة!\n";
} else {
    echo "⚠️  الملفات المفقودة:\n";
    foreach ($missing_files as $file) {
        echo "   - $file\n";
    }
}

// فحص صحة ملفات PHP
echo "\n🔍 فحص صحة ملفات PHP:\n";
echo str_repeat("-", 50) . "\n";

$php_files = [
    'includes/assets.php',
    'includes/header.php',
    'includes/footer.php',
    'includes/sidebar.php',
    'includes/navbar.php',
    'includes/breadcrumb.php'
];

$syntax_errors = 0;
foreach ($php_files as $file) {
    if (file_exists($file)) {
        $output = [];
        $return_code = 0;
        exec("php -l $file 2>&1", $output, $return_code);
        
        if ($return_code === 0) {
            echo "✅ $file - صحيح\n";
        } else {
            $syntax_errors++;
            echo "❌ $file - خطأ في الصيغة\n";
            echo "   " . implode("\n   ", $output) . "\n";
        }
    }
}

// فحص الأذونات
echo "\n🔐 فحص الأذونات:\n";
echo str_repeat("-", 50) . "\n";

$directories = ['assets', 'assets/css', 'assets/js', 'includes'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        if ($perms >= '0755') {
            echo "✅ $dir - أذونات صحيحة ($perms)\n";
        } else {
            echo "⚠️  $dir - أذونات قد تحتاج تعديل ($perms)\n";
        }
    } else {
        echo "❌ $dir - مجلد غير موجود\n";
    }
}

// فحص متطلبات النظام
echo "\n⚙️  فحص متطلبات النظام:\n";
echo str_repeat("-", 50) . "\n";

$php_version = phpversion();
echo "🐘 إصدار PHP: $php_version ";
if (version_compare($php_version, '7.4.0', '>=')) {
    echo "✅\n";
} else {
    echo "⚠️  (يُنصح بـ PHP 7.4+)\n";
}

$extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext - مثبت\n";
    } else {
        echo "❌ $ext - غير مثبت\n";
    }
}

// تقرير نهائي
echo "\n" . str_repeat("=", 60) . "\n";
echo "📋 التقرير النهائي:\n";
echo str_repeat("=", 60) . "\n";

$total_score = 0;
$max_score = 0;

// نقاط الملفات
$max_score += 100;
$file_score = ($existing_files / $total_files) * 100;
$total_score += $file_score;
echo "📁 الملفات: " . round($file_score, 1) . "%\n";

// نقاط الصفحات
$max_score += 100;
$page_score = ($updated_count / count($updated_pages)) * 100;
$total_score += $page_score;
echo "📄 الصفحات: " . round($page_score, 1) . "%\n";

// نقاط PHP
$max_score += 100;
$php_score = $syntax_errors === 0 ? 100 : 50;
$total_score += $php_score;
echo "🐘 PHP: " . $php_score . "%\n";

$final_score = ($total_score / $max_score) * 100;
echo "\n🎯 النقاط الإجمالية: " . round($final_score, 1) . "%\n";

if ($final_score >= 90) {
    echo "🎉 ممتاز! النظام جاهز للاستخدام!\n";
} elseif ($final_score >= 70) {
    echo "👍 جيد! بعض التحسينات مطلوبة.\n";
} else {
    echo "⚠️  يحتاج المزيد من العمل.\n";
}

echo "\n📝 التوصيات:\n";
if (!empty($missing_files)) {
    echo "- إنشاء الملفات المفقودة\n";
}
if ($updated_count < count($updated_pages)) {
    echo "- تحديث الصفحات المتبقية\n";
}
if ($syntax_errors > 0) {
    echo "- إصلاح أخطاء PHP\n";
}

echo "\n🔗 الموارد المفيدة:\n";
echo "- دليل البدء السريع: QUICK_START_GUIDE.md\n";
echo "- تقرير التحديث: SYSTEM_UPDATE_REPORT.md\n";
echo "- دليل الأصول: assets/README.md\n";

echo "\n✨ انتهى الفحص!\n";

function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}
?>
