# Trust Plus - دليل البدء السريع

## 🚀 كيفية استخدام النظام الجديد

### 1. إنشاء صفحة جديدة

```php
<?php
require_once '../includes/auth.php';

$auth = new Auth();

// التحقق من الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'default'; // أو 'dashboard' أو 'reports' أو 'forms'
$page_title = 'عنوان الصفحة - ' . SYSTEM_NAME;
$page_header = 'عنوان الصفحة';
$page_subtitle = 'وصف الصفحة';
$page_icon = 'fas fa-icon';
$show_breadcrumb = true;

include '../includes/header.php';
?>

<!-- محتوى الصفحة -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">محتوى البطاقة</h5>
                </div>
                <div class="card-body">
                    <!-- المحتوى هنا -->
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
```

### 2. أنواع الصفحات المدعومة

#### `default` - الصفحات العادية
- Bootstrap CSS & JS
- Font Awesome
- Main CSS & JS
- مناسب للصفحات البسيطة

#### `dashboard` - لوحات التحكم
- جميع أصول default
- Chart.js للرسوم البيانية
- Dashboard CSS & JS
- مناسب للوحات التحكم والإحصائيات

#### `reports` - التقارير
- جميع أصول default
- Chart.js للرسوم البيانية
- Reports CSS & JS
- مناسب لصفحات التقارير

#### `forms` - النماذج
- جميع أصول default
- CSS و JS إضافي للنماذج
- تحقق محسن من البيانات
- مناسب للنماذج المعقدة

### 3. إضافة أصول مخصصة

```php
<?php
// إضافة CSS مخصص
$additional_head = '
<style>
    .custom-class {
        color: red;
    }
</style>
';

// إضافة JavaScript مخصص
$inline_js = '
    console.log("Custom JavaScript");
    
    function customFunction() {
        alert("مرحبا!");
    }
';

include '../includes/header.php';
?>
```

### 4. استخدام مكتبة TrustPlus JavaScript

```javascript
// تنسيق العملة
TrustPlus.utils.formatCurrency(1500, 'USD'); // $1,500.00

// تنسيق التاريخ
TrustPlus.utils.formatDate(new Date()); // 2024-01-01

// عرض تنبيه
TrustPlus.utils.showToast('تم الحفظ بنجاح', 'success');

// إظهار/إخفاء التحميل
TrustPlus.ui.showLoading('.my-element');
TrustPlus.ui.hideLoading('.my-element');

// تأكيد الحذف
TrustPlus.ui.confirmDelete('هل أنت متأكد؟').then(confirmed => {
    if (confirmed) {
        // تنفيذ الحذف
    }
});

// طلبات API
TrustPlus.api.get('api/data.php').then(data => {
    console.log(data);
});

TrustPlus.api.post('api/save.php', {name: 'value'}).then(data => {
    console.log(data);
});

// التحقق من النماذج
TrustPlus.validation.validateForm('#myForm');
TrustPlus.validation.isValidEmail('<EMAIL>');
```

### 5. استخدام الفئات CSS الجاهزة

```html
<!-- البطاقات -->
<div class="card">
    <div class="card-header">العنوان</div>
    <div class="card-body">المحتوى</div>
</div>

<!-- الأزرار -->
<button class="btn btn-primary">زر أساسي</button>
<button class="btn btn-success">زر نجاح</button>

<!-- بطاقات الإحصائيات -->
<div class="stats-card">
    <div class="stats-icon bg-primary-gradient">
        <i class="fas fa-users"></i>
    </div>
    <div class="stats-value">150</div>
    <div class="stats-label">إجمالي العملاء</div>
</div>

<!-- التدرجات اللونية -->
<div class="bg-primary-gradient">خلفية متدرجة أساسية</div>
<div class="bg-success-gradient">خلفية متدرجة نجاح</div>
<div class="bg-warning-gradient">خلفية متدرجة تحذير</div>
```

### 6. إعدادات الصفحة المتقدمة

```php
<?php
// إخفاء عناصر معينة
$hide_sidebar = true;    // إخفاء الشريط الجانبي
$hide_navbar = true;     // إخفاء شريط التنقل
$hide_footer = true;     // إخفاء التذييل
$hide_preloader = true;  // إخفاء شاشة التحميل

// مسار تنقل مخصص
$breadcrumb_path = [
    ['title' => 'الرئيسية', 'url' => 'index.php'],
    ['title' => 'الإعدادات', 'url' => 'settings.php']
];

// إجراءات الصفحة
$page_actions = '
    <button class="btn btn-primary">إضافة جديد</button>
    <button class="btn btn-secondary">تصدير</button>
';

include '../includes/header.php';
?>
```

### 7. إضافة رسائل النظام

```php
<?php
// رسائل النجاح
$_SESSION['success_message'] = 'تم الحفظ بنجاح';

// رسائل الخطأ
$_SESSION['error_message'] = 'حدث خطأ أثناء الحفظ';

// رسائل التحذير
$_SESSION['warning_message'] = 'يرجى التحقق من البيانات';

// رسائل المعلومات
$_SESSION['info_message'] = 'معلومة مفيدة';
?>
```

### 8. استخدام النوافذ المنبثقة

```html
<!-- نافذة منبثقة بسيطة -->
<div class="modal fade" id="myModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">العنوان</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                المحتوى
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary">حفظ</button>
            </div>
        </div>
    </div>
</div>
```

### 9. إضافة رسوم بيانية

```javascript
// رسم بياني بسيط
const ctx = document.getElementById('myChart');
const chart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس'],
        datasets: [{
            label: 'المبيعات',
            data: [12, 19, 3],
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
```

### 10. نصائح مهمة

#### ✅ افعل
- استخدم النظام الموحد للأصول
- اتبع تسمية الفئات المعيارية
- استخدم مكتبة TrustPlus JavaScript
- اختبر على أجهزة مختلفة

#### ❌ لا تفعل
- لا تضع روابط CDN مباشرة
- لا تكرر CSS أو JavaScript
- لا تتجاهل رسائل الأخطاء
- لا تنس اختبار التصميم المتجاوب

### 11. استكشاف الأخطاء

#### مشكلة: الأنماط لا تظهر
```bash
# تحقق من المسارات
ls -la assets/css/

# تحقق من الصلاحيات
chmod 644 assets/css/*.css
```

#### مشكلة: JavaScript لا يعمل
```javascript
// تحقق من وجود الأخطاء في Console
console.log('TrustPlus loaded:', typeof TrustPlus !== 'undefined');
```

#### مشكلة: الصفحة لا تحمل
```php
// تحقق من أخطاء PHP
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

---

## 🎯 الخطوات التالية

1. **اختبر الصفحات الموجودة** للتأكد من عملها
2. **أنشئ صفحات جديدة** باستخدام النظام الجديد
3. **خصص التصميم** حسب احتياجاتك
4. **أضف ميزات جديدة** باستخدام المكتبات المتاحة

---

**🚀 مبروك! أنت الآن جاهز لاستخدام النظام الجديد! 🎉**
