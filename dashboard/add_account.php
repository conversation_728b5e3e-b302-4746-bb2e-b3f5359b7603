<?php
require_once '../includes/auth.php';
require_once '../includes/header.php';

$auth = new Auth();
if (!$auth->checkSession() || !$auth->hasPermission('accounting.view')) {
    header('Location: index.php?error=ليس لديك صلاحية لإضافة حسابات');
    exit();
}

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $account_code = trim($_POST['account_code'] ?? '');
    $account_name = trim($_POST['account_name'] ?? '');
    $account_type = trim($_POST['account_type'] ?? '');
    $description = trim($_POST['description'] ?? '');

    // تحقق من الحقول المطلوبة
    if ($account_code === '' || $account_name === '' || $account_type === '') {
        $error_message = 'جميع الحقول الإلزامية مطلوبة.';
    } else {
        try {
            $database = new Database();
            $db = $database->getConnection();
            // تحقق من عدم تكرار رمز الحساب
            $stmt = $db->prepare('SELECT COUNT(*) FROM accounts WHERE account_code = ?');
            $stmt->execute([$account_code]);
            if ($stmt->fetchColumn() > 0) {
                $error_message = 'رمز الحساب مستخدم بالفعل.';
            } else {
                $stmt = $db->prepare('INSERT INTO accounts (account_code, account_name, account_type, description) VALUES (?, ?, ?, ?)');
                $stmt->execute([$account_code, $account_name, $account_type, $description]);
                $success_message = 'تمت إضافة الحساب بنجاح!';
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء إضافة الحساب: ' . $e->getMessage();
        }
    }
}
?>
<div class="container-fluid p-4">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h2 class="mb-0"><i class="fas fa-plus-circle me-2"></i>إضافة حساب جديد</h2>
            <a href="accounting.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للمحاسبة
            </a>
        </div>
    </div>
    <?php if ($error_message): ?>
        <div class="alert alert-danger"> <?php echo htmlspecialchars($error_message); ?> </div>
    <?php endif; ?>
    <?php if ($success_message): ?>
        <div class="alert alert-success"> <?php echo htmlspecialchars($success_message); ?> </div>
    <?php endif; ?>
    <div class="card mx-auto" style="max-width: 600px;">
        <div class="card-body">
            <form method="POST" action="" class="needs-validation" novalidate>
                <div class="mb-3">
                    <label class="form-label">رمز الحساب <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="account_code" required maxlength="20" value="<?php echo htmlspecialchars($_POST['account_code'] ?? ''); ?>">
                    <div class="invalid-feedback">رمز الحساب مطلوب.</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">اسم الحساب <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="account_name" required maxlength="100" value="<?php echo htmlspecialchars($_POST['account_name'] ?? ''); ?>">
                    <div class="invalid-feedback">اسم الحساب مطلوب.</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">نوع الحساب <span class="text-danger">*</span></label>
                    <select class="form-control" name="account_type" required>
                        <option value="">اختر النوع...</option>
                        <option value="assets" <?php if(($_POST['account_type'] ?? '')=='assets') echo 'selected'; ?>>أصول</option>
                        <option value="liabilities" <?php if(($_POST['account_type'] ?? '')=='liabilities') echo 'selected'; ?>>التزامات</option>
                        <option value="equity" <?php if(($_POST['account_type'] ?? '')=='equity') echo 'selected'; ?>>حقوق ملكية</option>
                        <option value="revenue" <?php if(($_POST['account_type'] ?? '')=='revenue') echo 'selected'; ?>>إيرادات</option>
                        <option value="expenses" <?php if(($_POST['account_type'] ?? '')=='expenses') echo 'selected'; ?>>مصروفات</option>
                    </select>
                    <div class="invalid-feedback">نوع الحساب مطلوب.</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">الوصف (اختياري)</label>
                    <textarea class="form-control" name="description" rows="2"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                </div>
                <div class="text-end">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>حفظ الحساب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php require_once '../includes/footer.php'; ?> 