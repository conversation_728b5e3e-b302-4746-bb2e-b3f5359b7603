<?php
require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/user_manager.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

// التحقق من الصلاحيات
if (!$auth->hasPermission('users.view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى إدارة المستخدمين';
    header('Location: index.php');
    exit();
}

$current_user = $auth->getCurrentUser();
$userManager = new UserManager();

// معالجة الطلبات
$action = $_GET['action'] ?? 'list';
$user_id = $_GET['id'] ?? null;
$message = '';
$error = '';

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!hash_equals($_SESSION['csrf_token'] ?? '', $csrf_token)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        switch ($_POST['action']) {
            case 'add_user':
                if ($auth->hasPermission('users.create')) {
                    $result = $userManager->createUser($_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: users.php');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لإضافة مستخدمين';
                }
                break;
                
            case 'edit_user':
                if ($auth->hasPermission('users.edit')) {
                    $result = $userManager->updateUser($_POST['user_id'], $_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: users.php');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لتعديل المستخدمين';
                }
                break;
                
            case 'toggle_status':
                if ($auth->hasPermission('users.edit')) {
                    $result = $userManager->toggleUserStatus($_POST['user_id']);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                    } else {
                        $_SESSION['error_message'] = $result['message'];
                    }
                    header('Location: users.php');
                    exit();
                } else {
                    $error = 'ليس لديك صلاحية لتغيير حالة المستخدمين';
                }
                break;
                
            case 'reset_password':
                if ($auth->hasPermission('users.edit')) {
                    $result = $userManager->resetPassword($_POST['user_id']);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                    } else {
                        $_SESSION['error_message'] = $result['message'];
                    }
                    header('Location: users.php');
                    exit();
                } else {
                    $error = 'ليس لديك صلاحية لإعادة تعيين كلمات المرور';
                }
                break;
        }
    }
}

// إنشاء رمز CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// جلب البيانات حسب الإجراء
$users = [];
$roles = [];
$branches = [];
$user_data = null;

try {
    if ($action == 'list' || $action == 'add' || $action == 'edit') {
        $users = $userManager->getAllUsers();
        $roles = $userManager->getAllRoles();
        $branches = $userManager->getAllBranches();
    }
    
    if ($action == 'edit' && $user_id) {
        $user_data = $userManager->getUserById($user_id);
        if (!$user_data) {
            $error = 'المستخدم غير موجود';
            $action = 'list';
        }
    }
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
}

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'إدارة المستخدمين - ' . SYSTEM_NAME;
$page_header = 'إدارة المستخدمين';
$page_subtitle = 'إدارة حسابات المستخدمين والصلاحيات';
$page_icon = 'fas fa-users';
$show_breadcrumb = true;

// إضافة أزرار الإجراءات
$page_actions = '';
if ($auth->hasPermission('users.create') && $action == 'list') {
    $page_actions = '<a href="users.php?action=add" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة مستخدم جديد
    </a>';
}
?>
<?php include '../includes/header.php'; ?>

<div class="container-fluid p-4">
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($action == 'list'): ?>
        <!-- قائمة المستخدمين -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    قائمة المستخدمين
                </h5>
            </div>
            <div class="card-body">
                <!-- شريط البحث والفلترة -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="searchUsers" placeholder="البحث في المستخدمين...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterRole">
                            <option value="">جميع الأدوار</option>
                            <?php foreach ($roles as $role): ?>
                                <option value="<?php echo $role['id']; ?>">
                                    <?php echo htmlspecialchars($role['role_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterStatus">
                            <option value="">جميع الحالات</option>
                            <option value="1">نشط</option>
                            <option value="0">غير نشط</option>
                        </select>
                    </div>
                </div>

                <!-- جدول المستخدمين -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="usersTable">
                        <thead class="table-dark">
                            <tr>
                                <th>الرقم</th>
                                <th>اسم المستخدم</th>
                                <th>الاسم الكامل</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الفرع</th>
                                <th>الحالة</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr data-role="<?php echo $user['role_id']; ?>" data-status="<?php echo $user['is_active']; ?>">
                                    <td><?php echo $user['id']; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                        <?php if ($user['failed_login_attempts'] > 0): ?>
                                            <span class="badge bg-warning ms-1" title="محاولات دخول فاشلة">
                                                <?php echo $user['failed_login_attempts']; ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo htmlspecialchars($user['role_name'] ?? 'غير محدد'); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['branch_name'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <?php if ($user['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                        <?php endif; ?>
                                        
                                        <?php if ($user['locked_until'] && strtotime($user['locked_until']) > time()): ?>
                                            <span class="badge bg-warning ms-1">مقفل</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['last_login']): ?>
                                            <small class="text-muted">
                                                <?php echo date('Y-m-d H:i', strtotime($user['last_login'])); ?>
                                            </small>
                                        <?php else: ?>
                                            <small class="text-muted">لم يسجل دخول</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <?php if ($auth->hasPermission('users.edit')): ?>
                                                <a href="users.php?action=edit&id=<?php echo $user['id']; ?>" 
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                <?php if ($user['id'] != $current_user['id']): ?>
                                                    <button type="button" class="btn btn-outline-warning" 
                                                            onclick="toggleUserStatus(<?php echo $user['id']; ?>, <?php echo $user['is_active'] ? 0 : 1; ?>)"
                                                            title="<?php echo $user['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="fas fa-<?php echo $user['is_active'] ? 'ban' : 'check'; ?>"></i>
                                                    </button>
                                                    
                                                    <button type="button" class="btn btn-outline-info" 
                                                            onclick="resetUserPassword(<?php echo $user['id']; ?>)"
                                                            title="إعادة تعيين كلمة المرور">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    <?php elseif ($action == 'add'): ?>
        <!-- نموذج إضافة مستخدم جديد -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستخدم جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="users.php" id="addUserForm">
                    <input type="hidden" name="action" value="add_user">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <div class="form-text">يجب أن يكون فريداً ولا يحتوي على مسافات</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required minlength="8">
                                <div class="form-text">يجب أن تكون 8 أحرف على الأقل</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role_id" class="form-label">الدور <span class="text-danger">*</span></label>
                                <select class="form-select" id="role_id" name="role_id" required>
                                    <option value="">اختر الدور</option>
                                    <?php foreach ($roles as $role): ?>
                                        <option value="<?php echo $role['id']; ?>">
                                            <?php echo htmlspecialchars($role['role_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="branch_id" class="form-label">الفرع</label>
                                <select class="form-select" id="branch_id" name="branch_id">
                                    <option value="">اختر الفرع</option>
                                    <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>">
                                            <?php echo htmlspecialchars($branch['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        تفعيل المستخدم
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="users.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ المستخدم
                        </button>
                    </div>
                </form>
            </div>
        </div>

    <?php elseif ($action == 'edit' && $user_data): ?>
        <!-- نموذج تعديل المستخدم -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    تعديل المستخدم: <?php echo htmlspecialchars($user_data['full_name']); ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="users.php" id="editUserForm">
                    <input type="hidden" name="action" value="edit_user">
                    <input type="hidden" name="user_id" value="<?php echo $user_data['id']; ?>">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username"
                                       value="<?php echo htmlspecialchars($user_data['username']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?php echo htmlspecialchars($user_data['email']); ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name"
                                       value="<?php echo htmlspecialchars($user_data['full_name']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" minlength="8">
                                <div class="form-text">اتركها فارغة إذا كنت لا تريد تغييرها</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role_id" class="form-label">الدور <span class="text-danger">*</span></label>
                                <select class="form-select" id="role_id" name="role_id" required>
                                    <option value="">اختر الدور</option>
                                    <?php foreach ($roles as $role): ?>
                                        <option value="<?php echo $role['id']; ?>"
                                                <?php echo $role['id'] == $user_data['role_id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($role['role_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="branch_id" class="form-label">الفرع</label>
                                <select class="form-select" id="branch_id" name="branch_id">
                                    <option value="">اختر الفرع</option>
                                    <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>"
                                                <?php echo $branch['id'] == $user_data['branch_id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($branch['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                           <?php echo $user_data['is_active'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">
                                        تفعيل المستخدم
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="users.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- نماذج منبثقة -->
<!-- نموذج تأكيد تغيير الحالة -->
<div class="modal fade" id="toggleStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد تغيير الحالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="statusMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="users.php" style="display: inline;">
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="user_id" id="toggleUserId">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    <button type="submit" class="btn btn-warning">تأكيد</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تأكيد إعادة تعيين كلمة المرور -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إعادة تعيين كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إعادة تعيين كلمة المرور لهذا المستخدم؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    سيتم إنشاء كلمة مرور جديدة وإرسالها للمستخدم
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="users.php" style="display: inline;">
                    <input type="hidden" name="action" value="reset_password">
                    <input type="hidden" name="user_id" id="resetUserId">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    <button type="submit" class="btn btn-info">إعادة تعيين</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// البحث والفلترة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchUsers');
    const roleFilter = document.getElementById('filterRole');
    const statusFilter = document.getElementById('filterStatus');
    const table = document.getElementById('usersTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedRole = roleFilter.value;
        const selectedStatus = statusFilter.value;

        for (let row of rows) {
            const text = row.textContent.toLowerCase();
            const roleMatch = !selectedRole || row.dataset.role === selectedRole;
            const statusMatch = !selectedStatus || row.dataset.status === selectedStatus;
            const searchMatch = !searchTerm || text.includes(searchTerm);

            if (roleMatch && statusMatch && searchMatch) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        }
    }

    searchInput.addEventListener('input', filterTable);
    roleFilter.addEventListener('change', filterTable);
    statusFilter.addEventListener('change', filterTable);
});

// تغيير حالة المستخدم
function toggleUserStatus(userId, newStatus) {
    const message = newStatus === 1 ?
        'هل أنت متأكد من تفعيل هذا المستخدم؟' :
        'هل أنت متأكد من إلغاء تفعيل هذا المستخدم؟';

    document.getElementById('statusMessage').textContent = message;
    document.getElementById('toggleUserId').value = userId;

    const modal = new bootstrap.Modal(document.getElementById('toggleStatusModal'));
    modal.show();
}

// إعادة تعيين كلمة المرور
function resetUserPassword(userId) {
    document.getElementById('resetUserId').value = userId;

    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

// التحقق من صحة النماذج
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form[id$="UserForm"]');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const username = form.querySelector('input[name="username"]');
            const email = form.querySelector('input[name="email"]');
            const password = form.querySelector('input[name="password"]');

            // التحقق من اسم المستخدم
            if (username && !/^[a-zA-Z0-9_]+$/.test(username.value)) {
                e.preventDefault();
                alert('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط');
                username.focus();
                return;
            }

            // التحقق من كلمة المرور
            if (password && password.value && password.value.length < 8) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                password.focus();
                return;
            }
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
