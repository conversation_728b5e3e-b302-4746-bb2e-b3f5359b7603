<?php
require_once '../includes/auth.php';
require_once '../includes/transfer_manager.php';

$auth = new Auth();
$transferManager = new TransferManager();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('transfers.view')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى هذه الصفحة');
    exit();
}

$transfer_id = $_GET['id'] ?? null;
$success_message = $_GET['success'] ?? '';

if (!$transfer_id) {
    header('Location: transfers.php?error=معرف التحويل مطلوب');
    exit();
}

// جلب تفاصيل التحويل
$transfer = $transferManager->getTransferDetails($transfer_id);

if (!$transfer) {
    header('Location: transfers.php?error=التحويل غير موجود');
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال التحويل - <?php echo htmlspecialchars($transfer['reference_number']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .receipt-container {
            max-width: 700px;
            margin: 2rem auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .receipt-body {
            padding: 2rem;
        }
        
        .company-logo {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
        }
        
        .transfer-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            text-align: center;
        }
        
        .amount-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .success-badge {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 25px;
            padding: 0.5rem 1rem;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .status-timeline {
            position: relative;
            padding: 1rem 0;
        }
        
        .timeline-item {
            position: relative;
            padding-left: 3rem;
            margin-bottom: 1rem;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0.75rem;
            top: 0;
            bottom: -1rem;
            width: 2px;
            background: #e9ecef;
        }
        
        .timeline-item:last-child::before {
            display: none;
        }
        
        .timeline-icon {
            position: absolute;
            left: 0;
            top: 0;
            width: 1.5rem;
            height: 1.5rem;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
        }
        
        .timeline-icon.completed {
            background: #28a745;
        }
        
        .timeline-icon.current {
            background: #ffc107;
            color: #000;
        }
        
        @media print {
            body {
                background: white;
            }
            
            .receipt-container {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }
            
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header -->
        <div class="receipt-header">
            <div class="company-logo">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h3 class="mb-1">Trust Plus</h3>
            <p class="mb-0">نظام إدارة التحويلات المالية</p>
        </div>
        
        <!-- Body -->
        <div class="receipt-body">
            <?php if ($success_message): ?>
            <div class="text-center mb-4">
                <div class="success-badge">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="text-center mb-4">
                <h4 class="text-primary">إيصال تحويل مالي</h4>
                <p class="text-muted">رقم المرجع: <?php echo htmlspecialchars($transfer['reference_number']); ?></p>
            </div>
            
            <!-- ملخص التحويل -->
            <div class="transfer-summary">
                <div class="row">
                    <div class="col-6">
                        <div class="text-muted small">المبلغ المرسل</div>
                        <div class="amount-display text-danger">
                            <?php echo htmlspecialchars($transfer['sending_currency_symbol']); ?>
                            <?php echo number_format($transfer['sending_amount'], 2); ?>
                        </div>
                        <div class="small"><?php echo htmlspecialchars($transfer['sending_currency_name']); ?></div>
                    </div>
                    <div class="col-6">
                        <div class="text-muted small">المبلغ المستلم</div>
                        <div class="amount-display text-success">
                            <?php echo htmlspecialchars($transfer['receiving_currency_symbol']); ?>
                            <?php echo number_format($transfer['receiving_amount'], 2); ?>
                        </div>
                        <div class="small"><?php echo htmlspecialchars($transfer['receiving_currency_name']); ?></div>
                    </div>
                </div>
                
                <hr class="my-3">
                
                <div class="row">
                    <div class="col-6">
                        <div class="text-muted small">سعر الصرف</div>
                        <div class="fw-bold">
                            1 <?php echo htmlspecialchars($transfer['sending_currency_code']); ?> = 
                            <?php echo number_format($transfer['exchange_rate_used'], 6); ?> 
                            <?php echo htmlspecialchars($transfer['receiving_currency_code']); ?>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-muted small">رسوم التحويل</div>
                        <div class="fw-bold">
                            <?php echo htmlspecialchars($transfer['sending_currency_symbol']); ?>
                            <?php echo number_format($transfer['transfer_fee_amount'], 2); ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- تفاصيل المرسل -->
            <div class="mb-4">
                <h6 class="text-primary mb-3">تفاصيل المرسل</h6>
                <div class="info-row">
                    <span class="info-label">الاسم</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['sender_name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهاتف</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['sender_phone'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">البريد الإلكتروني</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['sender_email'] ?? 'غير محدد'); ?></span>
                </div>
            </div>
            
            <!-- تفاصيل المستفيد -->
            <div class="mb-4">
                <h6 class="text-primary mb-3">تفاصيل المستفيد</h6>
                <div class="info-row">
                    <span class="info-label">الاسم</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['recipient_name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">البلد</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['recipient_country']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">البنك/الوكيل</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['recipient_bank'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">تفاصيل الحساب</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['recipient_account_details'] ?? 'غير محدد'); ?></span>
                </div>
            </div>
            
            <!-- تفاصيل التحويل -->
            <div class="mb-4">
                <h6 class="text-primary mb-3">تفاصيل التحويل</h6>
                <div class="info-row">
                    <span class="info-label">تاريخ التحويل</span>
                    <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($transfer['created_at'])); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">طريقة التحويل</span>
                    <span class="info-value">
                        <?php 
                        $methods = [
                            'cash' => 'استلام نقدي',
                            'bank_transfer' => 'تحويل بنكي',
                            'online' => 'خدمة إلكترونية'
                        ];
                        echo $methods[$transfer['transfer_method']] ?? $transfer['transfer_method'];
                        ?>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">الفرع</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['branch_name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الموظف</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['created_by_name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الحالة الحالية</span>
                    <span class="info-value">
                        <?php 
                        $status_labels = [
                            'pending' => 'معلق',
                            'sent' => 'مرسل',
                            'received' => 'مستلم',
                            'paid' => 'مدفوع',
                            'cancelled' => 'ملغي'
                        ];
                        $status_class = [
                            'pending' => 'warning',
                            'sent' => 'info',
                            'received' => 'primary',
                            'paid' => 'success',
                            'cancelled' => 'danger'
                        ];
                        ?>
                        <span class="badge bg-<?php echo $status_class[$transfer['status']] ?? 'secondary'; ?>">
                            <?php echo $status_labels[$transfer['status']] ?? $transfer['status']; ?>
                        </span>
                    </span>
                </div>
            </div>
            
            <!-- تتبع الحالة -->
            <?php if (!empty($transfer['status_history'])): ?>
            <div class="mb-4">
                <h6 class="text-primary mb-3">تتبع حالة التحويل</h6>
                <div class="status-timeline">
                    <?php foreach ($transfer['status_history'] as $history): ?>
                    <div class="timeline-item">
                        <div class="timeline-icon <?php echo $history['new_status'] == $transfer['status'] ? 'current' : 'completed'; ?>">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <div class="fw-bold">
                                <?php echo $status_labels[$history['new_status']] ?? $history['new_status']; ?>
                            </div>
                            <div class="text-muted small">
                                <?php echo date('Y-m-d H:i', strtotime($history['status_date'])); ?>
                                بواسطة: <?php echo htmlspecialchars($history['changed_by_name']); ?>
                            </div>
                            <?php if ($history['notes']): ?>
                            <div class="text-muted small">
                                <?php echo htmlspecialchars($history['notes']); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- ملاحظات -->
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:</h6>
                <ul class="mb-0 small">
                    <li>يرجى الاحتفاظ بهذا الإيصال كإثبات للتحويل</li>
                    <li>يمكن تتبع حالة التحويل باستخدام الرقم المرجعي</li>
                    <li>في حالة وجود أي استفسار، يرجى التواصل مع خدمة العملاء</li>
                    <li>جميع التحويلات خاضعة لشروط وأحكام الشركة</li>
                </ul>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="d-flex gap-2 no-print">
                <button onclick="window.print()" class="btn btn-primary flex-fill">
                    <i class="fas fa-print me-2"></i>
                    طباعة الإيصال
                </button>
                <a href="transfers.php" class="btn btn-outline-secondary flex-fill">
                    <i class="fas fa-plus me-2"></i>
                    تحويل جديد
                </a>
                <a href="transfer_details.php?id=<?php echo $transfer['id']; ?>" class="btn btn-outline-info flex-fill">
                    <i class="fas fa-eye me-2"></i>
                    التفاصيل
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
