<?php
require_once '../config.php';
require_once '../includes/auth.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

$current_user = $auth->getCurrentUser();

// معالجة البحث في المساعدة
$search_query = $_GET['search'] ?? '';
$help_section = $_GET['section'] ?? 'overview';

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'المساعدة والدعم الفني - ' . SYSTEM_NAME;
$page_header = 'المساعدة والدعم الفني';
$page_subtitle = 'دليل الاستخدام والأسئلة الشائعة';
$page_icon = 'fas fa-question-circle';
$show_breadcrumb = true;

// إضافة CSS خاص بصفحة المساعدة
$additional_css = [
    'help-page.css'
];

// إضافة JavaScript خاص بصفحة المساعدة
$additional_js = [
    'help-page.js'
];
?>
<?php include '../includes/header.php'; ?>

<div class="container-fluid p-4">
    <!-- شريط البحث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card help-search-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="input-group input-group-lg">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="helpSearch" 
                                       placeholder="ابحث في المساعدة..." value="<?php echo htmlspecialchars($search_query); ?>">
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="help-quick-stats">
                                <span class="badge bg-primary me-2">
                                    <i class="fas fa-book me-1"></i>
                                    12 دليل
                                </span>
                                <span class="badge bg-info me-2">
                                    <i class="fas fa-question me-1"></i>
                                    45 سؤال شائع
                                </span>
                                <span class="badge bg-success">
                                    <i class="fas fa-video me-1"></i>
                                    8 فيديو
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الشريط الجانبي للتنقل -->
        <div class="col-lg-3">
            <div class="card help-navigation">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        أقسام المساعدة
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="help.php?section=overview" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'overview' ? 'active' : ''; ?>">
                            <i class="fas fa-home me-2"></i>
                            نظرة عامة
                        </a>
                        <a href="help.php?section=getting-started" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'getting-started' ? 'active' : ''; ?>">
                            <i class="fas fa-play-circle me-2"></i>
                            البدء السريع
                        </a>
                        <a href="help.php?section=user-management" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'user-management' ? 'active' : ''; ?>">
                            <i class="fas fa-users me-2"></i>
                            إدارة المستخدمين
                        </a>
                        <a href="help.php?section=customers" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'customers' ? 'active' : ''; ?>">
                            <i class="fas fa-user-friends me-2"></i>
                            إدارة العملاء
                        </a>
                        <a href="help.php?section=exchange" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'exchange' ? 'active' : ''; ?>">
                            <i class="fas fa-exchange-alt me-2"></i>
                            عمليات الصرافة
                        </a>
                        <a href="help.php?section=transfers" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'transfers' ? 'active' : ''; ?>">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            التحويلات المالية
                        </a>
                        <a href="help.php?section=cash-management" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'cash-management' ? 'active' : ''; ?>">
                            <i class="fas fa-university me-2"></i>
                            إدارة الصناديق والبنوك
                        </a>
                        <a href="help.php?section=reports" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'reports' ? 'active' : ''; ?>">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير المالية
                        </a>
                        <a href="help.php?section=security" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'security' ? 'active' : ''; ?>">
                            <i class="fas fa-shield-alt me-2"></i>
                            الأمان والحماية
                        </a>
                        <a href="help.php?section=troubleshooting" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'troubleshooting' ? 'active' : ''; ?>">
                            <i class="fas fa-tools me-2"></i>
                            استكشاف الأخطاء
                        </a>
                        <a href="help.php?section=faq" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'faq' ? 'active' : ''; ?>">
                            <i class="fas fa-question-circle me-2"></i>
                            الأسئلة الشائعة
                        </a>
                        <a href="help.php?section=contact" 
                           class="list-group-item list-group-item-action <?php echo $help_section == 'contact' ? 'active' : ''; ?>">
                            <i class="fas fa-phone me-2"></i>
                            اتصل بنا
                        </a>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        روابط سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="../README.md" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-book me-2"></i>
                            دليل النظام الكامل
                        </a>
                        <a href="../QUICK_START.md" target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-rocket me-2"></i>
                            دليل البدء السريع
                        </a>
                        <a href="settings.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات النظام
                        </a>
                        <a href="profile.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-user me-2"></i>
                            الملف الشخصي
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-lg-9">
            <div class="help-content">
                <?php
                // تحديد المحتوى حسب القسم المختار
                switch ($help_section) {
                    case 'overview':
                        include 'help_sections/overview.php';
                        break;
                    case 'getting-started':
                        include 'help_sections/getting_started.php';
                        break;
                    case 'user-management':
                        include 'help_sections/user_management.php';
                        break;
                    case 'customers':
                        include 'help_sections/customers.php';
                        break;
                    case 'exchange':
                        include 'help_sections/exchange.php';
                        break;
                    case 'transfers':
                        include 'help_sections/transfers.php';
                        break;
                    case 'cash-management':
                        include 'help_sections/cash_management.php';
                        break;
                    case 'reports':
                        include 'help_sections/reports.php';
                        break;
                    case 'security':
                        include 'help_sections/security.php';
                        break;
                    case 'troubleshooting':
                        include 'help_sections/troubleshooting.php';
                        break;
                    case 'faq':
                        include 'help_sections/faq.php';
                        break;
                    case 'contact':
                        include 'help_sections/contact.php';
                        break;
                    default:
                        include 'help_sections/overview.php';
                        break;
                }
                ?>
            </div>
        </div>
    </div>
</div>

<!-- نافذة منبثقة للفيديوهات التعليمية -->
<div class="modal fade" id="videoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">فيديو تعليمي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="ratio ratio-16x9">
                    <iframe id="videoFrame" src="" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// البحث في المساعدة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('helpSearch');
    const helpContent = document.querySelector('.help-content');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            performHelpSearch(query);
        });
    }
    
    // تفعيل الروابط السريعة
    document.querySelectorAll('.quick-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.dataset.section;
            loadHelpSection(section);
        });
    });
    
    // تفعيل الفيديوهات التعليمية
    document.querySelectorAll('.video-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const videoUrl = this.dataset.video;
            showVideo(videoUrl);
        });
    });
});

function performHelpSearch(query) {
    if (query.length < 2) {
        return;
    }
    
    // البحث في المحتوى المحلي
    const contentElements = document.querySelectorAll('.help-content h1, .help-content h2, .help-content h3, .help-content p, .help-content li');
    let results = [];
    
    contentElements.forEach(element => {
        if (element.textContent.toLowerCase().includes(query)) {
            results.push({
                element: element,
                text: element.textContent,
                type: element.tagName.toLowerCase()
            });
        }
    });
    
    highlightSearchResults(results, query);
}

function highlightSearchResults(results, query) {
    // إزالة التمييز السابق
    document.querySelectorAll('.search-highlight').forEach(el => {
        el.classList.remove('search-highlight');
    });
    
    // تمييز النتائج الجديدة
    results.forEach(result => {
        result.element.classList.add('search-highlight');
    });
    
    // التمرير إلى أول نتيجة
    if (results.length > 0) {
        results[0].element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

function loadHelpSection(section) {
    window.location.href = `help.php?section=${section}`;
}

function showVideo(videoUrl) {
    const modal = new bootstrap.Modal(document.getElementById('videoModal'));
    const iframe = document.getElementById('videoFrame');
    iframe.src = videoUrl;
    modal.show();
    
    // تنظيف الفيديو عند إغلاق النافذة
    document.getElementById('videoModal').addEventListener('hidden.bs.modal', function() {
        iframe.src = '';
    });
}

// تفعيل tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});
</script>

<?php include '../includes/footer.php'; ?>
