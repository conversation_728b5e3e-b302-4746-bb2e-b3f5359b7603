<?php
require_once '../includes/auth.php';
require_once '../includes/reports_manager.php';

header('Content-Type: application/json');

$auth = new Auth();
$reportsManager = new ReportsManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    echo json_encode(['success' => false, 'message' => 'جلسة غير صحيحة']);
    exit();
}

if (!$auth->hasPermission('reports.financial')) {
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية للوصول إلى التقارير المالية']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit();
}

$current_user = $auth->getCurrentUser();

// استلام البيانات
$report_type = $_POST['report_type'] ?? '';
$date_from = $_POST['date_from'] ?? '';
$date_to = $_POST['date_to'] ?? '';
$branch_id = !empty($_POST['branch_id']) ? intval($_POST['branch_id']) : null;
$report_name = $_POST['report_name'] ?? '';
$save_report = isset($_POST['save_report']) && $_POST['save_report'] === 'on';

// التحقق من صحة البيانات
if (empty($report_type)) {
    echo json_encode(['success' => false, 'message' => 'نوع التقرير مطلوب']);
    exit();
}

// التحقق من التواريخ للتقارير التي تحتاج فترة زمنية
$period_reports = ['profit_loss', 'cash_flow', 'operations_summary'];
if (in_array($report_type, $period_reports)) {
    if (empty($date_from) || empty($date_to)) {
        echo json_encode(['success' => false, 'message' => 'التواريخ مطلوبة لهذا النوع من التقارير']);
        exit();
    }
    
    if (strtotime($date_from) > strtotime($date_to)) {
        echo json_encode(['success' => false, 'message' => 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية']);
        exit();
    }
}

// التحقق من التاريخ للتقارير التي تحتاج تاريخ واحد
$snapshot_reports = ['balance_sheet', 'trial_balance'];
if (in_array($report_type, $snapshot_reports)) {
    $as_of_date = $date_to; // استخدام تاريخ النهاية كتاريخ الميزانية
    if (empty($as_of_date)) {
        echo json_encode(['success' => false, 'message' => 'التاريخ مطلوب لهذا النوع من التقارير']);
        exit();
    }
}

try {
    $result = null;
    
    // إنشاء التقرير حسب النوع
    switch ($report_type) {
        case 'profit_loss':
            $result = $reportsManager->generateProfitLossReport($date_from, $date_to, $branch_id);
            break;
            
        case 'balance_sheet':
            $result = $reportsManager->generateBalanceSheet($as_of_date, $branch_id);
            break;
            
        case 'cash_flow':
            $result = $reportsManager->generateCashFlowReport($date_from, $date_to, $branch_id);
            break;
            
        case 'trial_balance':
            $result = $reportsManager->generateTrialBalance($as_of_date, $branch_id);
            break;
            
        case 'operations_summary':
            $result = $reportsManager->generateOperationsSummary($date_from, $date_to, $branch_id);
            break;
            
        case 'compliance_report':
            $result = generateComplianceReport($date_from, $date_to, $branch_id);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'نوع التقرير غير مدعوم']);
            exit();
    }
    
    if (!$result['success']) {
        echo json_encode($result);
        exit();
    }
    
    // حفظ التقرير إذا طُلب ذلك
    if ($save_report) {
        // إنشاء اسم تلقائي إذا لم يتم تحديد اسم
        if (empty($report_name)) {
            $report_types_ar = [
                'profit_loss' => 'تقرير الأرباح والخسائر',
                'balance_sheet' => 'الميزانية العمومية',
                'cash_flow' => 'تقرير التدفق النقدي',
                'trial_balance' => 'ميزان المراجعة',
                'operations_summary' => 'ملخص العمليات',
                'compliance_report' => 'تقرير الامتثال'
            ];
            
            $report_name = $report_types_ar[$report_type] ?? $report_type;
            $report_name .= ' - ' . date('Y-m-d H:i');
        }
        
        $save_result = $reportsManager->saveReport(
            $report_name,
            $report_type,
            $result['data'],
            $current_user['id']
        );
        
        if ($save_result['success']) {
            $result['data']['saved_report_id'] = $save_result['report_id'];
            $result['data']['saved_message'] = 'تم حفظ التقرير بنجاح';
        }
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في إنشاء التقرير: ' . $e->getMessage()
    ]);
}

/**
 * إنشاء تقرير الامتثال
 */
function generateComplianceReport($date_from, $date_to, $branch_id = null) {
    try {
        $database = new Database();
        $db = $database->getConnection();
        
        $where_conditions = ['DATE(c.check_date) BETWEEN :date_from AND :date_to'];
        $params = [':date_from' => $date_from, ':date_to' => $date_to];
        
        if ($branch_id) {
            $where_conditions[] = 'c.customer_id IN (SELECT id FROM customers WHERE branch_id = :branch_id)';
            $params[':branch_id'] = $branch_id;
        }
        
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        
        // إحصائيات فحوصات الامتثال
        $stmt = $db->prepare("
            SELECT 
                c.check_type,
                c.check_result,
                COUNT(*) as count,
                AVG(c.risk_score) as avg_risk_score
            FROM compliance_checks c
            $where_clause
            GROUP BY c.check_type, c.check_result
            ORDER BY c.check_type, c.check_result
        ");
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $compliance_stats = $stmt->fetchAll();
        
        // العملاء عالي المخاطر
        $stmt = $db->prepare("
            SELECT 
                cu.full_name,
                cu.id_number,
                cu.risk_level,
                MAX(c.risk_score) as max_risk_score,
                COUNT(c.id) as total_checks
            FROM customers cu
            JOIN compliance_checks c ON cu.id = c.customer_id
            $where_clause AND cu.risk_level IN ('high', 'medium')
            GROUP BY cu.id, cu.full_name, cu.id_number, cu.risk_level
            ORDER BY max_risk_score DESC
            LIMIT 20
        ");
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $high_risk_customers = $stmt->fetchAll();
        
        // المعاملات عالية القيمة
        $stmt = $db->prepare("
            SELECT 
                t.reference_number,
                t.transaction_type,
                t.total_amount_base_currency,
                t.transaction_date,
                cu.full_name as customer_name
            FROM transactions t
            LEFT JOIN customers cu ON t.customer_id = cu.id
            WHERE DATE(t.transaction_date) BETWEEN :date_from AND :date_to
            AND t.total_amount_base_currency >= 10000
            " . ($branch_id ? "AND t.branch_id = :branch_id" : "") . "
            ORDER BY t.total_amount_base_currency DESC
            LIMIT 50
        ");
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();
        $high_value_transactions = $stmt->fetchAll();
        
        // تنظيم البيانات
        $compliance_summary = [
            'kyc_verification' => ['pass' => 0, 'fail' => 0, 'pending' => 0, 'requires_review' => 0],
            'aml_screening' => ['pass' => 0, 'fail' => 0, 'pending' => 0, 'requires_review' => 0],
            'sanctions_check' => ['pass' => 0, 'fail' => 0, 'pending' => 0, 'requires_review' => 0],
            'pep_check' => ['pass' => 0, 'fail' => 0, 'pending' => 0, 'requires_review' => 0],
            'risk_assessment' => ['pass' => 0, 'fail' => 0, 'pending' => 0, 'requires_review' => 0]
        ];
        
        foreach ($compliance_stats as $stat) {
            if (isset($compliance_summary[$stat['check_type']])) {
                $compliance_summary[$stat['check_type']][$stat['check_result']] = intval($stat['count']);
            }
        }
        
        // حساب نقاط الامتثال
        $total_checks = 0;
        $passed_checks = 0;
        
        foreach ($compliance_summary as $check_type => $results) {
            $total_checks += array_sum($results);
            $passed_checks += $results['pass'];
        }
        
        $compliance_score = $total_checks > 0 ? ($passed_checks / $total_checks) * 100 : 100;
        
        return [
            'success' => true,
            'data' => [
                'period' => ['from' => $date_from, 'to' => $date_to],
                'compliance_summary' => $compliance_summary,
                'compliance_score' => $compliance_score,
                'total_checks' => $total_checks,
                'passed_checks' => $passed_checks,
                'high_risk_customers' => $high_risk_customers,
                'high_value_transactions' => $high_value_transactions,
                'high_value_count' => count($high_value_transactions),
                'generated_at' => date('Y-m-d H:i:s')
            ]
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ في إنشاء تقرير الامتثال: ' . $e->getMessage()];
    }
}
?>
