<?php
require_once '../config.php';
require_once '../includes/auth.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'لوحة التحكم - ' . SYSTEM_NAME;
$page_header = 'لوحة التحكم';
$page_subtitle = 'مرحباً بك في نظام Trust Plus';
$page_icon = 'fas fa-tachometer-alt';
$show_breadcrumb = false;
?>
<?php include '../includes/header.php'; ?>
<!-- محتوى لوحة التحكم -->
<div class="dashboard-container">
    <div class="container-fluid p-4">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="mb-0">مرحباً، <?php echo htmlspecialchars($current_user['full_name']); ?></h2>
                    <p class="text-muted">نظرة عامة على أنشطة اليوم</p>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card" data-stat="total_customers">
                        <div class="stat-icon bg-primary-gradient">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-value">0</div>
                        <div class="stat-label">إجمالي العملاء</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> +0%
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card" data-stat="daily_exchanges">
                        <div class="stat-icon bg-success-gradient">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="stat-value">0</div>
                        <div class="stat-label">عمليات الصرافة اليوم</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> +0%
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card" data-stat="pending_transfers">
                        <div class="stat-icon bg-warning-gradient">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="stat-value">0</div>
                        <div class="stat-label">التحويلات المعلقة</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-down"></i> -0%
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card" data-stat="daily_revenue">
                        <div class="stat-icon bg-info-gradient">
                            <i class="fas fa-shekel-sign"></i>
                        </div>
                        <div class="stat-value">₪0</div>
                        <div class="stat-label">إجمالي الإيرادات اليوم</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> +0%
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="quick-actions">
                        <h5 class="mb-3">
                            <i class="fas fa-bolt"></i>
                            الإجراءات السريعة
                        </h5>
                        <div class="action-grid">
                            <?php if ($auth->hasPermission('customers.create')): ?>
                            <a href="customers.php?action=add" class="action-btn btn-primary-action">
                                <i class="fas fa-user-plus"></i>
                                <span>إضافة عميل جديد</span>
                            </a>
                            <?php endif; ?>

                            <?php if ($auth->hasPermission('exchange.create')): ?>
                            <a href="exchange.php" class="action-btn btn-success-action">
                                <i class="fas fa-exchange-alt"></i>
                                <span>عملية صرافة جديدة</span>
                            </a>
                            <?php endif; ?>

                            <?php if ($auth->hasPermission('exchange.rates')): ?>
                            <a href="exchange_rates.php" class="action-btn btn-info-action">
                                <i class="fas fa-chart-line"></i>
                                <span>تحديث أسعار الصرف</span>
                            </a>
                            <?php endif; ?>

                            <?php if ($auth->hasPermission('transfers.create')): ?>
                            <a href="transfers.php?action=add" class="action-btn btn-warning-action">
                                <i class="fas fa-paper-plane"></i>
                                <span>تحويل مالي جديد</span>
                            </a>
                            <?php endif; ?>

                            <?php if ($auth->hasPermission('reports.financial')): ?>
                            <a href="financial_dashboard.php" class="action-btn btn-info-action">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>لوحة الأداء المالي</span>
                            </a>
                            <?php endif; ?>

                            <?php if ($auth->hasPermission('reports.financial')): ?>
                            <a href="reports.php" class="action-btn">
                                <i class="fas fa-file-chart-line"></i>
                                <span>التقارير المالية</span>
                            </a>
                            <?php endif; ?>

                            <?php if ($auth->hasPermission('customers.view')): ?>
                            <a href="customers.php" class="action-btn">
                                <i class="fas fa-users"></i>
                                <span>إدارة العملاء</span>
                            </a>
                            <?php endif; ?>

                            <?php if ($auth->hasPermission('settings.view')): ?>
                            <a href="settings.php" class="action-btn">
                                <i class="fas fa-cog"></i>
                                <span>إعدادات النظام</span>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
