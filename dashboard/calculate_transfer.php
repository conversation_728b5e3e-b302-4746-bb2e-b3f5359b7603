<?php
require_once '../includes/auth.php';
require_once '../includes/transfer_manager.php';

header('Content-Type: application/json');

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    echo json_encode(['success' => false, 'message' => 'جلسة غير صحيحة']);
    exit();
}

if (!$auth->hasPermission('transfers.view')) {
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية للوصول']);
    exit();
}

// قراءة البيانات من الطلب
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit();
}

$sending_currency_id = intval($input['sending_currency_id'] ?? 0);
$receiving_currency_id = intval($input['receiving_currency_id'] ?? 0);
$sending_amount = floatval($input['sending_amount'] ?? 0);
$recipient_country = trim($input['recipient_country'] ?? '');

if (!$sending_currency_id || !$receiving_currency_id || !$sending_amount || !$recipient_country) {
    echo json_encode(['success' => false, 'message' => 'بيانات مطلوبة مفقودة']);
    exit();
}

try {
    $transferManager = new TransferManager();
    $current_user = $auth->getCurrentUser();
    
    // الحصول على إعدادات التحويل
    $database = new Database();
    $db = $database->getConnection();
    
    // الحصول على رموز العملات
    $stmt = $db->prepare("SELECT code FROM currencies WHERE id = :id");
    $stmt->bindParam(':id', $sending_currency_id);
    $stmt->execute();
    $sending_currency_code = $stmt->fetch()['code'] ?? '';
    
    $stmt = $db->prepare("SELECT code FROM currencies WHERE id = :id");
    $stmt->bindParam(':id', $receiving_currency_id);
    $stmt->execute();
    $receiving_currency_code = $stmt->fetch()['code'] ?? '';
    
    $corridor = $sending_currency_code . '_' . $receiving_currency_code;
    
    // الحصول على إعدادات التحويل
    $stmt = $db->prepare("
        SELECT * FROM transfer_settings
        WHERE corridor = :corridor AND to_country = :to_country AND is_active = 1
    ");
    $stmt->bindParam(':corridor', $corridor);
    $stmt->bindParam(':to_country', $recipient_country);
    $stmt->execute();
    
    $settings = $stmt->fetch();
    
    // إعدادات افتراضية إذا لم توجد
    if (!$settings) {
        $settings = [
            'base_fee' => 5.00,
            'percentage_fee' => 0.0050,
            'exchange_margin' => 0.0025,
            'processing_time_hours' => 24,
            'partner_commission' => 0
        ];
    }
    
    // الحصول على سعر الصرف
    $stmt = $db->prepare("
        SELECT sell_rate FROM exchange_rates
        WHERE from_currency_id = :from_currency_id 
          AND to_currency_id = :to_currency_id
          AND is_active = 1
          AND effective_date <= CURDATE()
        ORDER BY effective_date DESC
        LIMIT 1
    ");
    
    $stmt->bindParam(':from_currency_id', $sending_currency_id);
    $stmt->bindParam(':to_currency_id', $receiving_currency_id);
    $stmt->execute();
    
    $rate_result = $stmt->fetch();
    
    if (!$rate_result) {
        echo json_encode(['success' => false, 'message' => 'سعر الصرف غير متوفر']);
        exit();
    }
    
    $exchange_rate = $rate_result['sell_rate'];
    
    // تطبيق هامش الربح على سعر الصرف
    $rate_with_margin = $exchange_rate * (1 - $settings['exchange_margin']);
    
    // حساب المبلغ المستلم
    $receiving_amount = $sending_amount * $rate_with_margin;
    
    // حساب رسوم التحويل
    $percentage_fee = $sending_amount * $settings['percentage_fee'];
    $transfer_fee = max($percentage_fee, $settings['base_fee']);
    
    // حساب الأرباح
    $exchange_profit = $sending_amount * $exchange_rate * $settings['exchange_margin'];
    $fee_profit = $transfer_fee - ($settings['partner_commission'] ?? 0);
    $total_profit = $exchange_profit + $fee_profit;
    
    echo json_encode([
        'success' => true,
        'receiving_amount' => round($receiving_amount, 2),
        'exchange_rate' => $rate_with_margin,
        'transfer_fee' => round($transfer_fee, 2),
        'exchange_profit' => round($exchange_profit, 2),
        'fee_profit' => round($fee_profit, 2),
        'total_profit' => round($total_profit, 2),
        'processing_time_hours' => $settings['processing_time_hours']
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في النظام: ' . $e->getMessage()]);
}
?>
