<?php
require_once '../includes/auth.php';
require_once '../includes/exchange_rate_api.php';

$auth = new Auth();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('exchange.rates')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى هذه الصفحة');
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'تحديث أسعار الصرف من API - ' . SYSTEM_NAME;
$page_header = 'تحديث أسعار الصرف من API';
$page_subtitle = 'إدارة مصادر بيانات أسعار الصرف وتحديثها تلقائياً';
$page_icon = 'fas fa-cloud-download-alt';
$show_breadcrumb = true;

$error_message = '';
$success_message = '';

// تهيئة كائن API
$exchangeRateAPI = new ExchangeRateAPI();

// تحديد المزود الافتراضي
$current_provider = isset($_GET['provider']) ? $_GET['provider'] : 'exchangerate-api';

// قائمة بمزودي البيانات المتاحين
$providers = [
    'exchangerate-api' => [
        'name' => 'Exchange Rate API',
        'description' => 'API مجاني يوفر أسعار صرف مباشرة',
        'url' => 'https://www.exchangerate-api.com/',
        'requires_key' => false,
        'free' => true,
        'icon' => 'fas fa-dollar-sign'
    ],
    'frankfurter' => [
        'name' => 'Frankfurter',
        'description' => 'خدمة مفتوحة المصدر تعتمد على بيانات البنك المركزي الأوروبي',
        'url' => 'https://www.frankfurter.app/',
        'requires_key' => false,
        'free' => true,
        'icon' => 'fas fa-euro-sign'
    ],
    'openexchangerates' => [
        'name' => 'Open Exchange Rates',
        'description' => 'خدمة تطرح بيانات عبر API مع حدود استدعاء مجانية',
        'url' => 'https://openexchangerates.org/',
        'requires_key' => true,
        'free' => true,
        'icon' => 'fas fa-exchange-alt'
    ]
];

// إذا تم الضغط على زر التحديث
if (isset($_GET['action']) && $_GET['action'] == 'update') {
    // تعيين مزود البيانات المحدد
    $exchangeRateAPI->setProvider($current_provider);
    
    // تنفيذ تحديث الأسعار
    $result = $exchangeRateAPI->updateRatesFromAPI($current_user['id']);
    
    if ($result['success']) {
        $success_message = $result['message'];
    } else {
        $error_message = $result['message'];
    }
}

// معلومات عن آخر تحديث
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->prepare("
        SELECT e.*, u.full_name as user_name
        FROM system_events e
        LEFT JOIN users u ON e.user_id = u.id
        WHERE e.event_type = 'api_rates_updated'
        ORDER BY e.created_at DESC
        LIMIT 1
    ");
    $stmt->execute();
    $last_update = $stmt->fetch();
    
} catch (Exception $e) {
    $last_update = null;
}

include '../includes/header.php';
?>

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">تحديث أسعار الصرف من API</h2>
                    <p class="text-muted">إدارة مصادر بيانات أسعار الصرف وتحديثها تلقائياً</p>
                </div>
                <div>
                    <a href="exchange_rates.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لأسعار الصرف
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>

    <!-- آخر تحديث -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-history me-2"></i> معلومات آخر تحديث</h5>
        </div>
        <div class="card-body">
            <?php if ($last_update): ?>
                <?php $details = json_decode($last_update['event_details'], true); ?>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ التحديث</label>
                            <div class="h5"><?php echo date('Y-m-d H:i', strtotime($last_update['created_at'])); ?></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted">مصدر البيانات</label>
                            <div class="h5">
                                <?php echo isset($providers[$details['source']]) ? $providers[$details['source']]['name'] : $details['source']; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label text-muted">عدد الأزواج المحدثة</label>
                            <div class="h5"><?php echo number_format($details['updated_pairs']); ?></div>
                        </div>
                    </div>
                </div>
                <div class="mb-2">
                    <label class="form-label text-muted">تم بواسطة</label>
                    <div><?php echo htmlspecialchars($last_update['user_name']); ?></div>
                </div>
                <div class="mb-0">
                    <label class="form-label text-muted">العملة الأساسية</label>
                    <div><?php echo htmlspecialchars($details['base_currency'] ?? 'USD'); ?></div>
                </div>
            <?php else: ?>
                <div class="text-center p-4">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">لم يتم تنفيذ أي تحديث بعد</h6>
                    <p class="text-muted">قم بتشغيل التحديث التلقائي لأول مرة لعرض المعلومات هنا</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- مزودي البيانات -->
    <div class="row mb-4">
        <?php foreach ($providers as $provider_key => $provider): ?>
            <div class="col-md-4 mb-4">
                <div class="card h-100 <?php echo $provider_key == $current_provider ? 'border-primary' : ''; ?>">
                    <div class="card-header <?php echo $provider_key == $current_provider ? 'bg-primary text-white' : 'bg-light'; ?>">
                        <h5 class="mb-0">
                            <i class="<?php echo $provider['icon']; ?> me-2"></i>
                            <?php echo $provider['name']; ?>
                            <?php if ($provider_key == $current_provider): ?>
                                <span class="badge bg-white text-primary float-end">مُحدد</span>
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <p><?php echo $provider['description']; ?></p>
                        
                        <div class="mb-3">
                            <span class="badge <?php echo $provider['free'] ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $provider['free'] ? 'مجاني' : 'مدفوع'; ?>
                            </span>
                            
                            <?php if ($provider['requires_key']): ?>
                                <span class="badge bg-warning text-dark">يتطلب مفتاح API</span>
                            <?php else: ?>
                                <span class="badge bg-info">لا يتطلب مفتاح</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="<?php echo $provider['url']; ?>" class="btn btn-sm btn-outline-secondary" target="_blank">
                                زيارة الموقع
                            </a>
                            
                            <?php if ($provider_key != $current_provider): ?>
                                <a href="?provider=<?php echo $provider_key; ?>" class="btn btn-sm btn-primary">
                                    اختيار هذا المزود
                                </a>
                            <?php else: ?>
                                <a href="?action=update&provider=<?php echo $provider_key; ?>" class="btn btn-sm btn-success">
                                    <i class="fas fa-sync me-1"></i>
                                    تحديث الأسعار
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- التحديث التلقائي -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-clock me-2"></i> التحديث التلقائي للأسعار</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <h6>إعداد التحديث التلقائي باستخدام Cron Job</h6>
                        <p>يمكنك إعداد وظيفة كرون (Cron Job) لتحديث الأسعار تلقائياً كل عدة ساعات باستخدام الأمر التالي:</p>
                        <div class="bg-light p-3 rounded">
                            <code>0 */4 * * * /usr/bin/php <?php echo $_SERVER['DOCUMENT_ROOT']; ?>/cron/update_exchange_rates.php >> /var/log/exchange-rates.log 2>&1</code>
                        </div>
                        <small class="text-muted">هذا الأمر سيقوم بتحديث الأسعار كل 4 ساعات.</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>اختيار وتيرة التحديث</h6>
                    <p>للأغراض التجارية، ينصح بتحديث أسعار العملات عدة مرات في اليوم للحصول على أسعار دقيقة. اختر الوتيرة المناسبة لنشاطك:</p>
                    <ul>
                        <li><code>0 */2 * * *</code> - كل ساعتين</li>
                        <li><code>0 */4 * * *</code> - كل 4 ساعات</li>
                        <li><code>0 */8 * * *</code> - 3 مرات في اليوم</li>
                        <li><code>0 0 * * *</code> - مرة واحدة يومياً (منتصف الليل)</li>
                    </ul>
                </div>
            </div>
            
            <hr>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> تأكد من أن خادمك يدعم جدولة المهام (Cron Jobs) ولديك الصلاحيات اللازمة لإعدادها. يمكنك أيضاً استخدام خدمات مجدولة المهام عبر الإنترنت للقيام بهذه العملية.
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?> 