<?php
require_once '../includes/auth.php';
require_once '../includes/reports_manager.php';

$auth = new Auth();
$reportsManager = new ReportsManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('accounting.view')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى المحاسبة');
    exit();
}

// إعدادات الصفحة
$page_type = 'dashboard';

$current_user = $auth->getCurrentUser();

// فلاتر
$as_of_date = $_GET['as_of_date'] ?? date('Y-m-d');
$branch_id = $_GET['branch_id'] ?? '';

// رسائل النظام
$error_message = '';
$success_message = '';

// معالجة إضافة قيد يدوي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['entry_date'], $_POST['entry_branch_id'], $_POST['entry_account_id'], $_POST['entry_debit'], $_POST['entry_credit'])) {
    $entry_date = $_POST['entry_date'];
    $entry_branch_id = $_POST['entry_branch_id'];
    $entry_description = trim($_POST['entry_description'] ?? '');
    $account_ids = $_POST['entry_account_id'];
    $debits = $_POST['entry_debit'];
    $credits = $_POST['entry_credit'];

    $total_debit = 0;
    $total_credit = 0;
    $details = [];
    for ($i = 0; $i < count($account_ids); $i++) {
        $acc_id = intval($account_ids[$i]);
        $debit = floatval($debits[$i]);
        $credit = floatval($credits[$i]);
        if ($acc_id && ($debit > 0 || $credit > 0)) {
            $details[] = [
                'account_id' => $acc_id,
                'debit' => $debit,
                'credit' => $credit
            ];
            $total_debit += $debit;
            $total_credit += $credit;
        }
    }
    if (count($details) < 2) {
        $error_message = 'يجب إدخال حسابين على الأقل.';
    } elseif (abs($total_debit - $total_credit) > 0.01) {
        $error_message = 'يجب أن يتساوى مجموع المدين مع مجموع الدائن.';
    } else {
        try {
            $database = new Database();
            $db = $database->getConnection();
            $db->beginTransaction();
            // إدراج القيد الرئيسي
            $stmt = $db->prepare("INSERT INTO transactions (transaction_date, branch_id, description, transaction_type, created_by) VALUES (:date, :branch_id, :desc, 'manual', :user_id)");
            $stmt->bindParam(':date', $entry_date);
            $stmt->bindParam(':branch_id', $entry_branch_id);
            $stmt->bindParam(':desc', $entry_description);
            $stmt->bindParam(':user_id', $current_user['id']);
            $stmt->execute();
            $transaction_id = $db->lastInsertId();
            // إدراج تفاصيل القيد
            $stmt = $db->prepare("INSERT INTO transaction_details (transaction_id, account_id, debit, credit) VALUES (:tx, :acc, :debit, :credit)");
            foreach ($details as $row) {
                $stmt->bindParam(':tx', $transaction_id);
                $stmt->bindParam(':acc', $row['account_id']);
                $stmt->bindParam(':debit', $row['debit']);
                $stmt->bindParam(':credit', $row['credit']);
                $stmt->execute();
            }
            $db->commit();
            $success_message = 'تم حفظ القيد بنجاح.';
        } catch (Exception $e) {
            if ($db && $db->inTransaction()) $db->rollBack();
            $error_message = 'حدث خطأ أثناء حفظ القيد: ' . $e->getMessage();
        }
    }
}

try {
    $database = new Database();
    $db = $database->getConnection();
    // جلب الفروع
    $stmt = $db->prepare("SELECT * FROM branches WHERE is_active = 1 ORDER BY name");
    $stmt->execute();
    $branches = $stmt->fetchAll();
    // جلب شجرة الحسابات
    $accounts = $db->query("SELECT * FROM accounts ORDER BY account_code")->fetchAll();
    // جلب ميزان المراجعة
    $trial_balance = $reportsManager->generateTrialBalance($as_of_date, $branch_id);
} catch (Exception $e) {
    $branches = [];
    $accounts = [];
    $trial_balance = ['success'=>false,'data'=>['accounts'=>[],'totals'=>[]]];
}

include '../includes/header.php';
?>
<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">المحاسبة</h2>
                    <p class="text-muted">إدارة الحسابات، القيود اليومية، وميزان المراجعة</p>
                </div>
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
    </div>
    <!-- رسائل النظام -->
    <?php if ($error_message): ?>
        <div class="alert alert-danger"> <?php echo htmlspecialchars($error_message); ?> </div>
    <?php endif; ?>
    <?php if ($success_message): ?>
        <div class="alert alert-success"> <?php echo htmlspecialchars($success_message); ?> </div>
    <?php endif; ?>
    <!-- فلاتر -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <label class="form-label">حتى تاريخ</label>
                        <input type="date" class="form-control" name="as_of_date" value="<?php echo $as_of_date; ?>">
                    </div>
                    <div class="col-md-4 mb-2">
                        <label class="form-label">الفرع</label>
                        <select class="form-control" name="branch_id">
                            <option value="">جميع الفروع</option>
                            <?php foreach ($branches as $branch): ?>
                                <option value="<?php echo $branch['id']; ?>" <?php echo $branch_id == $branch['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($branch['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4 mb-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>
                            تطبيق الفلاتر
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- شجرة الحسابات -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-sitemap me-2"></i>شجرة الحسابات</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-sm table-hover mb-0">
                    <thead>
                        <tr>
                            <th>رمز الحساب</th>
                            <th>اسم الحساب</th>
                            <th>النوع</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($accounts as $acc): ?>
                        <tr>
                            <td><code><?php echo htmlspecialchars($acc['account_code']); ?></code></td>
                            <td><?php echo htmlspecialchars($acc['account_name']); ?></td>
                            <td><?php echo htmlspecialchars($acc['account_type']); ?></td>
                            <td><?php echo htmlspecialchars($acc['description'] ?? ''); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- ميزان المراجعة -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-balance-scale me-2"></i>ميزان المراجعة</h5>
            <a href="export_journal_excel.php?as_of_date=<?php echo urlencode($as_of_date); ?>&branch_id=<?php echo urlencode($branch_id); ?>" class="btn btn-success btn-sm">
                <i class="fas fa-file-excel me-1"></i> تصدير إلى Excel
            </a>
        </div>
        <div class="card-body">
            <?php if ($trial_balance['success']): ?>
            <div class="table-responsive">
                <table class="table table-striped mb-0">
                    <thead>
                        <tr>
                            <th>رمز الحساب</th>
                            <th>اسم الحساب</th>
                            <th>مدين</th>
                            <th>دائن</th>
                            <th>رصيد مدين</th>
                            <th>رصيد دائن</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($trial_balance['data']['accounts'] as $acc): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($acc['account_code']); ?></td>
                            <td><?php echo htmlspecialchars($acc['account_name']); ?></td>
                            <td class="text-end">$<?php echo number_format($acc['total_debits'], 2); ?></td>
                            <td class="text-end">$<?php echo number_format($acc['total_credits'], 2); ?></td>
                            <td class="text-end">$<?php echo number_format($acc['debit_balance'], 2); ?></td>
                            <td class="text-end">$<?php echo number_format($acc['credit_balance'], 2); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="2">الإجمالي</th>
                            <th class="text-end">$<?php echo number_format($trial_balance['data']['totals']['total_debits'], 2); ?></th>
                            <th class="text-end">$<?php echo number_format($trial_balance['data']['totals']['total_credits'], 2); ?></th>
                            <th class="text-end">$<?php echo number_format($trial_balance['data']['totals']['debit_balances'], 2); ?></th>
                            <th class="text-end">$<?php echo number_format($trial_balance['data']['totals']['credit_balances'], 2); ?></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <?php else: ?>
                <div class="alert alert-warning">لا توجد بيانات ميزان مراجعة متاحة.</div>
            <?php endif; ?>
        </div>
    </div>
    <!-- إضافة قيد يدوي -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>إضافة قيد يومي يدوي</h5>
        </div>
        <div class="card-body">
            <form method="POST" action="#entry_form" id="entry_form">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <label class="form-label">التاريخ</label>
                        <input type="date" class="form-control" name="entry_date" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label class="form-label">الفرع</label>
                        <select class="form-control" name="entry_branch_id" required>
                            <?php foreach ($branches as $branch): ?>
                                <option value="<?php echo $branch['id']; ?>"><?php echo htmlspecialchars($branch['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label">الوصف</label>
                        <input type="text" class="form-control" name="entry_description" placeholder="وصف القيد" required>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <label class="form-label">تفاصيل القيد</label>
                        <div class="table-responsive">
                            <table class="table table-bordered mb-0" id="entry_details_table">
                                <thead>
                                    <tr>
                                        <th width="50%">الحساب</th>
                                        <th width="20%">مدين</th>
                                        <th width="20%">دائن</th>
                                        <th width="10%">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="entry_rows">
                                    <tr class="entry-row">
                                        <td>
                                            <select class="form-control entry-account" name="entry_account_id[]" required>
                                                <option value="">-- اختر الحساب --</option>
                                                <?php foreach ($accounts as $acc): ?>
                                                    <option value="<?php echo $acc['id']; ?>"><?php echo htmlspecialchars($acc['account_code'] . ' - ' . $acc['account_name']); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td><input type="number" class="form-control entry-debit" name="entry_debit[]" step="0.01" min="0" value="0"></td>
                                        <td><input type="number" class="form-control entry-credit" name="entry_credit[]" step="0.01" min="0" value="0"></td>
                                        <td class="text-center">
                                            <button type="button" class="btn btn-sm btn-danger remove-row"><i class="fas fa-times"></i></button>
                                        </td>
                                    </tr>
                                    <tr class="entry-row">
                                        <td>
                                            <select class="form-control entry-account" name="entry_account_id[]" required>
                                                <option value="">-- اختر الحساب --</option>
                                                <?php foreach ($accounts as $acc): ?>
                                                    <option value="<?php echo $acc['id']; ?>"><?php echo htmlspecialchars($acc['account_code'] . ' - ' . $acc['account_name']); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td><input type="number" class="form-control entry-debit" name="entry_debit[]" step="0.01" min="0" value="0"></td>
                                        <td><input type="number" class="form-control entry-credit" name="entry_credit[]" step="0.01" min="0" value="0"></td>
                                        <td class="text-center">
                                            <button type="button" class="btn btn-sm btn-danger remove-row"><i class="fas fa-times"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr class="table-light">
                                        <th class="text-start">المجموع</th>
                                        <th><span id="total_debit">0.00</span></th>
                                        <th><span id="total_credit">0.00</span></th>
                                        <th class="text-center">
                                            <button type="button" class="btn btn-sm btn-success" id="add_row"><i class="fas fa-plus"></i></button>
                                        </th>
                                    </tr>
                                    <tr>
                                        <th class="text-start">الفرق</th>
                                        <th colspan="3" class="text-danger"><span id="balance_diff">0.00</span></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12 text-end">
                        <button type="submit" class="btn btn-success" id="save_entry">
                            <i class="fas fa-save me-2"></i>
                            حفظ القيد
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// سكريبت إدارة قيد المحاسبة
$(document).ready(function() {
    // إضافة صف جديد
    $("#add_row").click(function() {
        let rowTemplate = `
            <tr class="entry-row">
                <td>
                    <select class="form-control entry-account" name="entry_account_id[]" required>
                        <option value="">-- اختر الحساب --</option>
                        <?php foreach ($accounts as $acc): ?>
                            <option value="<?php echo $acc['id']; ?>"><?php echo htmlspecialchars($acc['account_code'] . ' - ' . $acc['account_name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </td>
                <td><input type="number" class="form-control entry-debit" name="entry_debit[]" step="0.01" min="0" value="0"></td>
                <td><input type="number" class="form-control entry-credit" name="entry_credit[]" step="0.01" min="0" value="0"></td>
                <td class="text-center">
                    <button type="button" class="btn btn-sm btn-danger remove-row"><i class="fas fa-times"></i></button>
                </td>
            </tr>
        `;
        $("#entry_rows").append(rowTemplate);
        updateTotals();
    });
    
    // حذف صف
    $(document).on("click", ".remove-row", function() {
        if ($("#entry_rows tr").length > 2) {
            $(this).closest("tr").remove();
            updateTotals();
        } else {
            alert("يجب أن يحتوي القيد على صفين على الأقل");
        }
    });
    
    // تحديث التوتال عند تغيير القيم
    $(document).on("change", ".entry-debit, .entry-credit", function() {
        const row = $(this).closest("tr");
        
        // إذا أدخل المستخدم قيمة في المدين، يجب تصفير الدائن
        if ($(this).hasClass("entry-debit") && parseFloat($(this).val()) > 0) {
            row.find(".entry-credit").val(0);
        }
        
        // إذا أدخل المستخدم قيمة في الدائن، يجب تصفير المدين
        if ($(this).hasClass("entry-credit") && parseFloat($(this).val()) > 0) {
            row.find(".entry-debit").val(0);
        }
        
        updateTotals();
    });
    
    // تحديث المجاميع
    function updateTotals() {
        let totalDebit = 0;
        let totalCredit = 0;
        
        // حساب المجاميع
        $(".entry-debit").each(function() {
            totalDebit += parseFloat($(this).val()) || 0;
        });
        
        $(".entry-credit").each(function() {
            totalCredit += parseFloat($(this).val()) || 0;
        });
        
        // عرض المجاميع
        $("#total_debit").text(totalDebit.toFixed(2));
        $("#total_credit").text(totalCredit.toFixed(2));
        
        // حساب الفرق
        const diff = Math.abs(totalDebit - totalCredit).toFixed(2);
        $("#balance_diff").text(diff);
        
        // تغيير لون الفرق
        if (diff > 0.001) {
            $("#balance_diff").closest("th").addClass("text-danger").removeClass("text-success");
            $("#save_entry").prop("disabled", true);
        } else {
            $("#balance_diff").closest("th").addClass("text-success").removeClass("text-danger");
            $("#save_entry").prop("disabled", false);
        }
    }
    
    // التحقق من صحة النموذج قبل الإرسال
    $("#entry_form").on("submit", function(e) {
        const diff = Math.abs(parseFloat($("#total_debit").text()) - parseFloat($("#total_credit").text()));
        
        if (diff > 0.001) {
            e.preventDefault();
            alert("يجب أن يتساوى مجموع المدين مع مجموع الدائن");
            return false;
        }
        
        let accountsSelected = true;
        $(".entry-account").each(function() {
            if (!$(this).val()) {
                accountsSelected = false;
            }
        });
        
        if (!accountsSelected) {
            e.preventDefault();
            alert("يجب اختيار حساب لكل صف");
            return false;
        }
        
        // التأكد من وجود صفين على الأقل
        if ($("#entry_rows tr").length < 2) {
            e.preventDefault();
            alert("يجب أن يحتوي القيد على صفين على الأقل");
            return false;
        }
        
        return true;
    });
    
    // تهيئة المجاميع عند تحميل الصفحة
    updateTotals();
});
</script>

<?php include '../includes/footer.php'; ?> 