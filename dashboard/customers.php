<?php
require_once '../includes/auth.php';
require_once '../includes/customer_manager.php';

$auth = new Auth();
$customerManager = new CustomerManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('customers.view')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى هذه الصفحة');
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'إدارة العملاء - ' . SYSTEM_NAME;
$page_header = 'إدارة العملاء';
$page_subtitle = 'إضافة وتعديل وإدارة بيانات العملاء';
$page_icon = 'fas fa-users';
$show_breadcrumb = true;
$error_message = '';
$success_message = '';

// معالجة الإجراءات
$action = $_GET['action'] ?? 'list';
$customer_id = $_GET['id'] ?? null;

// معالجة البحث والفلاتر
$search_term = $_GET['search'] ?? '';
$kyc_filter = $_GET['kyc_status'] ?? '';
$risk_filter = $_GET['risk_level'] ?? '';
$blacklist_filter = $_GET['blacklisted'] ?? '';

$filters = [];
if ($kyc_filter) $filters['kyc_status'] = $kyc_filter;
if ($risk_filter) $filters['risk_level'] = $risk_filter;
if ($blacklist_filter !== '') $filters['blacklisted'] = (bool)$blacklist_filter;

// جلب العملاء
$customers = $customerManager->searchCustomers($search_term, $filters, 50, 0);

// معالجة تحديث حالة KYC
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] == 'update_kyc' && $auth->hasPermission('customers.kyc')) {
        $result = $customerManager->updateKYCStatus(
            $_POST['customer_id'],
            $_POST['kyc_status'],
            $current_user['id'],
            $_POST['notes'] ?? ''
        );
        
        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
        
        // إعادة جلب العملاء
        $customers = $customerManager->searchCustomers($search_term, $filters, 50, 0);
    }
    
    if ($_POST['action'] == 'update_risk' && $auth->hasPermission('customers.kyc')) {
        $result = $customerManager->updateRiskLevel(
            $_POST['customer_id'],
            $_POST['risk_level'],
            $current_user['id'],
            $_POST['reason'] ?? ''
        );
        
        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
        
        // إعادة جلب العملاء
        $customers = $customerManager->searchCustomers($search_term, $filters, 50, 0);
    }
    
    if ($_POST['action'] == 'update_blacklist' && $auth->hasPermission('customers.kyc')) {
        $result = $customerManager->updateBlacklistStatus(
            $_POST['customer_id'],
            (bool)$_POST['blacklisted'],
            $_POST['reason'] ?? '',
            $current_user['id']
        );
        
        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
        
        // إعادة جلب العملاء
        $customers = $customerManager->searchCustomers($search_term, $filters, 50, 0);
    }
}

// دالة مساعدة لترجمة حالات KYC
function getKYCStatusText($status) {
    switch ($status) {
        case 'pending': return 'قيد المراجعة';
        case 'approved': return 'معتمد';
        case 'rejected': return 'مرفوض';
        case 'under_review': return 'تحت المراجعة';
        default: return 'غير محدد';
    }
}

// دالة مساعدة لترجمة مستويات المخاطر
function getRiskLevelText($level) {
    switch ($level) {
        case 'low': return 'منخفض';
        case 'medium': return 'متوسط';
        case 'high': return 'عالي';
        default: return 'غير محدد';
    }
}

// دالة مساعدة لألوان حالات KYC
function getKYCStatusClass($status) {
    switch ($status) {
        case 'approved': return 'success';
        case 'rejected': return 'danger';
        case 'under_review': return 'warning';
        case 'pending': return 'secondary';
        default: return 'secondary';
    }
}

// دالة مساعدة لألوان مستويات المخاطر
function getRiskLevelClass($level) {
    switch ($level) {
        case 'low': return 'success';
        case 'medium': return 'warning';
        case 'high': return 'danger';
        default: return 'secondary';
    }
}

include '../includes/header.php';
?>
<!-- صفحة إدارة العملاء -->
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">إدارة العملاء</h2>
                        <p class="text-muted">إدارة بيانات العملاء ونظام KYC/AML</p>
                    </div>
                    <div>
                        <?php if ($auth->hasPermission('customers.create')): ?>
                        <a href="add_customer.php" class="btn btn-primary me-2">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة عميل جديد
                        </a>
                        <?php endif; ?>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($error_message): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <!-- البحث والفلاتر -->
        <div class="card">
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">البحث</label>
                            <input type="text" 
                                   class="form-control search-box" 
                                   name="search" 
                                   value="<?php echo htmlspecialchars($search_term); ?>"
                                   placeholder="البحث بالاسم، رقم الهوية، الهاتف، أو البريد الإلكتروني">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">حالة KYC</label>
                            <select class="form-control" name="kyc_status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $kyc_filter === 'pending' ? 'selected' : ''; ?>>قيد المراجعة</option>
                                <option value="approved" <?php echo $kyc_filter === 'approved' ? 'selected' : ''; ?>>معتمد</option>
                                <option value="rejected" <?php echo $kyc_filter === 'rejected' ? 'selected' : ''; ?>>مرفوض</option>
                                <option value="under_review" <?php echo $kyc_filter === 'under_review' ? 'selected' : ''; ?>>تحت المراجعة</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">مستوى المخاطر</label>
                            <select class="form-control" name="risk_level">
                                <option value="">جميع المستويات</option>
                                <option value="low" <?php echo $risk_filter === 'low' ? 'selected' : ''; ?>>منخفض</option>
                                <option value="medium" <?php echo $risk_filter === 'medium' ? 'selected' : ''; ?>>متوسط</option>
                                <option value="high" <?php echo $risk_filter === 'high' ? 'selected' : ''; ?>>عالي</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">القائمة السوداء</label>
                            <select class="form-control" name="blacklisted">
                                <option value="">الكل</option>
                                <option value="0" <?php echo $blacklist_filter === '0' ? 'selected' : ''; ?>>عادي</option>
                                <option value="1" <?php echo $blacklist_filter === '1' ? 'selected' : ''; ?>>في القائمة السوداء</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- قائمة العملاء -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    قائمة العملاء (<?php echo count($customers); ?>)
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($customers)): ?>
                    <div class="text-center p-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد عملاء</h5>
                        <p class="text-muted">لم يتم العثور على عملاء مطابقين لمعايير البحث</p>
                        <?php if ($auth->hasPermission('customers.create')): ?>
                        <a href="add_customer.php" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة أول عميل
                        </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>رقم الهوية</th>
                                    <th>الهاتف</th>
                                    <th>حالة KYC</th>
                                    <th>مستوى المخاطر</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($customers as $customer): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="customer-avatar me-3">
                                                <?php echo strtoupper(substr($customer['full_name'], 0, 1)); ?>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?php echo htmlspecialchars($customer['full_name']); ?></div>
                                                <small class="text-muted"><?php echo htmlspecialchars($customer['email'] ?? 'لا يوجد بريد إلكتروني'); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div><?php echo htmlspecialchars($customer['id_number']); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($customer['id_type']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($customer['phone'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo getKYCStatusClass($customer['kyc_status']); ?>">
                                            <?php echo getKYCStatusText($customer['kyc_status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo getRiskLevelClass($customer['risk_level']); ?>">
                                            <?php echo getRiskLevelText($customer['risk_level']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($customer['blacklisted']): ?>
                                            <span class="badge bg-dark">
                                                <i class="fas fa-ban me-1"></i>
                                                قائمة سوداء
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>
                                                نشط
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($customer['registration_date'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="customer_details.php?id=<?php echo $customer['id']; ?>" 
                                               class="btn btn-outline-primary action-btn" 
                                               title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            <?php if ($auth->hasPermission('customers.edit')): ?>
                                            <a href="edit_customer.php?id=<?php echo $customer['id']; ?>" 
                                               class="btn btn-outline-warning action-btn" 
                                               title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <?php if ($auth->hasPermission('customers.kyc')): ?>
                                            <button type="button" 
                                                    class="btn btn-outline-info action-btn" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#kycModal"
                                                    data-customer-id="<?php echo $customer['id']; ?>"
                                                    data-customer-name="<?php echo htmlspecialchars($customer['full_name']); ?>"
                                                    data-kyc-status="<?php echo $customer['kyc_status']; ?>"
                                                    title="إدارة KYC">
                                                <i class="fas fa-shield-alt"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal إدارة KYC -->
    <?php if ($auth->hasPermission('customers.kyc')): ?>
    <div class="modal fade" id="kycModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إدارة KYC/AML</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="kycTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="kyc-tab" data-bs-toggle="tab" data-bs-target="#kyc" type="button" role="tab">
                                حالة KYC
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="risk-tab" data-bs-toggle="tab" data-bs-target="#risk" type="button" role="tab">
                                مستوى المخاطر
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="blacklist-tab" data-bs-toggle="tab" data-bs-target="#blacklist" type="button" role="tab">
                                القائمة السوداء
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content mt-3" id="kycTabContent">
                        <!-- تحديث حالة KYC -->
                        <div class="tab-pane fade show active" id="kyc" role="tabpanel">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_kyc">
                                <input type="hidden" name="customer_id" id="kyc_customer_id">
                                
                                <div class="mb-3">
                                    <label class="form-label">حالة KYC الجديدة</label>
                                    <select class="form-control" name="kyc_status" required>
                                        <option value="pending">قيد المراجعة</option>
                                        <option value="under_review">تحت المراجعة</option>
                                        <option value="approved">معتمد</option>
                                        <option value="rejected">مرفوض</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea class="form-control" name="notes" rows="3" placeholder="أدخل ملاحظات حول قرار KYC"></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ حالة KYC
                                </button>
                            </form>
                        </div>
                        
                        <!-- تحديث مستوى المخاطر -->
                        <div class="tab-pane fade" id="risk" role="tabpanel">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_risk">
                                <input type="hidden" name="customer_id" id="risk_customer_id">
                                
                                <div class="mb-3">
                                    <label class="form-label">مستوى المخاطر الجديد</label>
                                    <select class="form-control" name="risk_level" required>
                                        <option value="low">منخفض</option>
                                        <option value="medium">متوسط</option>
                                        <option value="high">عالي</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">سبب التغيير</label>
                                    <textarea class="form-control" name="reason" rows="3" placeholder="أدخل سبب تغيير مستوى المخاطر" required></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    تحديث مستوى المخاطر
                                </button>
                            </form>
                        </div>
                        
                        <!-- إدارة القائمة السوداء -->
                        <div class="tab-pane fade" id="blacklist" role="tabpanel">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_blacklist">
                                <input type="hidden" name="customer_id" id="blacklist_customer_id">
                                
                                <div class="mb-3">
                                    <label class="form-label">حالة القائمة السوداء</label>
                                    <select class="form-control" name="blacklisted" required>
                                        <option value="0">إزالة من القائمة السوداء</option>
                                        <option value="1">إضافة للقائمة السوداء</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">السبب</label>
                                    <textarea class="form-control" name="reason" rows="3" placeholder="أدخل سبب الإضافة/الإزالة من القائمة السوداء" required></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-ban me-2"></i>
                                    تحديث حالة القائمة السوداء
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

<?php
// إضافة JavaScript خاص بصفحة العملاء
$inline_js = '
    // تحديث بيانات Modal عند فتحه
    const kycModal = document.getElementById("kycModal");
    if (kycModal) {
        kycModal.addEventListener("show.bs.modal", function (event) {
            const button = event.relatedTarget;
            const customerId = button.getAttribute("data-customer-id");
            const customerName = button.getAttribute("data-customer-name");
            const kycStatus = button.getAttribute("data-kyc-status");

            // تحديث العنوان
            this.querySelector(".modal-title").textContent = "إدارة KYC/AML - " + customerName;

            // تحديث معرفات العميل في جميع النماذج
            document.getElementById("kyc_customer_id").value = customerId;
            document.getElementById("risk_customer_id").value = customerId;
            document.getElementById("blacklist_customer_id").value = customerId;

            // تحديد الحالة الحالية
            const kycSelect = this.querySelector("select[name=\"kyc_status\"]");
            kycSelect.value = kycStatus;
        });
    }
';

include '../includes/footer.php';
?>
