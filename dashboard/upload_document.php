<?php
require_once '../includes/auth.php';
require_once '../includes/customer_manager.php';
require_once '../includes/document_manager.php';

$auth = new Auth();
$customerManager = new CustomerManager();
$documentManager = new DocumentManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('customers.edit')) {
    header('Location: customers.php?error=ليس لديك صلاحية لرفع المستندات');
    exit();
}

$current_user = $auth->getCurrentUser();
$customer_id = $_GET['customer_id'] ?? null;

if (!$customer_id) {
    header('Location: customers.php?error=معرف العميل مطلوب');
    exit();
}

// جلب بيانات العميل
$customer = $customerManager->getCustomerById($customer_id);

if (!$customer) {
    header('Location: customers.php?error=العميل غير موجود');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة رفع المستند
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['document'])) {
    $document_type = $_POST['document_type'] ?? '';
    $notes = trim($_POST['notes'] ?? '');
    
    if (empty($document_type)) {
        $error_message = 'يرجى اختيار نوع المستند';
    } elseif (!isset($_FILES['document']) || $_FILES['document']['error'] !== UPLOAD_ERR_OK) {
        $error_message = 'يرجى اختيار ملف للرفع';
    } else {
        $result = $documentManager->uploadDocument(
            $_FILES['document'],
            'customer',
            $customer_id,
            $document_type,
            $current_user['id'],
            $notes
        );
        
        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    }
}

// جلب المستندات الموجودة
$existing_documents = $documentManager->getDocuments('customer', $customer_id);
$document_types = $documentManager->getDocumentTypes();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع مستندات العميل - <?php echo htmlspecialchars($customer['full_name']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 15px;
            padding: 3rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            background: #e9ecef;
            border-color: #5a67d8;
        }
        
        .upload-area.dragover {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        
        .file-info {
            background: #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .document-item {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #e9ecef;
        }
        
        .document-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }
        
        .doc-pdf { background: #dc3545; }
        .doc-image { background: #28a745; }
        .doc-word { background: #007bff; }
        .doc-other { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="mb-0">رفع مستندات العميل</h2>
                        <p class="text-muted">العميل: <?php echo htmlspecialchars($customer['full_name']); ?></p>
                    </div>
                    <div>
                        <a href="customer_details.php?id=<?php echo $customer['id']; ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة لتفاصيل العميل
                        </a>
                    </div>
                </div>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success_message): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="row">
            <!-- نموذج رفع المستند -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            رفع مستند جديد
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" id="uploadForm">
                            <div class="mb-3">
                                <label for="document_type" class="form-label">نوع المستند</label>
                                <select class="form-control" id="document_type" name="document_type" required>
                                    <option value="">اختر نوع المستند</option>
                                    <?php foreach ($document_types as $type => $label): ?>
                                        <option value="<?php echo $type; ?>"><?php echo htmlspecialchars($label); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الملف</label>
                                <div class="upload-area" id="uploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">اسحب الملف هنا أو اضغط للاختيار</h5>
                                    <p class="text-muted mb-0">
                                        الأنواع المدعومة: JPG, PNG, PDF, DOC, DOCX<br>
                                        الحد الأقصى: 5MB
                                    </p>
                                    <input type="file" 
                                           class="d-none" 
                                           id="document" 
                                           name="document" 
                                           accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx"
                                           required>
                                </div>
                                <div id="fileInfo" class="file-info d-none">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file me-2"></i>
                                        <div>
                                            <div id="fileName" class="fw-bold"></div>
                                            <div id="fileSize" class="text-muted small"></div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger ms-auto" id="removeFile">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3"
                                          placeholder="أدخل أي ملاحظات حول المستند (اختياري)"></textarea>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i>
                                    رفع المستند
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- المستندات الموجودة -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-folder me-2"></i>
                            المستندات الموجودة (<?php echo count($existing_documents); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($existing_documents)): ?>
                            <div class="text-center p-4">
                                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد مستندات</h6>
                                <p class="text-muted">لم يتم رفع أي مستندات لهذا العميل بعد</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($existing_documents as $doc): ?>
                                <div class="document-item">
                                    <div class="d-flex align-items-center">
                                        <div class="document-icon me-3 <?php echo getDocumentIconClass($doc['file_name']); ?>">
                                            <i class="fas <?php echo getDocumentIcon($doc['file_name']); ?>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($document_types[$doc['document_type']] ?? $doc['document_type']); ?></h6>
                                            <div class="text-muted small">
                                                <?php echo htmlspecialchars($doc['file_name']); ?> 
                                                (<?php echo formatFileSize($doc['file_size']); ?>)
                                            </div>
                                            <div class="text-muted small">
                                                رفع بواسطة: <?php echo htmlspecialchars($doc['uploaded_by_name']); ?> |
                                                <?php echo date('Y-m-d H:i', strtotime($doc['upload_date'])); ?>
                                            </div>
                                        </div>
                                        <div>
                                            <?php if ($doc['is_verified']): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>
                                                    تم التحقق
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>
                                                    قيد المراجعة
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <?php if ($doc['notes']): ?>
                                        <div class="mt-2 p-2 bg-light rounded">
                                            <small class="text-muted"><?php echo nl2br(htmlspecialchars($doc['notes'])); ?></small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mt-2">
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="viewDocument(<?php echo $doc['id']; ?>)">
                                                <i class="fas fa-eye"></i> عرض
                                            </button>
                                            
                                            <?php if ($auth->hasPermission('customers.kyc') && !$doc['is_verified']): ?>
                                                <button class="btn btn-outline-success" onclick="verifyDocument(<?php echo $doc['id']; ?>)">
                                                    <i class="fas fa-check"></i> تحقق
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="rejectDocument(<?php echo $doc['id']; ?>)">
                                                    <i class="fas fa-times"></i> رفض
                                                </button>
                                            <?php endif; ?>
                                            
                                            <?php if ($auth->hasPermission('customers.delete')): ?>
                                                <button class="btn btn-outline-danger" onclick="deleteDocument(<?php echo $doc['id']; ?>)">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إعداد منطقة الرفع
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('document');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const removeFileBtn = document.getElementById('removeFile');

        // النقر على منطقة الرفع
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // السحب والإفلات
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFileInfo(files[0]);
            }
        });

        // تغيير الملف
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                showFileInfo(e.target.files[0]);
            }
        });

        // إزالة الملف
        removeFileBtn.addEventListener('click', () => {
            fileInput.value = '';
            hideFileInfo();
        });

        // عرض معلومات الملف
        function showFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            uploadArea.classList.add('d-none');
            fileInfo.classList.remove('d-none');
        }

        // إخفاء معلومات الملف
        function hideFileInfo() {
            uploadArea.classList.remove('d-none');
            fileInfo.classList.add('d-none');
        }

        // تنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // دوال إدارة المستندات
        function viewDocument(documentId) {
            // فتح المستند في نافذة جديدة
            window.open('view_document.php?id=' + documentId, '_blank');
        }

        function verifyDocument(documentId) {
            if (confirm('هل أنت متأكد من التحقق من هذا المستند؟')) {
                // إرسال طلب التحقق
                window.location.href = 'verify_document.php?id=' + documentId + '&customer_id=<?php echo $customer_id; ?>';
            }
        }

        function rejectDocument(documentId) {
            const reason = prompt('أدخل سبب رفض المستند:');
            if (reason) {
                window.location.href = 'reject_document.php?id=' + documentId + '&customer_id=<?php echo $customer_id; ?>&reason=' + encodeURIComponent(reason);
            }
        }

        function deleteDocument(documentId) {
            if (confirm('هل أنت متأكد من حذف هذا المستند؟ لا يمكن التراجع عن هذا الإجراء.')) {
                window.location.href = 'delete_document.php?id=' + documentId + '&customer_id=<?php echo $customer_id; ?>';
            }
        }

        // إخفاء رسائل التنبيه تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>

<?php
// دوال مساعدة
function getDocumentIcon($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    switch ($extension) {
        case 'pdf': return 'fa-file-pdf';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif': return 'fa-file-image';
        case 'doc':
        case 'docx': return 'fa-file-word';
        default: return 'fa-file';
    }
}

function getDocumentIconClass($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    switch ($extension) {
        case 'pdf': return 'doc-pdf';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif': return 'doc-image';
        case 'doc':
        case 'docx': return 'doc-word';
        default: return 'doc-other';
    }
}

function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>
