<?php
require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/cash_manager.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

// التحقق من الصلاحيات
if (!$auth->hasPermission('cash.view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى إدارة الصناديق والبنوك';
    header('Location: index.php');
    exit();
}

$current_user = $auth->getCurrentUser();
$cashManager = new CashManager();

// معالجة الطلبات
$action = $_GET['action'] ?? 'list';
$type = $_GET['type'] ?? 'cash'; // cash أو bank
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!hash_equals($_SESSION['csrf_token'] ?? '', $csrf_token)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        switch ($_POST['action']) {
            case 'add_cash_box':
                if ($auth->hasPermission('cash.edit')) {
                    $result = $cashManager->createCashBox($_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: cash.php?type=cash');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لإضافة صناديق نقدية';
                }
                break;
                
            case 'edit_cash_box':
                if ($auth->hasPermission('cash.edit')) {
                    $result = $cashManager->updateCashBox($_POST['cash_box_id'], $_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: cash.php?type=cash');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لتعديل الصناديق النقدية';
                }
                break;
                
            case 'add_bank_account':
                if ($auth->hasPermission('cash.edit')) {
                    $result = $cashManager->createBankAccount($_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: cash.php?type=bank');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لإضافة حسابات بنكية';
                }
                break;
                
            case 'edit_bank_account':
                if ($auth->hasPermission('cash.edit')) {
                    $result = $cashManager->updateBankAccount($_POST['bank_account_id'], $_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: cash.php?type=bank');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لتعديل الحسابات البنكية';
                }
                break;
                
            case 'cash_movement':
                if ($auth->hasPermission('cash.edit')) {
                    $result = $cashManager->recordCashMovement(
                        $_POST['cash_box_id'],
                        $_POST['movement_type'],
                        $_POST['amount'],
                        $_POST['description'],
                        $_POST['reference_number']
                    );
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: cash.php?action=movements&type=cash&id=' . $_POST['cash_box_id']);
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لتسجيل حركات نقدية';
                }
                break;
                
            case 'bank_movement':
                if ($auth->hasPermission('cash.edit')) {
                    $result = $cashManager->recordBankMovement(
                        $_POST['bank_account_id'],
                        $_POST['movement_type'],
                        $_POST['amount'],
                        $_POST['description'],
                        $_POST['reference_number']
                    );
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: cash.php?action=movements&type=bank&id=' . $_POST['bank_account_id']);
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لتسجيل حركات بنكية';
                }
                break;
        }
    }
}

// إنشاء رمز CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// جلب البيانات حسب الإجراء
$cash_boxes = [];
$bank_accounts = [];
$currencies = [];
$branches = [];
$users = [];
$item_data = null;
$movements = [];

try {
    $currencies = $cashManager->getActiveCurrencies();
    $branches = $cashManager->getActiveBranches();
    $users = $cashManager->getActiveUsers($current_user['branch_id']);
    
    if ($action == 'list' || $action == 'add' || $action == 'edit') {
        if ($type == 'cash') {
            $cash_boxes = $cashManager->getAllCashBoxes($current_user['branch_id']);
        } else {
            $bank_accounts = $cashManager->getAllBankAccounts($current_user['branch_id']);
        }
    }
    
    if ($action == 'edit' && $id) {
        if ($type == 'cash') {
            $item_data = $cashManager->getCashBoxById($id);
        } else {
            $item_data = $cashManager->getBankAccountById($id);
        }
        
        if (!$item_data) {
            $error = $type == 'cash' ? 'الصندوق النقدي غير موجود' : 'الحساب البنكي غير موجود';
            $action = 'list';
        }
    }
    
    if ($action == 'movements' && $id) {
        if ($type == 'cash') {
            $item_data = $cashManager->getCashBoxById($id);
            $movements = $cashManager->getCashMovements($id);
        } else {
            $item_data = $cashManager->getBankAccountById($id);
            $movements = $cashManager->getBankMovements($id);
        }
        
        if (!$item_data) {
            $error = $type == 'cash' ? 'الصندوق النقدي غير موجود' : 'الحساب البنكي غير موجود';
            $action = 'list';
        }
    }
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
}

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'إدارة الصناديق والبنوك - ' . SYSTEM_NAME;
$page_header = 'إدارة الصناديق والبنوك';
$page_subtitle = 'إدارة الصناديق النقدية والحسابات البنكية';
$page_icon = 'fas fa-university';
$show_breadcrumb = true;

// إضافة أزرار الإجراءات
$page_actions = '';
if ($auth->hasPermission('cash.edit') && $action == 'list') {
    if ($type == 'cash') {
        $page_actions = '<a href="cash.php?action=add&type=cash" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة صندوق نقدي
        </a>';
    } else {
        $page_actions = '<a href="cash.php?action=add&type=bank" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة حساب بنكي
        </a>';
    }
}
?>
<?php include '../includes/header.php'; ?>

<div class="container-fluid p-4">
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- تبويبات التنقل -->
    <ul class="nav nav-tabs mb-4" id="cashTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link <?php echo $type == 'cash' ? 'active' : ''; ?>" 
               href="cash.php?type=cash" role="tab">
                <i class="fas fa-cash-register me-2"></i>
                الصناديق النقدية
            </a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link <?php echo $type == 'bank' ? 'active' : ''; ?>" 
               href="cash.php?type=bank" role="tab">
                <i class="fas fa-university me-2"></i>
                الحسابات البنكية
            </a>
        </li>
    </ul>

    <?php if ($action == 'list'): ?>
        <?php if ($type == 'cash'): ?>
            <!-- قائمة الصناديق النقدية -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cash-register me-2"></i>
                        الصناديق النقدية
                    </h5>
                </div>
                <div class="card-body">
                    <!-- شريط البحث -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchCashBoxes" placeholder="البحث في الصناديق...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterCurrency">
                                <option value="">جميع العملات</option>
                                <?php foreach ($currencies as $currency): ?>
                                    <option value="<?php echo $currency['id']; ?>">
                                        <?php echo htmlspecialchars($currency['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterStatus">
                                <option value="">جميع الحالات</option>
                                <option value="1">نشط</option>
                                <option value="0">غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <!-- جدول الصناديق النقدية -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="cashBoxesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>الرقم</th>
                                    <th>اسم الصندوق</th>
                                    <th>العملة</th>
                                    <th>الرصيد الحالي</th>
                                    <th>المسؤول</th>
                                    <th>الفرع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($cash_boxes as $cash_box): ?>
                                    <tr data-currency="<?php echo $cash_box['currency_id']; ?>" data-status="<?php echo $cash_box['is_active']; ?>">
                                        <td><?php echo $cash_box['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($cash_box['name']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($cash_box['currency_code']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-<?php echo $cash_box['current_balance'] >= 0 ? 'success' : 'danger'; ?>">
                                                <?php echo number_format($cash_box['current_balance'], 2); ?>
                                                <?php echo htmlspecialchars($cash_box['currency_symbol']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($cash_box['responsible_user_name'] ?? 'غير محدد'); ?></td>
                                        <td><?php echo htmlspecialchars($cash_box['branch_name']); ?></td>
                                        <td>
                                            <?php if ($cash_box['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="cash.php?action=movements&type=cash&id=<?php echo $cash_box['id']; ?>" 
                                                   class="btn btn-outline-info" title="الحركات">
                                                    <i class="fas fa-list"></i>
                                                </a>
                                                <?php if ($auth->hasPermission('cash.edit')): ?>
                                                    <a href="cash.php?action=edit&type=cash&id=<?php echo $cash_box['id']; ?>" 
                                                       class="btn btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php else: ?>
            <!-- قائمة الحسابات البنكية -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-university me-2"></i>
                        الحسابات البنكية
                    </h5>
                </div>
                <div class="card-body">
                    <!-- شريط البحث -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchBankAccounts" placeholder="البحث في الحسابات...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterCurrency">
                                <option value="">جميع العملات</option>
                                <?php foreach ($currencies as $currency): ?>
                                    <option value="<?php echo $currency['id']; ?>">
                                        <?php echo htmlspecialchars($currency['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterStatus">
                                <option value="">جميع الحالات</option>
                                <option value="1">نشط</option>
                                <option value="0">غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <!-- جدول الحسابات البنكية -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="bankAccountsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>الرقم</th>
                                    <th>اسم الحساب</th>
                                    <th>البنك</th>
                                    <th>رقم الحساب</th>
                                    <th>العملة</th>
                                    <th>الرصيد الحالي</th>
                                    <th>الفرع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($bank_accounts as $bank_account): ?>
                                    <tr data-currency="<?php echo $bank_account['currency_id']; ?>" data-status="<?php echo $bank_account['is_active']; ?>">
                                        <td><?php echo $bank_account['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($bank_account['account_name']); ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($bank_account['bank_name']); ?></td>
                                        <td>
                                            <code><?php echo htmlspecialchars($bank_account['account_number']); ?></code>
                                            <?php if ($bank_account['iban']): ?>
                                                <br><small class="text-muted">IBAN: <?php echo htmlspecialchars($bank_account['iban']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($bank_account['currency_code']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-<?php echo $bank_account['current_balance'] >= 0 ? 'success' : 'danger'; ?>">
                                                <?php echo number_format($bank_account['current_balance'], 2); ?>
                                                <?php echo htmlspecialchars($bank_account['currency_symbol']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($bank_account['branch_name']); ?></td>
                                        <td>
                                            <?php if ($bank_account['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="cash.php?action=movements&type=bank&id=<?php echo $bank_account['id']; ?>"
                                                   class="btn btn-outline-info" title="الحركات">
                                                    <i class="fas fa-list"></i>
                                                </a>
                                                <?php if ($auth->hasPermission('cash.edit')): ?>
                                                    <a href="cash.php?action=edit&type=bank&id=<?php echo $bank_account['id']; ?>"
                                                       class="btn btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    <?php elseif ($action == 'add'): ?>
        <?php if ($type == 'cash'): ?>
            <!-- نموذج إضافة صندوق نقدي -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إضافة صندوق نقدي جديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="cash.php" id="addCashBoxForm">
                        <input type="hidden" name="action" value="add_cash_box">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم الصندوق <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency_id" class="form-label">العملة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="currency_id" name="currency_id" required>
                                        <option value="">اختر العملة</option>
                                        <?php foreach ($currencies as $currency): ?>
                                            <option value="<?php echo $currency['id']; ?>">
                                                <?php echo htmlspecialchars($currency['name'] . ' (' . $currency['code'] . ')'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="branch_id" class="form-label">الفرع <span class="text-danger">*</span></label>
                                    <select class="form-select" id="branch_id" name="branch_id" required>
                                        <option value="">اختر الفرع</option>
                                        <?php foreach ($branches as $branch): ?>
                                            <option value="<?php echo $branch['id']; ?>"
                                                    <?php echo $branch['id'] == $current_user['branch_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($branch['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="responsible_user_id" class="form-label">المسؤول</label>
                                    <select class="form-select" id="responsible_user_id" name="responsible_user_id">
                                        <option value="">اختر المسؤول</option>
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo $user['id']; ?>">
                                                <?php echo htmlspecialchars($user['full_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="initial_balance" class="form-label">الرصيد الافتتاحي</label>
                                    <input type="number" class="form-control" id="initial_balance" name="initial_balance"
                                           step="0.01" min="0" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                        <label class="form-check-label" for="is_active">
                                            تفعيل الصندوق
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="cash.php?type=cash" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الصندوق
                            </button>
                        </div>
                    </form>
                </div>
            </div>

        <?php else: ?>
            <!-- نموذج إضافة حساب بنكي -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إضافة حساب بنكي جديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="cash.php" id="addBankAccountForm">
                        <input type="hidden" name="action" value="add_bank_account">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_name" class="form-label">اسم الحساب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="account_name" name="account_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bank_name" class="form-label">اسم البنك <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="bank_name" name="bank_name" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_number" class="form-label">رقم الحساب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="account_number" name="account_number" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="iban" class="form-label">رقم IBAN</label>
                                    <input type="text" class="form-control" id="iban" name="iban">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="swift_code" class="form-label">رمز SWIFT</label>
                                    <input type="text" class="form-control" id="swift_code" name="swift_code">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency_id" class="form-label">العملة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="currency_id" name="currency_id" required>
                                        <option value="">اختر العملة</option>
                                        <?php foreach ($currencies as $currency): ?>
                                            <option value="<?php echo $currency['id']; ?>">
                                                <?php echo htmlspecialchars($currency['name'] . ' (' . $currency['code'] . ')'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="branch_id" class="form-label">الفرع <span class="text-danger">*</span></label>
                                    <select class="form-select" id="branch_id" name="branch_id" required>
                                        <option value="">اختر الفرع</option>
                                        <?php foreach ($branches as $branch): ?>
                                            <option value="<?php echo $branch['id']; ?>"
                                                    <?php echo $branch['id'] == $current_user['branch_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($branch['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="initial_balance" class="form-label">الرصيد الافتتاحي</label>
                                    <input type="number" class="form-control" id="initial_balance" name="initial_balance"
                                           step="0.01" min="0" value="0">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                        <label class="form-check-label" for="is_active">
                                            تفعيل الحساب
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="cash.php?type=bank" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الحساب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>

    <?php elseif ($action == 'edit' && $item_data): ?>
        <!-- نماذج التعديل -->
        <?php if ($type == 'cash'): ?>
            <!-- نموذج تعديل صندوق نقدي -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الصندوق النقدي: <?php echo htmlspecialchars($item_data['name']); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="cash.php" id="editCashBoxForm">
                        <input type="hidden" name="action" value="edit_cash_box">
                        <input type="hidden" name="cash_box_id" value="<?php echo $item_data['id']; ?>">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم الصندوق <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<?php echo htmlspecialchars($item_data['name']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency_id" class="form-label">العملة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="currency_id" name="currency_id" required>
                                        <option value="">اختر العملة</option>
                                        <?php foreach ($currencies as $currency): ?>
                                            <option value="<?php echo $currency['id']; ?>"
                                                    <?php echo $currency['id'] == $item_data['currency_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($currency['name'] . ' (' . $currency['code'] . ')'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="branch_id" class="form-label">الفرع <span class="text-danger">*</span></label>
                                    <select class="form-select" id="branch_id" name="branch_id" required>
                                        <option value="">اختر الفرع</option>
                                        <?php foreach ($branches as $branch): ?>
                                            <option value="<?php echo $branch['id']; ?>"
                                                    <?php echo $branch['id'] == $item_data['branch_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($branch['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="responsible_user_id" class="form-label">المسؤول</label>
                                    <select class="form-select" id="responsible_user_id" name="responsible_user_id">
                                        <option value="">اختر المسؤول</option>
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo $user['id']; ?>"
                                                    <?php echo $user['id'] == $item_data['responsible_user_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($user['full_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرصيد الحالي</label>
                                    <div class="form-control-plaintext fw-bold text-<?php echo $item_data['current_balance'] >= 0 ? 'success' : 'danger'; ?>">
                                        <?php echo number_format($item_data['current_balance'], 2); ?>
                                        <?php echo htmlspecialchars($item_data['currency_symbol']); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                               <?php echo $item_data['is_active'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">
                                            تفعيل الصندوق
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="cash.php?type=cash" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>

        <?php else: ?>
            <!-- نموذج تعديل حساب بنكي -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الحساب البنكي: <?php echo htmlspecialchars($item_data['account_name']); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="cash.php" id="editBankAccountForm">
                        <input type="hidden" name="action" value="edit_bank_account">
                        <input type="hidden" name="bank_account_id" value="<?php echo $item_data['id']; ?>">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_name" class="form-label">اسم الحساب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="account_name" name="account_name"
                                           value="<?php echo htmlspecialchars($item_data['account_name']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bank_name" class="form-label">اسم البنك <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="bank_name" name="bank_name"
                                           value="<?php echo htmlspecialchars($item_data['bank_name']); ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_number" class="form-label">رقم الحساب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="account_number" name="account_number"
                                           value="<?php echo htmlspecialchars($item_data['account_number']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="iban" class="form-label">رقم IBAN</label>
                                    <input type="text" class="form-control" id="iban" name="iban"
                                           value="<?php echo htmlspecialchars($item_data['iban'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="swift_code" class="form-label">رمز SWIFT</label>
                                    <input type="text" class="form-control" id="swift_code" name="swift_code"
                                           value="<?php echo htmlspecialchars($item_data['swift_code'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency_id" class="form-label">العملة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="currency_id" name="currency_id" required>
                                        <option value="">اختر العملة</option>
                                        <?php foreach ($currencies as $currency): ?>
                                            <option value="<?php echo $currency['id']; ?>"
                                                    <?php echo $currency['id'] == $item_data['currency_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($currency['name'] . ' (' . $currency['code'] . ')'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="branch_id" class="form-label">الفرع <span class="text-danger">*</span></label>
                                    <select class="form-select" id="branch_id" name="branch_id" required>
                                        <option value="">اختر الفرع</option>
                                        <?php foreach ($branches as $branch): ?>
                                            <option value="<?php echo $branch['id']; ?>"
                                                    <?php echo $branch['id'] == $item_data['branch_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($branch['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرصيد الحالي</label>
                                    <div class="form-control-plaintext fw-bold text-<?php echo $item_data['current_balance'] >= 0 ? 'success' : 'danger'; ?>">
                                        <?php echo number_format($item_data['current_balance'], 2); ?>
                                        <?php echo htmlspecialchars($item_data['currency_symbol']); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                               <?php echo $item_data['is_active'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">
                                            تفعيل الحساب
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="cash.php?type=bank" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<script>
// البحث والفلترة
document.addEventListener('DOMContentLoaded', function() {
    // البحث في الصناديق النقدية
    const searchCashBoxes = document.getElementById('searchCashBoxes');
    const cashBoxesTable = document.getElementById('cashBoxesTable');

    if (searchCashBoxes && cashBoxesTable) {
        const rows = cashBoxesTable.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        const currencyFilter = document.getElementById('filterCurrency');
        const statusFilter = document.getElementById('filterStatus');

        function filterCashBoxes() {
            const searchTerm = searchCashBoxes.value.toLowerCase();
            const selectedCurrency = currencyFilter ? currencyFilter.value : '';
            const selectedStatus = statusFilter ? statusFilter.value : '';

            for (let row of rows) {
                const text = row.textContent.toLowerCase();
                const currencyMatch = !selectedCurrency || row.dataset.currency === selectedCurrency;
                const statusMatch = !selectedStatus || row.dataset.status === selectedStatus;
                const searchMatch = !searchTerm || text.includes(searchTerm);

                if (currencyMatch && statusMatch && searchMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        searchCashBoxes.addEventListener('input', filterCashBoxes);
        if (currencyFilter) currencyFilter.addEventListener('change', filterCashBoxes);
        if (statusFilter) statusFilter.addEventListener('change', filterCashBoxes);
    }

    // البحث في الحسابات البنكية
    const searchBankAccounts = document.getElementById('searchBankAccounts');
    const bankAccountsTable = document.getElementById('bankAccountsTable');

    if (searchBankAccounts && bankAccountsTable) {
        const rows = bankAccountsTable.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        const currencyFilter = document.getElementById('filterCurrency');
        const statusFilter = document.getElementById('filterStatus');

        function filterBankAccounts() {
            const searchTerm = searchBankAccounts.value.toLowerCase();
            const selectedCurrency = currencyFilter ? currencyFilter.value : '';
            const selectedStatus = statusFilter ? statusFilter.value : '';

            for (let row of rows) {
                const text = row.textContent.toLowerCase();
                const currencyMatch = !selectedCurrency || row.dataset.currency === selectedCurrency;
                const statusMatch = !selectedStatus || row.dataset.status === selectedStatus;
                const searchMatch = !searchTerm || text.includes(searchTerm);

                if (currencyMatch && statusMatch && searchMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        searchBankAccounts.addEventListener('input', filterBankAccounts);
        if (currencyFilter) currencyFilter.addEventListener('change', filterBankAccounts);
        if (statusFilter) statusFilter.addEventListener('change', filterBankAccounts);
    }
});

// التحقق من صحة النماذج
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form[id$="Form"]');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('input[required], select[required]');

            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    e.preventDefault();
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    field.focus();
                    return;
                }
            }

            // التحقق من الأرقام
            const numberFields = form.querySelectorAll('input[type="number"]');
            for (let field of numberFields) {
                if (field.value && parseFloat(field.value) < 0) {
                    e.preventDefault();
                    alert('القيم الرقمية يجب أن تكون موجبة');
                    field.focus();
                    return;
                }
            }
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
