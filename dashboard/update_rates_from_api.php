<?php
require_once '../includes/auth.php';
require_once '../includes/exchange_rate_api.php';

// التحقق من صحة الجلسة والصلاحيات
$auth = new Auth();

if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('exchange.rates')) {
    header('Location: exchange_rates.php?error=' . urlencode('ليس لديك صلاحية للوصول إلى هذه الصفحة'));
    exit();
}

$current_user = $auth->getCurrentUser();

// تهيئة API الأسعار
$exchangeRateAPI = new ExchangeRateAPI();

// تحديث الأسعار من API
$result = $exchangeRateAPI->updateRatesFromAPI($current_user['id']);

if ($result['success']) {
    header('Location: exchange_rates.php?success=' . urlencode($result['message']));
} else {
    header('Location: exchange_rates.php?error=' . urlencode($result['message']));
}
exit();
?> 