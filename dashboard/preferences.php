<?php
require_once '../config.php';
require_once '../includes/auth.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

$current_user = $auth->getCurrentUser();
$message = '';
$error = '';

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!hash_equals($_SESSION['csrf_token'] ?? '', $csrf_token)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        try {
            $database = new Database();
            $db = $database->getConnection();
            
            // حفظ التفضيلات في جدول user_preferences أو في session
            $preferences = [
                'theme' => $_POST['theme'] ?? 'light',
                'language' => $_POST['language'] ?? 'ar',
                'notifications' => isset($_POST['notifications']) ? 1 : 0,
                'email_notifications' => isset($_POST['email_notifications']) ? 1 : 0,
                'dashboard_layout' => $_POST['dashboard_layout'] ?? 'default',
                'items_per_page' => (int)($_POST['items_per_page'] ?? 25),
                'date_format' => $_POST['date_format'] ?? 'Y-m-d',
                'time_format' => $_POST['time_format'] ?? '24'
            ];
            
            // حفظ التفضيلات في الجلسة (يمكن تطويرها لحفظها في قاعدة البيانات)
            $_SESSION['user_preferences'] = $preferences;
            
            $message = 'تم حفظ التفضيلات بنجاح';
            
        } catch (Exception $e) {
            $error = 'خطأ في حفظ التفضيلات: ' . $e->getMessage();
        }
    }
}

// إنشاء رمز CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// جلب التفضيلات الحالية
$preferences = $_SESSION['user_preferences'] ?? [
    'theme' => 'light',
    'language' => 'ar',
    'notifications' => 1,
    'email_notifications' => 1,
    'dashboard_layout' => 'default',
    'items_per_page' => 25,
    'date_format' => 'Y-m-d',
    'time_format' => '24'
];

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'التفضيلات - ' . SYSTEM_NAME;
$page_header = 'التفضيلات';
$page_subtitle = 'إعدادات المستخدم والواجهة';
$page_icon = 'fas fa-sliders-h';
$show_breadcrumb = true;
?>
<?php include '../includes/header.php'; ?>

<div class="container-fluid p-4">
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات التفضيلات
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="preferences.php" id="preferencesForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        
                        <!-- إعدادات المظهر -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-palette me-2"></i>
                                    إعدادات المظهر
                                </h6>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="theme" class="form-label">المظهر</label>
                                    <select class="form-select" id="theme" name="theme">
                                        <option value="light" <?php echo $preferences['theme'] == 'light' ? 'selected' : ''; ?>>فاتح</option>
                                        <option value="dark" <?php echo $preferences['theme'] == 'dark' ? 'selected' : ''; ?>>داكن</option>
                                        <option value="auto" <?php echo $preferences['theme'] == 'auto' ? 'selected' : ''; ?>>تلقائي</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="language" class="form-label">اللغة</label>
                                    <select class="form-select" id="language" name="language">
                                        <option value="ar" <?php echo $preferences['language'] == 'ar' ? 'selected' : ''; ?>>العربية</option>
                                        <option value="en" <?php echo $preferences['language'] == 'en' ? 'selected' : ''; ?>>English</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dashboard_layout" class="form-label">تخطيط لوحة التحكم</label>
                                    <select class="form-select" id="dashboard_layout" name="dashboard_layout">
                                        <option value="default" <?php echo $preferences['dashboard_layout'] == 'default' ? 'selected' : ''; ?>>افتراضي</option>
                                        <option value="compact" <?php echo $preferences['dashboard_layout'] == 'compact' ? 'selected' : ''; ?>>مضغوط</option>
                                        <option value="expanded" <?php echo $preferences['dashboard_layout'] == 'expanded' ? 'selected' : ''; ?>>موسع</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="items_per_page" class="form-label">عدد العناصر في الصفحة</label>
                                    <select class="form-select" id="items_per_page" name="items_per_page">
                                        <option value="10" <?php echo $preferences['items_per_page'] == 10 ? 'selected' : ''; ?>>10</option>
                                        <option value="25" <?php echo $preferences['items_per_page'] == 25 ? 'selected' : ''; ?>>25</option>
                                        <option value="50" <?php echo $preferences['items_per_page'] == 50 ? 'selected' : ''; ?>>50</option>
                                        <option value="100" <?php echo $preferences['items_per_page'] == 100 ? 'selected' : ''; ?>>100</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- إعدادات التاريخ والوقت -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-clock me-2"></i>
                                    إعدادات التاريخ والوقت
                                </h6>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_format" class="form-label">تنسيق التاريخ</label>
                                    <select class="form-select" id="date_format" name="date_format">
                                        <option value="Y-m-d" <?php echo $preferences['date_format'] == 'Y-m-d' ? 'selected' : ''; ?>>2024-01-15</option>
                                        <option value="d/m/Y" <?php echo $preferences['date_format'] == 'd/m/Y' ? 'selected' : ''; ?>>15/01/2024</option>
                                        <option value="d-m-Y" <?php echo $preferences['date_format'] == 'd-m-Y' ? 'selected' : ''; ?>>15-01-2024</option>
                                        <option value="M d, Y" <?php echo $preferences['date_format'] == 'M d, Y' ? 'selected' : ''; ?>>Jan 15, 2024</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="time_format" class="form-label">تنسيق الوقت</label>
                                    <select class="form-select" id="time_format" name="time_format">
                                        <option value="24" <?php echo $preferences['time_format'] == '24' ? 'selected' : ''; ?>>24 ساعة (14:30)</option>
                                        <option value="12" <?php echo $preferences['time_format'] == '12' ? 'selected' : ''; ?>>12 ساعة (2:30 PM)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- إعدادات الإشعارات -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                    <i class="fas fa-bell me-2"></i>
                                    إعدادات الإشعارات
                                </h6>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="notifications" name="notifications" 
                                           <?php echo $preferences['notifications'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="notifications">
                                        تفعيل الإشعارات
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" 
                                           <?php echo $preferences['email_notifications'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="email_notifications">
                                        إشعارات البريد الإلكتروني
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- أزرار الحفظ -->
                        <div class="d-flex justify-content-between">
                            <a href="profile.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للملف الشخصي
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-warning me-2" onclick="resetPreferences()">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التفضيلات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- معاينة التفضيلات -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>
                        معاينة التفضيلات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>المظهر:</strong> <span id="preview_theme"><?php echo $preferences['theme']; ?></span></p>
                            <p><strong>اللغة:</strong> <span id="preview_language"><?php echo $preferences['language']; ?></span></p>
                            <p><strong>تخطيط لوحة التحكم:</strong> <span id="preview_layout"><?php echo $preferences['dashboard_layout']; ?></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>عدد العناصر:</strong> <span id="preview_items"><?php echo $preferences['items_per_page']; ?></span></p>
                            <p><strong>تنسيق التاريخ:</strong> <span id="preview_date"><?php echo date($preferences['date_format']); ?></span></p>
                            <p><strong>تنسيق الوقت:</strong> <span id="preview_time"><?php echo $preferences['time_format'] == '24' ? date('H:i') : date('g:i A'); ?></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث المعاينة عند تغيير التفضيلات
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('preferencesForm');
    const inputs = form.querySelectorAll('select, input[type="checkbox"]');
    
    inputs.forEach(input => {
        input.addEventListener('change', updatePreview);
    });
    
    function updatePreview() {
        // تحديث معاينة المظهر
        const theme = document.getElementById('theme').value;
        document.getElementById('preview_theme').textContent = theme;
        
        // تحديث معاينة اللغة
        const language = document.getElementById('language').value;
        document.getElementById('preview_language').textContent = language;
        
        // تحديث معاينة التخطيط
        const layout = document.getElementById('dashboard_layout').value;
        document.getElementById('preview_layout').textContent = layout;
        
        // تحديث معاينة عدد العناصر
        const items = document.getElementById('items_per_page').value;
        document.getElementById('preview_items').textContent = items;
        
        // تحديث معاينة التاريخ
        const dateFormat = document.getElementById('date_format').value;
        const now = new Date();
        let formattedDate = '';
        
        switch(dateFormat) {
            case 'Y-m-d':
                formattedDate = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0') + '-' + String(now.getDate()).padStart(2, '0');
                break;
            case 'd/m/Y':
                formattedDate = String(now.getDate()).padStart(2, '0') + '/' + String(now.getMonth() + 1).padStart(2, '0') + '/' + now.getFullYear();
                break;
            case 'd-m-Y':
                formattedDate = String(now.getDate()).padStart(2, '0') + '-' + String(now.getMonth() + 1).padStart(2, '0') + '-' + now.getFullYear();
                break;
            case 'M d, Y':
                formattedDate = now.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
                break;
        }
        document.getElementById('preview_date').textContent = formattedDate;
        
        // تحديث معاينة الوقت
        const timeFormat = document.getElementById('time_format').value;
        let formattedTime = '';
        
        if (timeFormat === '24') {
            formattedTime = String(now.getHours()).padStart(2, '0') + ':' + String(now.getMinutes()).padStart(2, '0');
        } else {
            formattedTime = now.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
        }
        document.getElementById('preview_time').textContent = formattedTime;
    }
});

// إعادة تعيين التفضيلات للقيم الافتراضية
function resetPreferences() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع التفضيلات للقيم الافتراضية؟')) {
        document.getElementById('theme').value = 'light';
        document.getElementById('language').value = 'ar';
        document.getElementById('dashboard_layout').value = 'default';
        document.getElementById('items_per_page').value = '25';
        document.getElementById('date_format').value = 'Y-m-d';
        document.getElementById('time_format').value = '24';
        document.getElementById('notifications').checked = true;
        document.getElementById('email_notifications').checked = true;
        
        // تحديث المعاينة
        updatePreview();
    }
}

// التحقق من صحة النموذج
document.getElementById('preferencesForm').addEventListener('submit', function(e) {
    const itemsPerPage = parseInt(document.getElementById('items_per_page').value);
    
    if (itemsPerPage < 1 || itemsPerPage > 1000) {
        e.preventDefault();
        alert('عدد العناصر في الصفحة يجب أن يكون بين 1 و 1000');
        return;
    }
});
</script>

<?php include '../includes/footer.php'; ?>
