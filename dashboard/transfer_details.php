<?php
require_once '../includes/auth.php';
require_once '../includes/transfer_manager.php';

$auth = new Auth();
$transferManager = new TransferManager();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('transfers.view')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى هذه الصفحة');
    exit();
}

$current_user = $auth->getCurrentUser();
$transfer_id = $_GET['id'] ?? null;
$error_message = '';
$success_message = '';

if (!$transfer_id) {
    header('Location: transfers.php?error=معرف التحويل مطلوب');
    exit();
}

// جلب تفاصيل التحويل
$transfer = $transferManager->getTransferDetails($transfer_id);

if (!$transfer) {
    header('Location: transfers.php?error=التحويل غير موجود');
    exit();
}

// معالجة تحديث الحالة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'update_status') {
    if ($auth->hasPermission('transfers.edit')) {
        $new_status = $_POST['new_status'] ?? '';
        $notes = $_POST['notes'] ?? '';
        $location = $_POST['location'] ?? '';
        
        if (!empty($new_status)) {
            $result = $transferManager->updateTransferStatus($transfer_id, $new_status, $current_user['id'], $notes, $location);
            
            if ($result['success']) {
                $success_message = $result['message'];
                // إعادة جلب التفاصيل المحدثة
                $transfer = $transferManager->getTransferDetails($transfer_id);
            } else {
                $error_message = $result['message'];
            }
        } else {
            $error_message = 'يرجى اختيار الحالة الجديدة';
        }
    } else {
        $error_message = 'ليس لديك صلاحية لتحديث حالة التحويل';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل التحويل - <?php echo htmlspecialchars($transfer['reference_number']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 150px;
        }
        
        .info-value {
            color: #212529;
            text-align: left;
        }
        
        .status-timeline {
            position: relative;
            padding: 1rem 0;
        }
        
        .timeline-item {
            position: relative;
            padding-left: 3rem;
            margin-bottom: 1.5rem;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0.75rem;
            top: 0;
            bottom: -1.5rem;
            width: 2px;
            background: #e9ecef;
        }
        
        .timeline-item:last-child::before {
            display: none;
        }
        
        .timeline-icon {
            position: absolute;
            left: 0;
            top: 0;
            width: 1.5rem;
            height: 1.5rem;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
        }
        
        .timeline-icon.completed {
            background: #28a745;
        }
        
        .timeline-icon.current {
            background: #ffc107;
            color: #000;
        }
        
        .amount-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            margin: 1rem 0;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-sent {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status-received {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-paid {
            background: #d1e7dd;
            color: #0f5132;
            border: 1px solid #badbcc;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">تفاصيل التحويل</h2>
                        <p class="text-muted">رقم المرجع: <?php echo htmlspecialchars($transfer['reference_number']); ?></p>
                    </div>
                    <div>
                        <a href="transfer_receipt.php?id=<?php echo $transfer['id']; ?>" class="btn btn-success me-2">
                            <i class="fas fa-receipt me-2"></i>
                            طباعة الإيصال
                        </a>
                        <a href="transfers.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للتحويلات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($error_message): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- تفاصيل التحويل -->
            <div class="col-lg-8">
                <!-- ملخص المبالغ -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            ملخص المبالغ
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="amount-highlight">
                                    <div class="h4 mb-0">
                                        <?php echo htmlspecialchars($transfer['sending_currency_symbol']); ?>
                                        <?php echo number_format($transfer['sending_amount'], 2); ?>
                                    </div>
                                    <small>المبلغ المرسل (<?php echo htmlspecialchars($transfer['sending_currency_code']); ?>)</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="amount-highlight">
                                    <div class="h4 mb-0">
                                        <?php echo htmlspecialchars($transfer['receiving_currency_symbol']); ?>
                                        <?php echo number_format($transfer['receiving_amount'], 2); ?>
                                    </div>
                                    <small>المبلغ المستلم (<?php echo htmlspecialchars($transfer['receiving_currency_code']); ?>)</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="h6 text-muted">سعر الصرف</div>
                                    <div class="fw-bold">
                                        1 <?php echo htmlspecialchars($transfer['sending_currency_code']); ?> = 
                                        <?php echo number_format($transfer['exchange_rate_used'], 6); ?> 
                                        <?php echo htmlspecialchars($transfer['receiving_currency_code']); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="h6 text-muted">رسوم التحويل</div>
                                    <div class="fw-bold">
                                        <?php echo htmlspecialchars($transfer['sending_currency_symbol']); ?>
                                        <?php echo number_format($transfer['transfer_fee_amount'], 2); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="h6 text-muted">إجمالي المطلوب</div>
                                    <div class="fw-bold">
                                        <?php echo htmlspecialchars($transfer['sending_currency_symbol']); ?>
                                        <?php echo number_format($transfer['sending_amount'] + $transfer['transfer_fee_amount'], 2); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل المرسل والمستفيد -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user me-2"></i>
                                    تفاصيل المرسل
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="info-row">
                                    <span class="info-label">الاسم</span>
                                    <span class="info-value"><?php echo htmlspecialchars($transfer['sender_name']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">رقم الهاتف</span>
                                    <span class="info-value"><?php echo htmlspecialchars($transfer['sender_phone'] ?? 'غير محدد'); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">البريد الإلكتروني</span>
                                    <span class="info-value"><?php echo htmlspecialchars($transfer['sender_email'] ?? 'غير محدد'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user-check me-2"></i>
                                    تفاصيل المستفيد
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="info-row">
                                    <span class="info-label">الاسم</span>
                                    <span class="info-value"><?php echo htmlspecialchars($transfer['recipient_name']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">البلد</span>
                                    <span class="info-value"><?php echo htmlspecialchars($transfer['recipient_country']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">البنك/الوكيل</span>
                                    <span class="info-value"><?php echo htmlspecialchars($transfer['recipient_bank'] ?? 'غير محدد'); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">تفاصيل الحساب</span>
                                    <span class="info-value"><?php echo htmlspecialchars($transfer['recipient_account_details'] ?? 'غير محدد'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات التحويل -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات التحويل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="info-row">
                            <span class="info-label">تاريخ الإنشاء</span>
                            <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($transfer['created_at'])); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">طريقة التحويل</span>
                            <span class="info-value">
                                <?php 
                                $methods = [
                                    'cash' => 'استلام نقدي',
                                    'bank_transfer' => 'تحويل بنكي',
                                    'online' => 'خدمة إلكترونية'
                                ];
                                echo $methods[$transfer['transfer_method']] ?? $transfer['transfer_method'];
                                ?>
                            </span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">الفرع</span>
                            <span class="info-value"><?php echo htmlspecialchars($transfer['branch_name']); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">الموظف المنشئ</span>
                            <span class="info-value"><?php echo htmlspecialchars($transfer['created_by_name']); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">الحالة الحالية</span>
                            <span class="info-value">
                                <?php 
                                $status_labels = [
                                    'pending' => 'معلق',
                                    'sent' => 'مرسل',
                                    'received' => 'مستلم',
                                    'paid' => 'مدفوع',
                                    'cancelled' => 'ملغي'
                                ];
                                ?>
                                <span class="status-badge status-<?php echo $transfer['status']; ?>">
                                    <?php echo $status_labels[$transfer['status']] ?? $transfer['status']; ?>
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-4">
                <!-- تحديث الحالة -->
                <?php if ($auth->hasPermission('transfers.edit') && $transfer['status'] != 'paid' && $transfer['status'] != 'cancelled'): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            تحديث الحالة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="update_status">
                            
                            <div class="mb-3">
                                <label class="form-label">الحالة الجديدة</label>
                                <select class="form-control" name="new_status" required>
                                    <option value="">اختر الحالة</option>
                                    <?php if ($transfer['status'] == 'pending'): ?>
                                        <option value="sent">مرسل</option>
                                        <option value="cancelled">ملغي</option>
                                    <?php elseif ($transfer['status'] == 'sent'): ?>
                                        <option value="received">مستلم</option>
                                        <option value="cancelled">ملغي</option>
                                    <?php elseif ($transfer['status'] == 'received'): ?>
                                        <option value="paid">مدفوع</option>
                                        <option value="cancelled">ملغي</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الموقع (اختياري)</label>
                                <input type="text" class="form-control" name="location" placeholder="الموقع الحالي">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات حول التحديث"></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-save me-2"></i>
                                تحديث الحالة
                            </button>
                        </form>
                    </div>
                </div>
                <?php endif; ?>

                <!-- تتبع الحالة -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-route me-2"></i>
                            تتبع الحالة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($transfer['status_history'])): ?>
                            <div class="status-timeline">
                                <?php foreach ($transfer['status_history'] as $history): ?>
                                <div class="timeline-item">
                                    <div class="timeline-icon <?php echo $history['new_status'] == $transfer['status'] ? 'current' : 'completed'; ?>">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold">
                                            <?php echo $status_labels[$history['new_status']] ?? $history['new_status']; ?>
                                        </div>
                                        <div class="text-muted small">
                                            <?php echo date('Y-m-d H:i', strtotime($history['status_date'])); ?>
                                        </div>
                                        <div class="text-muted small">
                                            بواسطة: <?php echo htmlspecialchars($history['changed_by_name']); ?>
                                        </div>
                                        <?php if ($history['location']): ?>
                                        <div class="text-muted small">
                                            الموقع: <?php echo htmlspecialchars($history['location']); ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php if ($history['notes']): ?>
                                        <div class="text-muted small">
                                            <?php echo htmlspecialchars($history['notes']); ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php if ($history['estimated_completion_date']): ?>
                                        <div class="text-info small">
                                            الإنجاز المتوقع: <?php echo date('Y-m-d H:i', strtotime($history['estimated_completion_date'])); ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center p-3">
                                <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">لا يوجد تاريخ للحالات</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إخفاء رسائل التنبيه تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
