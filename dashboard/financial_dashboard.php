<?php
require_once '../includes/auth.php';

$auth = new Auth();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('reports.financial')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى لوحة الأداء المالي');
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'لوحة الأداء المالي - ' . SYSTEM_NAME;
$page_header = 'لوحة الأداء المالي';
$page_subtitle = 'مراقبة الأداء المالي والإحصائيات';
$page_icon = 'fas fa-tachometer-alt';
$show_breadcrumb = true;

// جلب البيانات المالية
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $today = date('Y-m-d');
    $yesterday = date('Y-m-d', strtotime('-1 day'));
    $week_start = date('Y-m-d', strtotime('-7 days'));
    $month_start = date('Y-m-01');
    $last_month_start = date('Y-m-01', strtotime('-1 month'));
    $last_month_end = date('Y-m-t', strtotime('-1 month'));
    
    // أرباح اليوم
    $stmt = $db->prepare("
        SELECT 
            COALESCE(SUM(dep.total_profit), 0) as exchange_profit,
            COALESCE(SUM(dtp.total_profit), 0) as transfer_profit
        FROM (SELECT COALESCE(SUM(total_profit), 0) as total_profit FROM daily_exchange_profits WHERE profit_date = :today) dep
        CROSS JOIN (SELECT COALESCE(SUM(total_profit), 0) as total_profit FROM daily_transfer_profits WHERE profit_date = :today) dtp
    ");
    $stmt->bindParam(':today', $today);
    $stmt->execute();
    $today_profits = $stmt->fetch();
    
    // أرباح أمس للمقارنة
    $stmt = $db->prepare("
        SELECT 
            COALESCE(SUM(dep.total_profit), 0) as exchange_profit,
            COALESCE(SUM(dtp.total_profit), 0) as transfer_profit
        FROM (SELECT COALESCE(SUM(total_profit), 0) as total_profit FROM daily_exchange_profits WHERE profit_date = :yesterday) dep
        CROSS JOIN (SELECT COALESCE(SUM(total_profit), 0) as total_profit FROM daily_transfer_profits WHERE profit_date = :yesterday) dtp
    ");
    $stmt->bindParam(':yesterday', $yesterday);
    $stmt->execute();
    $yesterday_profits = $stmt->fetch();
    
    // أرباح الشهر الحالي
    $stmt = $db->prepare("
        SELECT 
            COALESCE(SUM(dep.total_profit), 0) as exchange_profit,
            COALESCE(SUM(dtp.total_profit), 0) as transfer_profit
        FROM (SELECT COALESCE(SUM(total_profit), 0) as total_profit FROM daily_exchange_profits WHERE profit_date >= :month_start) dep
        CROSS JOIN (SELECT COALESCE(SUM(total_profit), 0) as total_profit FROM daily_transfer_profits WHERE profit_date >= :month_start) dtp
    ");
    $stmt->bindParam(':month_start', $month_start);
    $stmt->execute();
    $month_profits = $stmt->fetch();
    
    // أرباح الشهر الماضي للمقارنة
    $stmt = $db->prepare("
        SELECT 
            COALESCE(SUM(dep.total_profit), 0) as exchange_profit,
            COALESCE(SUM(dtp.total_profit), 0) as transfer_profit
        FROM (SELECT COALESCE(SUM(total_profit), 0) as total_profit FROM daily_exchange_profits WHERE profit_date BETWEEN :last_month_start AND :last_month_end) dep
        CROSS JOIN (SELECT COALESCE(SUM(total_profit), 0) as total_profit FROM daily_transfer_profits WHERE profit_date BETWEEN :last_month_start AND :last_month_end) dtp
    ");
    $stmt->bindParam(':last_month_start', $last_month_start);
    $stmt->bindParam(':last_month_end', $last_month_end);
    $stmt->execute();
    $last_month_profits = $stmt->fetch();
    
    // حجم المعاملات اليوم
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as transaction_count,
            COALESCE(SUM(total_amount_base_currency), 0) as total_volume
        FROM transactions 
        WHERE DATE(transaction_date) = :today
    ");
    $stmt->bindParam(':today', $today);
    $stmt->execute();
    $today_volume = $stmt->fetch();
    
    // أفضل العملات أداءً (آخر 7 أيام)
    $stmt = $db->prepare("
        SELECT 
            c1.code as from_currency,
            c2.code as to_currency,
            SUM(dep.total_profit) as total_profit,
            COUNT(dep.id) as transaction_count
        FROM daily_exchange_profits dep
        JOIN currencies c1 ON dep.from_currency_id = c1.id
        JOIN currencies c2 ON dep.to_currency_id = c2.id
        WHERE dep.profit_date >= :week_start
        GROUP BY dep.from_currency_id, dep.to_currency_id
        ORDER BY total_profit DESC
        LIMIT 5
    ");
    $stmt->bindParam(':week_start', $week_start);
    $stmt->execute();
    $top_currency_pairs = $stmt->fetchAll();
    
    // أفضل الممرات أداءً (التحويلات)
    $stmt = $db->prepare("
        SELECT 
            corridor,
            SUM(total_profit) as total_profit,
            SUM(total_transfers) as total_transfers
        FROM daily_transfer_profits
        WHERE profit_date >= :week_start
        GROUP BY corridor
        ORDER BY total_profit DESC
        LIMIT 5
    ");
    $stmt->bindParam(':week_start', $week_start);
    $stmt->execute();
    $top_corridors = $stmt->fetchAll();
    
} catch (Exception $e) {
    $today_profits = ['exchange_profit' => 0, 'transfer_profit' => 0];
    $yesterday_profits = ['exchange_profit' => 0, 'transfer_profit' => 0];
    $month_profits = ['exchange_profit' => 0, 'transfer_profit' => 0];
    $last_month_profits = ['exchange_profit' => 0, 'transfer_profit' => 0];
    $today_volume = ['transaction_count' => 0, 'total_volume' => 0];
    $top_currency_pairs = [];
    $top_corridors = [];
}

// حساب النسب المئوية للتغيير
$today_total = $today_profits['exchange_profit'] + $today_profits['transfer_profit'];
$yesterday_total = $yesterday_profits['exchange_profit'] + $yesterday_profits['transfer_profit'];
$daily_change = $yesterday_total > 0 ? (($today_total - $yesterday_total) / $yesterday_total) * 100 : 0;

$month_total = $month_profits['exchange_profit'] + $month_profits['transfer_profit'];
$last_month_total = $last_month_profits['exchange_profit'] + $last_month_profits['transfer_profit'];
$monthly_change = $last_month_total > 0 ? (($month_total - $last_month_total) / $last_month_total) * 100 : 0;

include '../includes/header.php';
?>

<!-- صفحة لوحة الأداء المالي -->
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">لوحة الأداء المالي</h2>
                        <p class="text-muted">مراقبة الأداء المالي في الوقت الفعلي</p>
                    </div>
                    <div>
                        <button class="btn btn-primary me-2" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-2"></i>
                            تحديث البيانات
                        </button>
                        <a href="reports.php" class="btn btn-outline-secondary">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير التفصيلية
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- المؤشرات الرئيسية -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="metric-card">
                    <div class="metric-label">إجمالي الأرباح اليوم</div>
                    <div class="metric-value">$<?php echo number_format($today_total, 2); ?></div>
                    <div class="metric-change <?php echo $daily_change >= 0 ? 'change-positive' : 'change-negative'; ?>">
                        <i class="fas fa-<?php echo $daily_change >= 0 ? 'arrow-up' : 'arrow-down'; ?> me-1"></i>
                        <?php echo abs(round($daily_change, 1)); ?>% مقارنة بالأمس
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="metric-card">
                    <div class="metric-label">أرباح الصرافة اليوم</div>
                    <div class="metric-value">$<?php echo number_format($today_profits['exchange_profit'], 2); ?></div>
                    <div class="metric-change text-info">
                        <i class="fas fa-exchange-alt me-1"></i>
                        <?php echo $today_volume['transaction_count']; ?> معاملة
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="metric-card">
                    <div class="metric-label">أرباح التحويلات اليوم</div>
                    <div class="metric-value">$<?php echo number_format($today_profits['transfer_profit'], 2); ?></div>
                    <div class="metric-change text-warning">
                        <i class="fas fa-paper-plane me-1"></i>
                        حجم التداول: $<?php echo number_format($today_volume['total_volume'], 0); ?>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="metric-card">
                    <div class="metric-label">أرباح الشهر الحالي</div>
                    <div class="metric-value">$<?php echo number_format($month_total, 2); ?></div>
                    <div class="metric-change <?php echo $monthly_change >= 0 ? 'change-positive' : 'change-negative'; ?>">
                        <i class="fas fa-<?php echo $monthly_change >= 0 ? 'arrow-up' : 'arrow-down'; ?> me-1"></i>
                        <?php echo abs(round($monthly_change, 1)); ?>% مقارنة بالشهر الماضي
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الرسوم البيانية -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">اتجاه الأرباح (آخر 30 يوم)</h5>
                            <div class="btn-group btn-group-sm" role="group">
                                <input type="radio" class="btn-check" name="chartPeriod" id="chart7days" checked>
                                <label class="btn btn-outline-primary" for="chart7days">7 أيام</label>
                                
                                <input type="radio" class="btn-check" name="chartPeriod" id="chart30days">
                                <label class="btn btn-outline-primary" for="chart30days">30 يوم</label>
                                
                                <input type="radio" class="btn-check" name="chartPeriod" id="chart90days">
                                <label class="btn btn-outline-primary" for="chart90days">90 يوم</label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="profitChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">توزيع الأرباح حسب النوع</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="distributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-4">
                <!-- أفضل أزواج العملات -->
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy me-2 text-warning"></i>
                            أفضل أزواج العملات
                        </h5>
                        <small class="text-muted">آخر 7 أيام</small>
                    </div>
                    <div class="card-body">
                        <?php if (empty($top_currency_pairs)): ?>
                            <div class="text-center p-3">
                                <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">لا توجد بيانات متاحة</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($top_currency_pairs as $index => $pair): ?>
                            <div class="top-performer">
                                <div class="d-flex align-items-center">
                                    <div class="performer-rank"><?php echo $index + 1; ?></div>
                                    <div class="ms-3">
                                        <div class="fw-bold"><?php echo $pair['from_currency'] . '/' . $pair['to_currency']; ?></div>
                                        <small class="text-muted"><?php echo $pair['transaction_count']; ?> معاملة</small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold text-success">$<?php echo number_format($pair['total_profit'], 2); ?></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- أفضل ممرات التحويل -->
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-route me-2 text-info"></i>
                            أفضل ممرات التحويل
                        </h5>
                        <small class="text-muted">آخر 7 أيام</small>
                    </div>
                    <div class="card-body">
                        <?php if (empty($top_corridors)): ?>
                            <div class="text-center p-3">
                                <i class="fas fa-paper-plane fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">لا توجد بيانات متاحة</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($top_corridors as $index => $corridor): ?>
                            <div class="top-performer">
                                <div class="d-flex align-items-center">
                                    <div class="performer-rank"><?php echo $index + 1; ?></div>
                                    <div class="ms-3">
                                        <div class="fw-bold"><?php echo htmlspecialchars($corridor['corridor']); ?></div>
                                        <small class="text-muted"><?php echo $corridor['total_transfers']; ?> تحويل</small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold text-success">$<?php echo number_format($corridor['total_profit'], 2); ?></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- مؤشر الأداء الإجمالي -->
                <div class="card">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-gauge-high me-2 text-primary"></i>
                            مؤشر الأداء
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="d-flex justify-content-center mb-3">
                            <svg class="progress-ring">
                                <circle class="progress-ring-circle"></circle>
                                <circle class="progress-ring-progress" style="stroke-dashoffset: 98.02;"></circle>
                            </svg>
                        </div>
                        <div class="h4 text-primary">85%</div>
                        <p class="text-muted mb-0">أداء ممتاز</p>
                        <small class="text-muted">مقارنة بالهدف الشهري</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // إعداد الرسوم البيانية
        const ctx1 = document.getElementById('profitChart').getContext('2d');
        const profitChart = new Chart(ctx1, {
            type: 'line',
            data: {
                labels: ['اليوم 1', 'اليوم 2', 'اليوم 3', 'اليوم 4', 'اليوم 5', 'اليوم 6', 'اليوم 7'],
                datasets: [{
                    label: 'أرباح الصرافة',
                    data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }, {
                    label: 'أرباح التحويلات',
                    data: [800, 1200, 1800, 2200, 1500, 2100, 2800],
                    borderColor: '#764ba2',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        const ctx2 = document.getElementById('distributionChart').getContext('2d');
        const distributionChart = new Chart(ctx2, {
            type: 'doughnut',
            data: {
                labels: ['أرباح الصرافة', 'أرباح التحويلات', 'رسوم أخرى'],
                datasets: [{
                    data: [<?php echo $today_profits['exchange_profit']; ?>, <?php echo $today_profits['transfer_profit']; ?>, 500],
                    backgroundColor: ['#667eea', '#764ba2', '#f093fb']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });

        function refreshData() {
            location.reload();
        }

        // تحديث الرسم البياني حسب الفترة المختارة
        document.querySelectorAll('input[name="chartPeriod"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // يمكن تطوير هذه الدالة لتحديث البيانات حسب الفترة
                console.log('تم تغيير الفترة إلى:', this.id);
            });
        });
    </script>

<?php include '../includes/footer.php'; ?>
