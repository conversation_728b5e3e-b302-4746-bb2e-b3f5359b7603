<?php
/**
 * API لجلب سجل نشاط المستخدم
 * Trust Plus System
 */

require_once '../../config.php';
require_once '../../includes/auth.php';

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح بالوصول'
    ]);
    exit();
}

$current_user = $auth->getCurrentUser();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب آخر 20 نشاط للمستخدم الحالي
    $stmt = $db->prepare("
        SELECT 
            action,
            table_name,
            record_id,
            description,
            ip_address,
            created_at
        FROM audit_logs 
        WHERE user_id = :user_id 
        ORDER BY created_at DESC 
        LIMIT 20
    ");
    
    $stmt->execute([':user_id' => $current_user['id']]);
    $activities = $stmt->fetchAll();
    
    // تنسيق البيانات
    $formatted_activities = [];
    foreach ($activities as $activity) {
        $formatted_activities[] = [
            'action' => $activity['action'],
            'table_name' => $activity['table_name'],
            'record_id' => $activity['record_id'],
            'description' => $activity['description'],
            'ip_address' => $activity['ip_address'],
            'created_at' => $activity['created_at'],
            'formatted_date' => date('Y-m-d H:i:s', strtotime($activity['created_at']))
        ];
    }
    
    echo json_encode([
        'success' => true,
        'activities' => $formatted_activities,
        'total_count' => count($formatted_activities)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في جلب البيانات: ' . $e->getMessage()
    ]);
}
?>
