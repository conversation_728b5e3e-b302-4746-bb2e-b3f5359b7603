<?php
/**
 * Trust Plus - Dashboard Statistics API
 * API إحصائيات لوحة التحكم
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 0); // إخفاء الأخطاء في JSON

try {
    require_once '../../config.php';
    require_once '../../includes/database.php';
    require_once '../../includes/auth.php';
    
    $auth = new Auth();
    
    // التحقق من صحة الجلسة
    if (!$auth->checkSession()) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'غير مصرح بالوصول'
        ]);
        exit();
    }
    
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    // إحصائيات اليوم
    $today = date('Y-m-d');
    $stats = [];
    
    // إجمالي العملاء
    try {
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM customers WHERE is_active = 1");
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['total_customers'] = $result['total'] ?? 0;
    } catch (Exception $e) {
        $stats['total_customers'] = 0;
    }
    
    // عمليات الصرافة اليوم
    try {
        $stmt = $db->prepare("
            SELECT COUNT(*) as total, COALESCE(SUM(amount_from), 0) as total_amount 
            FROM exchange_transactions 
            WHERE DATE(created_at) = :today AND status = 'completed'
        ");
        $stmt->bindParam(':today', $today);
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['today_exchanges'] = $result['total'] ?? 0;
        $stats['today_exchange_amount'] = $result['total_amount'] ?? 0;
    } catch (Exception $e) {
        $stats['today_exchanges'] = 0;
        $stats['today_exchange_amount'] = 0;
    }
    
    // التحويلات المعلقة
    try {
        $stmt = $db->prepare("
            SELECT COUNT(*) as total 
            FROM transfers 
            WHERE status IN ('pending', 'processing')
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['pending_transfers'] = $result['total'] ?? 0;
    } catch (Exception $e) {
        $stats['pending_transfers'] = 0;
    }
    
    // إجمالي الإيرادات اليوم
    try {
        $stmt = $db->prepare("
            SELECT COALESCE(SUM(profit_amount), 0) as total_profit 
            FROM exchange_transactions 
            WHERE DATE(created_at) = :today AND status = 'completed'
        ");
        $stmt->bindParam(':today', $today);
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['today_revenue'] = $result['total_profit'] ?? 0;
    } catch (Exception $e) {
        $stats['today_revenue'] = 0;
    }
    
    // إحصائيات إضافية
    try {
        // المعاملات الأخيرة
        $stmt = $db->prepare("
            SELECT COUNT(*) as total 
            FROM exchange_transactions 
            WHERE DATE(created_at) = :today
        ");
        $stmt->bindParam(':today', $today);
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['today_transactions'] = $result['total'] ?? 0;
    } catch (Exception $e) {
        $stats['today_transactions'] = 0;
    }
    
    // أسعار الصرف الحالية (عينة)
    try {
        $stmt = $db->prepare("
            SELECT currency_from, currency_to, buy_rate, sell_rate 
            FROM exchange_rates 
            WHERE is_active = 1 
            ORDER BY updated_at DESC 
            LIMIT 5
        ");
        $stmt->execute();
        $stats['exchange_rates'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $stats['exchange_rates'] = [];
    }
    
    // معلومات النظام
    $stats['system_info'] = [
        'current_time' => date('Y-m-d H:i:s'),
        'server_time' => time(),
        'timezone' => date_default_timezone_get(),
        'system_name' => defined('SYSTEM_NAME') ? SYSTEM_NAME : 'Trust Plus',
        'system_version' => defined('SYSTEM_VERSION') ? SYSTEM_VERSION : '1.0.0'
    ];
    
    // إرجاع النتائج
    echo json_encode([
        'success' => true,
        'data' => $stats,
        'timestamp' => time(),
        'message' => 'تم تحميل الإحصائيات بنجاح'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage(),
        'error_code' => 'STATS_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}
?>
