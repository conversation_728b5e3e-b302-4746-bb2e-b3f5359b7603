<?php
require_once '../includes/auth.php';

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('exchange.view')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى هذه الصفحة');
    exit();
}

$transaction_id = $_GET['id'] ?? null;
$success_message = $_GET['success'] ?? '';

if (!$transaction_id) {
    header('Location: exchange.php?error=معرف المعاملة مطلوب');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب تفاصيل المعاملة
    $stmt = $db->prepare("
        SELECT t.*, cet.*, c.full_name as customer_name, c.id_number, c.phone,
               c1.name as sell_currency_name, c1.code as sell_currency_code, c1.symbol as sell_currency_symbol,
               c2.name as buy_currency_name, c2.code as buy_currency_code, c2.symbol as buy_currency_symbol,
               u.full_name as user_name, b.name as branch_name
        FROM transactions t
        JOIN currency_exchange_transactions cet ON t.id = cet.transaction_id
        JOIN customers c ON t.customer_id = c.id
        JOIN currencies c1 ON cet.sell_currency_id = c1.id
        JOIN currencies c2 ON cet.buy_currency_id = c2.id
        JOIN users u ON t.user_id = u.id
        JOIN branches b ON t.branch_id = b.id
        WHERE t.id = :transaction_id AND t.transaction_type = 'exchange'
    ");
    $stmt->bindParam(':transaction_id', $transaction_id);
    $stmt->execute();
    
    $transaction = $stmt->fetch();
    
    if (!$transaction) {
        header('Location: exchange.php?error=المعاملة غير موجودة');
        exit();
    }
    
} catch (Exception $e) {
    header('Location: exchange.php?error=خطأ في جلب بيانات المعاملة');
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال الصرافة - <?php echo htmlspecialchars($transaction['reference_number']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .receipt-container {
            max-width: 600px;
            margin: 2rem auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .receipt-body {
            padding: 2rem;
        }
        
        .company-logo {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
        }
        
        .transaction-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            text-align: center;
        }
        
        .amount-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .success-badge {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 25px;
            padding: 0.5rem 1rem;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        @media print {
            body {
                background: white;
            }
            
            .receipt-container {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }
            
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header -->
        <div class="receipt-header">
            <div class="company-logo">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h3 class="mb-1">Trust Plus</h3>
            <p class="mb-0">نظام إدارة التحويلات المالية</p>
        </div>
        
        <!-- Body -->
        <div class="receipt-body">
            <?php if ($success_message): ?>
            <div class="text-center mb-4">
                <div class="success-badge">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="text-center mb-4">
                <h4 class="text-primary">إيصال عملية صرافة</h4>
                <p class="text-muted">رقم المرجع: <?php echo htmlspecialchars($transaction['reference_number']); ?></p>
            </div>
            
            <!-- ملخص المعاملة -->
            <div class="transaction-summary">
                <div class="row">
                    <div class="col-6">
                        <div class="text-muted small">المبلغ المباع</div>
                        <div class="amount-display text-danger">
                            <?php echo htmlspecialchars($transaction['sell_currency_symbol']); ?>
                            <?php echo number_format($transaction['sell_amount'], 2); ?>
                        </div>
                        <div class="small"><?php echo htmlspecialchars($transaction['sell_currency_name']); ?></div>
                    </div>
                    <div class="col-6">
                        <div class="text-muted small">المبلغ المستلم</div>
                        <div class="amount-display text-success">
                            <?php echo htmlspecialchars($transaction['buy_currency_symbol']); ?>
                            <?php echo number_format($transaction['buy_amount'], 2); ?>
                        </div>
                        <div class="small"><?php echo htmlspecialchars($transaction['buy_currency_name']); ?></div>
                    </div>
                </div>
                
                <hr class="my-3">
                
                <div class="row">
                    <div class="col-6">
                        <div class="text-muted small">سعر الصرف</div>
                        <div class="fw-bold">
                            1 <?php echo htmlspecialchars($transaction['sell_currency_code']); ?> = 
                            <?php echo number_format($transaction['exchange_rate_used'], 6); ?> 
                            <?php echo htmlspecialchars($transaction['buy_currency_code']); ?>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-muted small">العمولة</div>
                        <div class="fw-bold">
                            <?php echo htmlspecialchars($transaction['sell_currency_symbol']); ?>
                            <?php echo number_format($transaction['commission_amount'], 2); ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- تفاصيل العميل -->
            <div class="mb-4">
                <h6 class="text-primary mb-3">تفاصيل العميل</h6>
                <div class="info-row">
                    <span class="info-label">الاسم</span>
                    <span class="info-value"><?php echo htmlspecialchars($transaction['customer_name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهوية</span>
                    <span class="info-value"><?php echo htmlspecialchars($transaction['id_number']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهاتف</span>
                    <span class="info-value"><?php echo htmlspecialchars($transaction['phone'] ?? 'غير محدد'); ?></span>
                </div>
            </div>
            
            <!-- تفاصيل المعاملة -->
            <div class="mb-4">
                <h6 class="text-primary mb-3">تفاصيل المعاملة</h6>
                <div class="info-row">
                    <span class="info-label">تاريخ المعاملة</span>
                    <span class="info-value"><?php echo date('Y-m-d H:i:s', strtotime($transaction['created_at'])); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الفرع</span>
                    <span class="info-value"><?php echo htmlspecialchars($transaction['branch_name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الموظف</span>
                    <span class="info-value"><?php echo htmlspecialchars($transaction['user_name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الحالة</span>
                    <span class="info-value">
                        <span class="badge bg-success">مكتملة</span>
                    </span>
                </div>
            </div>
            
            <!-- ملاحظات -->
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:</h6>
                <ul class="mb-0 small">
                    <li>يرجى الاحتفاظ بهذا الإيصال كإثبات للمعاملة</li>
                    <li>في حالة وجود أي استفسار، يرجى التواصل مع خدمة العملاء</li>
                    <li>جميع المعاملات خاضعة لشروط وأحكام الشركة</li>
                </ul>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="d-flex gap-2 no-print">
                <button onclick="window.print()" class="btn btn-primary flex-fill">
                    <i class="fas fa-print me-2"></i>
                    طباعة الإيصال
                </button>
                <a href="exchange.php" class="btn btn-outline-secondary flex-fill">
                    <i class="fas fa-plus me-2"></i>
                    عملية جديدة
                </a>
                <a href="exchange_history.php" class="btn btn-outline-info flex-fill">
                    <i class="fas fa-history me-2"></i>
                    السجل
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() {
        //     setTimeout(function() {
        //         window.print();
        //     }, 1000);
        // };
    </script>
</body>
</html>
