<?php
require_once '../includes/auth.php';
require_once '../includes/exchange_manager.php';
require_once '../includes/customer_manager.php';

$auth = new Auth();
$exchangeManager = new ExchangeManager();
$customerManager = new CustomerManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('exchange.create')) {
    header('Location: exchange.php?error=' . urlencode('ليس لديك صلاحية لإنشاء عمليات صرافة'));
    exit();
}

$current_user = $auth->getCurrentUser();

// التحقق من أن الطلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: exchange.php?error=' . urlencode('طريقة طلب غير صحيحة'));
    exit();
}

// سجل النموذج المرسل للتصحيح
error_log("Exchange form submitted: " . print_r($_POST, true));

// جمع البيانات من النموذج
$customer_id = intval($_POST['customer_id'] ?? 0);

// إذا لم يتم تحديد عميل، إنشاء عميل مجهول
if (empty($customer_id)) {
    // إنشاء عميل مجهول
    $anonymous_customer = $customerManager->createAnonymousCustomer();
    if ($anonymous_customer['success']) {
        $customer_id = $anonymous_customer['customer_id'];
    } else {
        header('Location: exchange.php?error=' . urlencode('فشل في إنشاء عميل مجهول: ' . $anonymous_customer['message']));
        exit();
    }
}

// جمع بيانات النموذج مع التحقق من صحة القيم
$sell_currency_id = intval($_POST['sell_currency_id'] ?? 0);
$buy_currency_id = intval($_POST['buy_currency_id'] ?? 0);
$sell_amount = floatval($_POST['sell_amount'] ?? 0);
$buy_amount = floatval($_POST['buy_amount'] ?? 0);
$branch_id = intval($_POST['branch_id'] ?? $current_user['branch_id']);

// التحقق من صحة البيانات الأساسية
if (empty($sell_currency_id) || empty($buy_currency_id) || 
    $sell_amount <= 0 || $sell_currency_id === $buy_currency_id) {
    $error_msg = '';
    if (empty($sell_currency_id)) {
        $error_msg = 'يجب اختيار العملة المباعة';
    } elseif (empty($buy_currency_id)) {
        $error_msg = 'يجب اختيار العملة المشتراة';
    } elseif ($sell_amount <= 0) {
        $error_msg = 'يجب إدخال مبلغ صحيح للبيع';
    } elseif ($sell_currency_id === $buy_currency_id) {
        $error_msg = 'لا يمكن اختيار نفس العملة للبيع والشراء';
    } else {
        $error_msg = 'بيانات غير مكتملة';
    }
    header('Location: exchange.php?error=' . urlencode($error_msg));
    exit();
}

// جمع البيانات في مصفوفة
$data = [
    'customer_id' => $customer_id,
    'sell_currency_id' => $sell_currency_id,
    'buy_currency_id' => $buy_currency_id,
    'sell_amount' => $sell_amount,
    'buy_amount' => $buy_amount,
    'branch_id' => $branch_id
];

// معالجة عملية الصرافة
$result = $exchangeManager->createExchangeTransaction($data, $current_user['id']);

if ($result['success']) {
    // تسجيل نجاح العملية
    error_log("Exchange transaction successful: " . print_r($result, true));
    
    // إعادة توجيه إلى صفحة الإيصال
    header('Location: exchange_receipt.php?id=' . $result['transaction_id'] . '&success=' . urlencode($result['message']));
} else {
    // تسجيل فشل العملية
    error_log("Exchange transaction failed: " . print_r($result, true));
    
    // إعادة توجيه مع رسالة خطأ
    header('Location: exchange.php?error=' . urlencode($result['message'] ?? 'حدث خطأ أثناء تنفيذ عملية الصرافة'));
}
exit();
?>
