<?php
require_once '../includes/auth.php';
require_once '../includes/customer_manager.php';

$auth = new Auth();
$customerManager = new CustomerManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('customers.view')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى هذه الصفحة');
    exit();
}

$current_user = $auth->getCurrentUser();
$customer_id = $_GET['id'] ?? null;

if (!$customer_id) {
    header('Location: customers.php?error=معرف العميل مطلوب');
    exit();
}

// جلب بيانات العميل
$customer = $customerManager->getCustomerById($customer_id);

if (!$customer) {
    header('Location: customers.php?error=العميل غير موجود');
    exit();
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب فحوصات الامتثال
    $stmt = $db->prepare("
        SELECT cc.*, u.full_name as performed_by_name
        FROM compliance_checks cc
        LEFT JOIN users u ON cc.performed_by_user_id = u.id
        WHERE cc.customer_id = :customer_id
        ORDER BY cc.check_date DESC
        LIMIT 10
    ");
    $stmt->bindParam(':customer_id', $customer_id);
    $stmt->execute();
    $compliance_checks = $stmt->fetchAll();
    
    // جلب تاريخ التحديثات
    $stmt = $db->prepare("
        SELECT ch.*, u.full_name as changed_by_name
        FROM customer_history ch
        LEFT JOIN users u ON ch.changed_by_user_id = u.id
        WHERE ch.customer_id = :customer_id
        ORDER BY ch.changed_at DESC
        LIMIT 10
    ");
    $stmt->bindParam(':customer_id', $customer_id);
    $stmt->execute();
    $customer_history = $stmt->fetchAll();
    
    // جلب المستندات
    $stmt = $db->prepare("
        SELECT d.*, u1.full_name as uploaded_by_name, u2.full_name as verified_by_name
        FROM documents d
        LEFT JOIN users u1 ON d.uploaded_by_user_id = u1.id
        LEFT JOIN users u2 ON d.verified_by_user_id = u2.id
        WHERE d.related_entity_type = 'customer' AND d.related_entity_id = :customer_id
        ORDER BY d.upload_date DESC
    ");
    $stmt->bindParam(':customer_id', $customer_id);
    $stmt->execute();
    $documents = $stmt->fetchAll();
    
    // جلب المعاملات الأخيرة
    $stmt = $db->prepare("
        SELECT t.*, u.full_name as user_name
        FROM transactions t
        LEFT JOIN users u ON t.user_id = u.id
        WHERE t.customer_id = :customer_id
        ORDER BY t.transaction_date DESC
        LIMIT 5
    ");
    $stmt->bindParam(':customer_id', $customer_id);
    $stmt->execute();
    $recent_transactions = $stmt->fetchAll();
    
} catch (Exception $e) {
    $compliance_checks = [];
    $customer_history = [];
    $documents = [];
    $recent_transactions = [];
}

// دوال مساعدة
function getKYCStatusText($status) {
    switch ($status) {
        case 'pending': return 'قيد المراجعة';
        case 'approved': return 'معتمد';
        case 'rejected': return 'مرفوض';
        case 'under_review': return 'تحت المراجعة';
        default: return 'غير محدد';
    }
}

function getRiskLevelText($level) {
    switch ($level) {
        case 'low': return 'منخفض';
        case 'medium': return 'متوسط';
        case 'high': return 'عالي';
        default: return 'غير محدد';
    }
}

function getKYCStatusClass($status) {
    switch ($status) {
        case 'approved': return 'success';
        case 'rejected': return 'danger';
        case 'under_review': return 'warning';
        case 'pending': return 'secondary';
        default: return 'secondary';
    }
}

function getRiskLevelClass($level) {
    switch ($level) {
        case 'low': return 'success';
        case 'medium': return 'warning';
        case 'high': return 'danger';
        default: return 'secondary';
    }
}

function getCheckTypeText($type) {
    switch ($type) {
        case 'kyc_verification': return 'التحقق من الهوية';
        case 'aml_screening': return 'فحص مكافحة غسيل الأموال';
        case 'sanctions_check': return 'فحص قوائم العقوبات';
        case 'pep_check': return 'فحص الشخصيات السياسية';
        case 'risk_assessment': return 'تقييم المخاطر';
        default: return $type;
    }
}

function getCheckResultText($result) {
    switch ($result) {
        case 'pass': return 'نجح';
        case 'fail': return 'فشل';
        case 'pending': return 'قيد المراجعة';
        case 'requires_review': return 'يتطلب مراجعة';
        default: return $result;
    }
}

function getCheckResultClass($result) {
    switch ($result) {
        case 'pass': return 'success';
        case 'fail': return 'danger';
        case 'pending': return 'warning';
        case 'requires_review': return 'info';
        default: return 'secondary';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل العميل - <?php echo htmlspecialchars($customer['full_name']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        
        .customer-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        
        .info-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.25rem;
        }
        
        .info-value {
            color: #212529;
        }
        
        .timeline {
            position: relative;
            padding-left: 2rem;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -0.5rem;
            top: 0.5rem;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #667eea;
        }
        
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            color: #667eea;
            font-weight: 600;
        }
        
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 0.5rem;
        }
        
        .status-active { background-color: #28a745; }
        .status-pending { background-color: #ffc107; }
        .status-rejected { background-color: #dc3545; }
        .status-blacklisted { background-color: #343a40; }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">تفاصيل العميل</h2>
                        <p class="text-muted">عرض شامل لبيانات العميل ونشاطاته</p>
                    </div>
                    <div>
                        <?php if ($auth->hasPermission('customers.edit')): ?>
                        <a href="edit_customer.php?id=<?php echo $customer['id']; ?>" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-2"></i>
                            تعديل البيانات
                        </a>
                        <?php endif; ?>
                        <a href="customers.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة لقائمة العملاء
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- معلومات العميل الأساسية -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            المعلومات الأساسية
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="customer-avatar mx-auto mb-3">
                            <?php echo strtoupper(substr($customer['full_name'], 0, 1)); ?>
                        </div>
                        <h4 class="mb-1"><?php echo htmlspecialchars($customer['full_name']); ?></h4>
                        <p class="text-muted mb-3"><?php echo htmlspecialchars($customer['id_number']); ?></p>
                        
                        <div class="row text-center">
                            <div class="col-4">
                                <span class="badge bg-<?php echo getKYCStatusClass($customer['kyc_status']); ?> w-100">
                                    <?php echo getKYCStatusText($customer['kyc_status']); ?>
                                </span>
                                <small class="d-block text-muted mt-1">حالة KYC</small>
                            </div>
                            <div class="col-4">
                                <span class="badge bg-<?php echo getRiskLevelClass($customer['risk_level']); ?> w-100">
                                    <?php echo getRiskLevelText($customer['risk_level']); ?>
                                </span>
                                <small class="d-block text-muted mt-1">مستوى المخاطر</small>
                            </div>
                            <div class="col-4">
                                <?php if ($customer['blacklisted']): ?>
                                    <span class="badge bg-dark w-100">قائمة سوداء</span>
                                <?php else: ?>
                                    <span class="badge bg-success w-100">نشط</span>
                                <?php endif; ?>
                                <small class="d-block text-muted mt-1">الحالة</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الاتصال -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-address-book me-2"></i>
                            معلومات الاتصال
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="info-item">
                            <div class="info-label">الهاتف</div>
                            <div class="info-value">
                                <i class="fas fa-phone me-2 text-muted"></i>
                                <?php echo htmlspecialchars($customer['phone'] ?? 'غير محدد'); ?>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">البريد الإلكتروني</div>
                            <div class="info-value">
                                <i class="fas fa-envelope me-2 text-muted"></i>
                                <?php echo htmlspecialchars($customer['email'] ?? 'غير محدد'); ?>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">العنوان</div>
                            <div class="info-value">
                                <i class="fas fa-map-marker-alt me-2 text-muted"></i>
                                <?php echo htmlspecialchars($customer['address'] ?? 'غير محدد'); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات إضافية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="info-item">
                            <div class="info-label">تاريخ الميلاد</div>
                            <div class="info-value"><?php echo $customer['date_of_birth'] ? date('Y-m-d', strtotime($customer['date_of_birth'])) : 'غير محدد'; ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">الجنسية</div>
                            <div class="info-value"><?php echo htmlspecialchars($customer['nationality'] ?? 'غير محدد'); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">نوع الهوية</div>
                            <div class="info-value"><?php echo htmlspecialchars($customer['id_type']); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">تاريخ التسجيل</div>
                            <div class="info-value"><?php echo date('Y-m-d', strtotime($customer['registration_date'])); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">مسجل بواسطة</div>
                            <div class="info-value"><?php echo htmlspecialchars($customer['registered_by_name']); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التفاصيل المتقدمة -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        <!-- Navigation Tabs -->
                        <ul class="nav nav-tabs" id="customerTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="compliance-tab" data-bs-toggle="tab" data-bs-target="#compliance" type="button" role="tab">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    فحوصات الامتثال
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab">
                                    <i class="fas fa-file-alt me-2"></i>
                                    المستندات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="transactions-tab" data-bs-toggle="tab" data-bs-target="#transactions" type="button" role="tab">
                                    <i class="fas fa-exchange-alt me-2"></i>
                                    المعاملات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">
                                    <i class="fas fa-history me-2"></i>
                                    تاريخ التحديثات
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content mt-4" id="customerTabContent">
                            <!-- فحوصات الامتثال -->
                            <div class="tab-pane fade show active" id="compliance" role="tabpanel">
                                <?php if (empty($compliance_checks)): ?>
                                    <div class="text-center p-4">
                                        <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">لا توجد فحوصات امتثال</h6>
                                        <p class="text-muted">لم يتم إجراء أي فحوصات امتثال لهذا العميل بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="timeline">
                                        <?php foreach ($compliance_checks as $check): ?>
                                        <div class="timeline-item">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1"><?php echo getCheckTypeText($check['check_type']); ?></h6>
                                                    <span class="badge bg-<?php echo getCheckResultClass($check['check_result']); ?> mb-2">
                                                        <?php echo getCheckResultText($check['check_result']); ?>
                                                    </span>
                                                    <?php if ($check['risk_score'] > 0): ?>
                                                        <span class="badge bg-info ms-2">نقاط المخاطر: <?php echo $check['risk_score']; ?></span>
                                                    <?php endif; ?>
                                                    <p class="text-muted mb-1"><?php echo htmlspecialchars($check['notes'] ?? ''); ?></p>
                                                    <small class="text-muted">
                                                        بواسطة: <?php echo htmlspecialchars($check['performed_by_name']); ?> | 
                                                        <?php echo date('Y-m-d H:i', strtotime($check['check_date'])); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- المستندات -->
                            <div class="tab-pane fade" id="documents" role="tabpanel">
                                <?php if (empty($documents)): ?>
                                    <div class="text-center p-4">
                                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">لا توجد مستندات</h6>
                                        <p class="text-muted">لم يتم رفع أي مستندات لهذا العميل بعد</p>
                                        <?php if ($auth->hasPermission('customers.edit')): ?>
                                        <button class="btn btn-primary">
                                            <i class="fas fa-upload me-2"></i>
                                            رفع مستند
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>نوع المستند</th>
                                                    <th>اسم الملف</th>
                                                    <th>الحالة</th>
                                                    <th>تاريخ الرفع</th>
                                                    <th>رفع بواسطة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($documents as $doc): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($doc['document_type']); ?></td>
                                                    <td><?php echo htmlspecialchars($doc['file_name']); ?></td>
                                                    <td>
                                                        <?php if ($doc['is_verified']): ?>
                                                            <span class="badge bg-success">تم التحقق</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-warning">قيد المراجعة</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo date('Y-m-d', strtotime($doc['upload_date'])); ?></td>
                                                    <td><?php echo htmlspecialchars($doc['uploaded_by_name']); ?></td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <?php if ($auth->hasPermission('customers.kyc') && !$doc['is_verified']): ?>
                                                        <button class="btn btn-sm btn-outline-success">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- المعاملات -->
                            <div class="tab-pane fade" id="transactions" role="tabpanel">
                                <?php if (empty($recent_transactions)): ?>
                                    <div class="text-center p-4">
                                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">لا توجد معاملات</h6>
                                        <p class="text-muted">لم يقم هذا العميل بأي معاملات بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>رقم المرجع</th>
                                                    <th>نوع المعاملة</th>
                                                    <th>المبلغ</th>
                                                    <th>التاريخ</th>
                                                    <th>الحالة</th>
                                                    <th>الموظف</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recent_transactions as $transaction): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($transaction['reference_number']); ?></td>
                                                    <td><?php echo htmlspecialchars($transaction['transaction_type']); ?></td>
                                                    <td>$<?php echo number_format($transaction['total_amount_base_currency'], 2); ?></td>
                                                    <td><?php echo date('Y-m-d', strtotime($transaction['transaction_date'])); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $transaction['status'] === 'completed' ? 'success' : 'warning'; ?>">
                                                            <?php echo $transaction['status']; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($transaction['user_name']); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center">
                                        <a href="transactions.php?customer_id=<?php echo $customer['id']; ?>" class="btn btn-outline-primary">
                                            عرض جميع المعاملات
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- تاريخ التحديثات -->
                            <div class="tab-pane fade" id="history" role="tabpanel">
                                <?php if (empty($customer_history)): ?>
                                    <div class="text-center p-4">
                                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                        <h6 class="text-muted">لا يوجد تاريخ تحديثات</h6>
                                        <p class="text-muted">لم يتم تحديث بيانات هذا العميل بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="timeline">
                                        <?php foreach ($customer_history as $history): ?>
                                        <div class="timeline-item">
                                            <div>
                                                <h6 class="mb-1">تحديث <?php echo htmlspecialchars($history['field_name']); ?></h6>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <small class="text-muted">القيمة السابقة:</small>
                                                        <div class="bg-light p-2 rounded"><?php echo htmlspecialchars($history['old_value'] ?? 'فارغ'); ?></div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <small class="text-muted">القيمة الجديدة:</small>
                                                        <div class="bg-light p-2 rounded"><?php echo htmlspecialchars($history['new_value'] ?? 'فارغ'); ?></div>
                                                    </div>
                                                </div>
                                                <?php if ($history['change_reason']): ?>
                                                    <p class="text-muted mt-2 mb-1"><?php echo htmlspecialchars($history['change_reason']); ?></p>
                                                <?php endif; ?>
                                                <small class="text-muted">
                                                    بواسطة: <?php echo htmlspecialchars($history['changed_by_name']); ?> | 
                                                    <?php echo date('Y-m-d H:i', strtotime($history['changed_at'])); ?>
                                                </small>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
