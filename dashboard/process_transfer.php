<?php
require_once '../includes/auth.php';
require_once '../includes/transfer_manager.php';

$auth = new Auth();
$transferManager = new TransferManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('transfers.create')) {
    header('Location: transfers.php?error=ليس لديك صلاحية لإنشاء تحويلات مالية');
    exit();
}

$current_user = $auth->getCurrentUser();

// التحقق من أن الطلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: transfers.php?error=طريقة طلب غير صحيحة');
    exit();
}

// جمع البيانات من النموذج
$data = [
    'sender_customer_id' => intval($_POST['sender_customer_id'] ?? 0),
    'recipient_name' => trim($_POST['recipient_name'] ?? ''),
    'recipient_account_details' => trim($_POST['recipient_account_details'] ?? ''),
    'recipient_bank' => trim($_POST['recipient_bank'] ?? ''),
    'recipient_country' => trim($_POST['recipient_country'] ?? ''),
    'sending_currency_id' => intval($_POST['sending_currency_id'] ?? 0),
    'receiving_currency_id' => intval($_POST['receiving_currency_id'] ?? 0),
    'sending_amount' => floatval($_POST['sending_amount'] ?? 0),
    'transfer_method' => trim($_POST['transfer_method'] ?? ''),
    'branch_id' => intval($_POST['branch_id'] ?? $current_user['branch_id'])
];

// التحقق من صحة البيانات الأساسية
if (empty($data['sender_customer_id']) || empty($data['recipient_name']) || 
    empty($data['recipient_country']) || empty($data['sending_currency_id']) || 
    empty($data['receiving_currency_id']) || empty($data['sending_amount']) ||
    empty($data['transfer_method'])) {
    header('Location: transfers.php?error=بيانات غير مكتملة');
    exit();
}

// معالجة التحويل المالي
$result = $transferManager->createTransfer($data, $current_user['id']);

if ($result['success']) {
    // إعادة توجيه إلى صفحة الإيصال
    header('Location: transfer_receipt.php?id=' . $result['transfer_id'] . '&success=' . urlencode($result['message']));
} else {
    // إعادة توجيه مع رسالة خطأ
    header('Location: transfers.php?error=' . urlencode($result['message']));
}
exit();
?>
