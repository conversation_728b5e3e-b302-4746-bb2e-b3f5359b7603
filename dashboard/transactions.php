<?php
require_once '../includes/auth.php';

$auth = new Auth();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('transactions.view')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى سجل المعاملات');
    exit();
}

$current_user = $auth->getCurrentUser();

// معالجة الفلاتر
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$branch_id = $_GET['branch_id'] ?? '';
$type_filter = $_GET['transaction_type'] ?? '';

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'سجل المعاملات المالية - ' . SYSTEM_NAME;
$page_header = 'سجل المعاملات المالية';
$page_subtitle = 'عرض جميع المعاملات المالية (صرافة، تحويلات، ...الخ)';
$page_icon = 'fas fa-list-alt';
$show_breadcrumb = true;

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب الفروع
    $stmt = $db->prepare("SELECT * FROM branches WHERE is_active = 1 ORDER BY name");
    $stmt->execute();
    $branches = $stmt->fetchAll();
    
    // بناء شروط الفلترة
    $where = ["DATE(transaction_date) BETWEEN :date_from AND :date_to"];
    $params = [':date_from' => $date_from, ':date_to' => $date_to];
    if ($branch_id) {
        $where[] = 'branch_id = :branch_id';
        $params[':branch_id'] = $branch_id;
    }
    if ($type_filter) {
        $where[] = 'transaction_type = :type';
        $params[':type'] = $type_filter;
    }
    $where_clause = 'WHERE ' . implode(' AND ', $where);
    
    // جلب المعاملات
    $stmt = $db->prepare("
        SELECT t.*, b.name as branch_name
        FROM transactions t
        JOIN branches b ON t.branch_id = b.id
        $where_clause
        ORDER BY t.transaction_date DESC, t.id DESC
        LIMIT 200
    ");
    foreach ($params as $k => $v) $stmt->bindValue($k, $v);
    $stmt->execute();
    $transactions = $stmt->fetchAll();
} catch (Exception $e) {
    $branches = [];
    $transactions = [];
}

include '../includes/header.php';
?>
<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0">سجل المعاملات المالية</h2>
                    <p class="text-muted">عرض جميع المعاملات المالية (صرافة، تحويلات، ...الخ)</p>
                </div>
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
    </div>
    <!-- فلاتر -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" name="date_from" value="<?php echo $date_from; ?>">
                    </div>
                    <div class="col-md-3 mb-2">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" name="date_to" value="<?php echo $date_to; ?>">
                    </div>
                    <div class="col-md-3 mb-2">
                        <label class="form-label">الفرع</label>
                        <select class="form-control" name="branch_id">
                            <option value="">جميع الفروع</option>
                            <?php foreach ($branches as $branch): ?>
                                <option value="<?php echo $branch['id']; ?>" <?php echo $branch_id == $branch['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($branch['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label class="form-label">نوع المعاملة</label>
                        <select class="form-control" name="transaction_type">
                            <option value="">الكل</option>
                            <option value="exchange" <?php echo $type_filter === 'exchange' ? 'selected' : ''; ?>>صرافة</option>
                            <option value="transfer" <?php echo $type_filter === 'transfer' ? 'selected' : ''; ?>>تحويل</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            تطبيق الفلاتر
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- جدول المعاملات -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list-alt me-2"></i>
                جميع المعاملات (<?php echo count($transactions); ?>)
            </h5>
        </div>
        <div class="card-body p-0">
            <?php if (empty($transactions)): ?>
                <div class="text-center p-5">
                    <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد معاملات</h5>
                    <p class="text-muted">لا توجد معاملات في الفترة أو الفلاتر المحددة</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>رقم</th>
                                <th>التاريخ</th>
                                <th>الفرع</th>
                                <th>النوع</th>
                                <th>المبلغ الأساسي</th>
                                <th>العملة</th>
                                <th>العميل</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($transactions as $tx): ?>
                            <tr>
                                <td><?php echo $tx['id']; ?></td>
                                <td><?php echo date('Y-m-d H:i', strtotime($tx['transaction_date'])); ?></td>
                                <td><?php echo htmlspecialchars($tx['branch_name']); ?></td>
                                <td>
                                    <?php if ($tx['transaction_type'] === 'exchange'): ?>
                                        <span class="badge bg-info">صرافة</span>
                                    <?php elseif ($tx['transaction_type'] === 'transfer'): ?>
                                        <span class="badge bg-success">تحويل</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">أخرى</span>
                                    <?php endif; ?>
                                </td>
                                <td>$<?php echo number_format($tx['total_amount_base_currency'], 2); ?></td>
                                <td><?php echo htmlspecialchars($tx['base_currency_code'] ?? ''); ?></td>
                                <td><?php echo htmlspecialchars($tx['customer_name'] ?? '-'); ?></td>
                                <td>
                                    <?php if ($tx['status'] === 'completed'): ?>
                                        <span class="badge bg-success">مكتملة</span>
                                    <?php elseif ($tx['status'] === 'pending'): ?>
                                        <span class="badge bg-warning">معلقة</span>
                                    <?php elseif ($tx['status'] === 'cancelled'): ?>
                                        <span class="badge bg-danger">ملغاة</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php include '../includes/footer.php'; ?> 