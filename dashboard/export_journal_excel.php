<?php
require_once '../includes/auth.php';

$auth = new Auth();
if (!$auth->checkSession() || !$auth->hasPermission('accounting.view')) {
    die('Unauthorized');
}

$as_of_date = $_GET['as_of_date'] ?? date('Y-m-d');
$branch_id = $_GET['branch_id'] ?? '';

require_once '../includes/reports_manager.php';
$reportsManager = new ReportsManager();

try {
    $database = new Database();
    $db = $database->getConnection();
    // جلب الفروع
    $stmt = $db->prepare("SELECT * FROM branches WHERE is_active = 1 ORDER BY name");
    $stmt->execute();
    $branches = $stmt->fetchAll(PDO::FETCH_UNIQUE|PDO::FETCH_ASSOC);
    // جلب جميع القيود اليومية حسب الفلاتر
    $where = ["DATE(transaction_date) <= :as_of_date", "transaction_type = 'manual'"];
    $params = [':as_of_date' => $as_of_date];
    if ($branch_id) {
        $where[] = 'branch_id = :branch_id';
        $params[':branch_id'] = $branch_id;
    }
    $where_clause = 'WHERE ' . implode(' AND ', $where);
    $stmt = $db->prepare("
        SELECT t.*, u.full_name as created_by_name
        FROM transactions t
        LEFT JOIN users u ON t.created_by = u.id
        $where_clause
        ORDER BY t.transaction_date DESC, t.id DESC
    ");
    foreach ($params as $k => $v) $stmt->bindValue($k, $v);
    $stmt->execute();
    $journals = $stmt->fetchAll();
    // جلب تفاصيل القيود
    $journal_ids = array_column($journals, 'id');
    $details = [];
    if ($journal_ids) {
        $in = implode(',', array_fill(0, count($journal_ids), '?'));
        $stmt = $db->prepare("SELECT td.*, a.account_code, a.account_name FROM transaction_details td JOIN accounts a ON td.account_id = a.id WHERE td.transaction_id IN ($in)");
        $stmt->execute($journal_ids);
        foreach ($stmt->fetchAll() as $row) {
            $details[$row['transaction_id']][] = $row;
        }
    }
} catch (Exception $e) {
    die('Error: ' . $e->getMessage());
}

// محاولة استخدام PhpSpreadsheet إذا كانت متوفرة
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once __DIR__ . '/../vendor/autoload.php';
    use PhpOffice\PhpSpreadsheet\Spreadsheet;
    use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Journal');
    $sheet->fromArray([
        ['رقم القيد','التاريخ','الفرع','الوصف','الحساب','اسم الحساب','مدين','دائن','المستخدم'],
    ], null, 'A1');
    $rowNum = 2;
    foreach ($journals as $j) {
        $branch_name = $branches[$j['branch_id']]['name'] ?? '';
        $user = $j['created_by_name'] ?? '';
        if (!empty($details[$j['id']])) {
            foreach ($details[$j['id']] as $d) {
                $sheet->fromArray([
                    $j['id'],
                    $j['transaction_date'],
                    $branch_name,
                    $j['description'],
                    $d['account_code'],
                    $d['account_name'],
                    $d['debit'],
                    $d['credit'],
                    $user
                ], null, 'A'.$rowNum);
                $rowNum++;
            }
        } else {
            $sheet->fromArray([
                $j['id'], $j['transaction_date'], $branch_name, $j['description'], '', '', '', '', $user
            ], null, 'A'.$rowNum);
            $rowNum++;
        }
    }
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="journal_export_'.date('Ymd').'.xlsx"');
    header('Cache-Control: max-age=0');
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
}
// إذا لم تتوفر PhpSpreadsheet، يصدر CSV بسيط
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment;filename="journal_export_'.date('Ymd').'.csv"');
echo "\xEF\xBB\xBF"; // BOM for UTF-8 Excel
$out = fopen('php://output', 'w');
fputcsv($out, ['رقم القيد','التاريخ','الفرع','الوصف','الحساب','اسم الحساب','مدين','دائن','المستخدم']);
foreach ($journals as $j) {
    $branch_name = $branches[$j['branch_id']]['name'] ?? '';
    $user = $j['created_by_name'] ?? '';
    if (!empty($details[$j['id']])) {
        foreach ($details[$j['id']] as $d) {
            fputcsv($out, [
                $j['id'],
                $j['transaction_date'],
                $branch_name,
                $j['description'],
                $d['account_code'],
                $d['account_name'],
                $d['debit'],
                $d['credit'],
                $user
            ]);
        }
    } else {
        fputcsv($out, [$j['id'], $j['transaction_date'], $branch_name, $j['description'], '', '', '', '', $user]);
    }
}
fclose($out);
exit; 