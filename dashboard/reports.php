<?php
require_once '../includes/auth.php';
require_once '../includes/reports_manager.php';

$auth = new Auth();
$reportsManager = new ReportsManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('reports.financial')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى التقارير المالية');
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'التقارير المالية والمحاسبية - ' . SYSTEM_NAME;
$page_header = 'التقارير المالية والمحاسبية';
$page_subtitle = 'تقارير شاملة للأداء المالي والمحاسبي';
$page_icon = 'fas fa-chart-bar';
$show_breadcrumb = true;

// جلب البيانات الأساسية
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب الفروع
    $stmt = $db->prepare("SELECT * FROM branches WHERE is_active = 1 ORDER BY name");
    $stmt->execute();
    $branches = $stmt->fetchAll();
    
    // جلب التقارير المحفوظة الأخيرة
    $saved_reports = $reportsManager->getSavedReports(null, null, 10);
    
    // إحصائيات سريعة
    $today = date('Y-m-d');
    $month_start = date('Y-m-01');
    
    // إجمالي المعاملات اليوم
    $stmt = $db->prepare("
        SELECT COUNT(*) as today_transactions, SUM(total_amount_base_currency) as today_volume
        FROM transactions 
        WHERE DATE(transaction_date) = :today
    ");
    $stmt->bindParam(':today', $today);
    $stmt->execute();
    $today_stats = $stmt->fetch();
    
    // أرباح الشهر الحالي
    $stmt = $db->prepare("
        SELECT 
            SUM(dep.total_profit) as exchange_profits,
            SUM(dtp.total_profit) as transfer_profits
        FROM daily_exchange_profits dep
        LEFT JOIN daily_transfer_profits dtp ON dep.profit_date = dtp.profit_date
        WHERE dep.profit_date >= :month_start
    ");
    $stmt->bindParam(':month_start', $month_start);
    $stmt->execute();
    $month_profits = $stmt->fetch();
    
} catch (Exception $e) {
    $branches = [];
    $saved_reports = [];
    $today_stats = ['today_transactions' => 0, 'today_volume' => 0];
    $month_profits = ['exchange_profits' => 0, 'transfer_profits' => 0];
}
<?php include '../includes/header.php'; ?>
<!-- محتوى التقارير -->
<div class="reports-container">
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">التقارير المالية والمحاسبية</h2>
                        <p class="text-muted">تقارير شاملة للأداء المالي والمحاسبي</p>
                    </div>
                    <div>
                        <button class="btn btn-success me-2" onclick="exportAllReports()">
                            <i class="fas fa-download me-2"></i>
                            تصدير جميع التقارير
                        </button>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number"><?php echo number_format($today_stats['today_transactions']); ?></div>
                    <div class="text-muted">معاملات اليوم</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number">$<?php echo number_format($today_stats['today_volume'], 2); ?></div>
                    <div class="text-muted">حجم المعاملات اليوم</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number">$<?php echo number_format($month_profits['exchange_profits'] + $month_profits['transfer_profits'], 2); ?></div>
                    <div class="text-muted">أرباح الشهر الحالي</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number"><?php echo count($saved_reports); ?></div>
                    <div class="text-muted">التقارير المحفوظة</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- التقارير المالية -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير المالية والمحاسبية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- تقرير الأرباح والخسائر -->
                            <div class="col-md-6">
                                <div class="report-card" onclick="openReportModal('profit_loss')">
                                    <div class="report-icon bg-success-gradient mx-auto">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">تقرير الأرباح والخسائر</h6>
                                        <p class="text-muted small">عرض الإيرادات والمصروفات والأرباح الصافية</p>
                                    </div>
                                </div>
                            </div>

                            <!-- الميزانية العمومية -->
                            <div class="col-md-6">
                                <div class="report-card" onclick="openReportModal('balance_sheet')">
                                    <div class="report-icon bg-primary-gradient mx-auto">
                                        <i class="fas fa-balance-scale"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">الميزانية العمومية</h6>
                                        <p class="text-muted small">الأصول والخصوم وحقوق الملكية</p>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير التدفق النقدي -->
                            <div class="col-md-6">
                                <div class="report-card" onclick="openReportModal('cash_flow')">
                                    <div class="report-icon bg-info-gradient mx-auto">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">تقرير التدفق النقدي</h6>
                                        <p class="text-muted small">التدفقات النقدية الداخلة والخارجة</p>
                                    </div>
                                </div>
                            </div>

                            <!-- ميزان المراجعة -->
                            <div class="col-md-6">
                                <div class="report-card" onclick="openReportModal('trial_balance')">
                                    <div class="report-icon bg-warning-gradient mx-auto">
                                        <i class="fas fa-calculator"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">ميزان المراجعة</h6>
                                        <p class="text-muted small">أرصدة جميع الحسابات</p>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير ملخص العمليات -->
                            <div class="col-md-6">
                                <div class="report-card" onclick="openReportModal('operations_summary')">
                                    <div class="report-icon bg-secondary-gradient mx-auto">
                                        <i class="fas fa-chart-pie"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">ملخص العمليات</h6>
                                        <p class="text-muted small">إحصائيات شاملة للعمليات</p>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير الامتثال -->
                            <div class="col-md-6">
                                <div class="report-card" onclick="openReportModal('compliance_report')">
                                    <div class="report-icon bg-danger-gradient mx-auto">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">تقرير الامتثال</h6>
                                        <p class="text-muted small">فحوصات KYC وAML والمخاطر</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-4">
                <!-- التقارير المحفوظة -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-save me-2"></i>
                            التقارير المحفوظة
                        </h5>
                        <button class="btn btn-sm btn-light" onclick="refreshSavedReports()">
                            <i class="fas fa-refresh"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <?php if (empty($saved_reports)): ?>
                            <div class="text-center p-4">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد تقارير محفوظة</h6>
                                <p class="text-muted">قم بإنشاء تقرير جديد لحفظه</p>
                            </div>
                        <?php else: ?>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <?php foreach ($saved_reports as $report): ?>
                                <div class="saved-report-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($report['report_name']); ?></h6>
                                            <div class="mb-2">
                                                <span class="report-type-badge bg-primary text-white">
                                                    <?php 
                                                    $report_types = [
                                                        'profit_loss' => 'أرباح وخسائر',
                                                        'balance_sheet' => 'ميزانية عمومية',
                                                        'cash_flow' => 'تدفق نقدي',
                                                        'trial_balance' => 'ميزان مراجعة',
                                                        'operations_summary' => 'ملخص عمليات',
                                                        'compliance_report' => 'تقرير امتثال'
                                                    ];
                                                    echo $report_types[$report['report_type']] ?? $report['report_type'];
                                                    ?>
                                                </span>
                                            </div>
                                            <div class="text-muted small">
                                                بواسطة: <?php echo htmlspecialchars($report['generated_by_name']); ?>
                                            </div>
                                            <div class="text-muted small">
                                                <?php echo date('Y-m-d H:i', strtotime($report['generated_at'])); ?>
                                            </div>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="viewReport(<?php echo $report['id']; ?>)">
                                                    <i class="fas fa-eye me-2"></i>عرض
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="downloadReport(<?php echo $report['id']; ?>)">
                                                    <i class="fas fa-download me-2"></i>تحميل
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteReport(<?php echo $report['id']; ?>)">
                                                    <i class="fas fa-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- إعدادات سريعة -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label small">الفترة الافتراضية</label>
                            <select class="form-control form-control-sm" id="defaultPeriod">
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month" selected>هذا الشهر</option>
                                <option value="quarter">هذا الربع</option>
                                <option value="year">هذا العام</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label small">الفرع الافتراضي</label>
                            <select class="form-control form-control-sm" id="defaultBranch">
                                <option value="">جميع الفروع</option>
                                <?php foreach ($branches as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>" 
                                            <?php echo $branch['id'] == $current_user['branch_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($branch['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="autoSave" checked>
                            <label class="form-check-label small" for="autoSave">
                                حفظ التقارير تلقائياً
                            </label>
                        </div>
                        
                        <button class="btn btn-primary btn-sm w-100" onclick="saveSettings()">
                            <i class="fas fa-save me-2"></i>
                            حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة لإعدادات التقرير -->
    <div class="modal fade" id="reportModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reportModalTitle">إنشاء تقرير</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="reportForm">
                        <input type="hidden" id="reportType" name="report_type">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="dateFrom" name="date_from" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="dateTo" name="date_to" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">الفرع</label>
                            <select class="form-control" id="branchId" name="branch_id">
                                <option value="">جميع الفروع</option>
                                <?php foreach ($branches as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>">
                                        <?php echo htmlspecialchars($branch['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">اسم التقرير (اختياري)</label>
                            <input type="text" class="form-control" id="reportName" name="report_name"
                                   placeholder="سيتم إنشاء اسم تلقائي إذا ترك فارغاً">
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="saveReport" name="save_report" checked>
                            <label class="form-check-label" for="saveReport">
                                حفظ التقرير في قاعدة البيانات
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-chart-bar me-2"></i>
                        إنشاء التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة منبثقة لعرض التقرير -->
    <div class="modal fade" id="reportViewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reportViewTitle">عرض التقرير</h5>
                    <div>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="exportReport()">
                            <i class="fas fa-download me-1"></i>
                            تصدير
                        </button>
                        <button type="button" class="btn btn-info btn-sm me-2" onclick="printReport()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <div id="reportContent">
                        <!-- سيتم ملء محتوى التقرير هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // متغيرات عامة
        let currentReportType = '';
        let currentReportData = null;

        // فتح نافذة إعدادات التقرير
        function openReportModal(reportType) {
            currentReportType = reportType;

            const reportTitles = {
                'profit_loss': 'تقرير الأرباح والخسائر',
                'balance_sheet': 'الميزانية العمومية',
                'cash_flow': 'تقرير التدفق النقدي',
                'trial_balance': 'ميزان المراجعة',
                'operations_summary': 'ملخص العمليات',
                'compliance_report': 'تقرير الامتثال'
            };

            document.getElementById('reportModalTitle').textContent = reportTitles[reportType] || 'إنشاء تقرير';
            document.getElementById('reportType').value = reportType;

            // تعيين التواريخ الافتراضية
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

            document.getElementById('dateFrom').value = firstDayOfMonth.toISOString().split('T')[0];
            document.getElementById('dateTo').value = today.toISOString().split('T')[0];

            // تعيين الفرع الافتراضي
            const defaultBranch = document.getElementById('defaultBranch').value;
            document.getElementById('branchId').value = defaultBranch;

            // إظهار النافذة
            new bootstrap.Modal(document.getElementById('reportModal')).show();
        }

        // إنشاء التقرير
        function generateReport() {
            const formData = new FormData(document.getElementById('reportForm'));
            const reportType = formData.get('report_type');

            // إظهار مؤشر التحميل
            const generateBtn = document.querySelector('#reportModal .btn-primary');
            const originalText = generateBtn.innerHTML;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
            generateBtn.disabled = true;

            fetch('generate_report.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentReportData = data.data;
                    displayReport(reportType, data.data);

                    // إخفاء نافذة الإعدادات وإظهار نافذة العرض
                    bootstrap.Modal.getInstance(document.getElementById('reportModal')).hide();
                    new bootstrap.Modal(document.getElementById('reportViewModal')).show();
                } else {
                    alert('خطأ في إنشاء التقرير: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('خطأ في الاتصال بالخادم');
            })
            .finally(() => {
                generateBtn.innerHTML = originalText;
                generateBtn.disabled = false;
            });
        }

        // عرض التقرير
        function displayReport(reportType, data) {
            const reportTitles = {
                'profit_loss': 'تقرير الأرباح والخسائر',
                'balance_sheet': 'الميزانية العمومية',
                'cash_flow': 'تقرير التدفق النقدي',
                'trial_balance': 'ميزان المراجعة',
                'operations_summary': 'ملخص العمليات',
                'compliance_report': 'تقرير الامتثال'
            };

            document.getElementById('reportViewTitle').textContent = reportTitles[reportType] || 'عرض التقرير';

            let content = '';

            switch (reportType) {
                case 'profit_loss':
                    content = generateProfitLossHTML(data);
                    break;
                case 'balance_sheet':
                    content = generateBalanceSheetHTML(data);
                    break;
                case 'cash_flow':
                    content = generateCashFlowHTML(data);
                    break;
                case 'trial_balance':
                    content = generateTrialBalanceHTML(data);
                    break;
                case 'operations_summary':
                    content = generateOperationsSummaryHTML(data);
                    break;
                default:
                    content = '<div class="alert alert-warning">نوع التقرير غير مدعوم</div>';
            }

            document.getElementById('reportContent').innerHTML = content;
        }

        // إنشاء HTML لتقرير الأرباح والخسائر
        function generateProfitLossHTML(data) {
            return `
                <div class="report-header text-center mb-4">
                    <h3>تقرير الأرباح والخسائر</h3>
                    <p class="text-muted">من ${data.period.from} إلى ${data.period.to}</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h5 class="text-success">الإيرادات</h5>
                        <table class="table table-sm">
                            <tr><td>عمولات الصرافة</td><td class="text-end">$${data.revenue.exchange_commission.toFixed(2)}</td></tr>
                            <tr><td>أرباح فوارق الصرف</td><td class="text-end">$${data.revenue.exchange_spread.toFixed(2)}</td></tr>
                            <tr><td>رسوم التحويلات</td><td class="text-end">$${data.revenue.transfer_fees.toFixed(2)}</td></tr>
                            <tr><td>أرباح صرف التحويلات</td><td class="text-end">$${data.revenue.transfer_exchange_profit.toFixed(2)}</td></tr>
                            <tr><td>إيرادات أخرى</td><td class="text-end">$${data.revenue.other_revenue.toFixed(2)}</td></tr>
                            <tr class="table-success"><th>إجمالي الإيرادات</th><th class="text-end">$${data.total_revenue.toFixed(2)}</th></tr>
                        </table>
                    </div>

                    <div class="col-md-6">
                        <h5 class="text-danger">المصروفات</h5>
                        <table class="table table-sm">
                            <tr><td>مصروفات تشغيلية</td><td class="text-end">$${data.expenses.operational_expenses.toFixed(2)}</td></tr>
                            <tr><td>مصروفات إدارية</td><td class="text-end">$${data.expenses.administrative_expenses.toFixed(2)}</td></tr>
                            <tr><td>مصروفات أخرى</td><td class="text-end">$${data.expenses.other_expenses.toFixed(2)}</td></tr>
                            <tr class="table-danger"><th>إجمالي المصروفات</th><th class="text-end">$${data.total_expenses.toFixed(2)}</th></tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4>الربح الإجمالي: <span class="text-success">$${data.gross_profit.toFixed(2)}</span></h4>
                                <h4>الربح الصافي: <span class="text-primary">$${data.net_profit.toFixed(2)}</span></h4>
                                <p>هامش الربح: ${data.profit_margin.toFixed(2)}%</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // إنشاء HTML للميزانية العمومية
        function generateBalanceSheetHTML(data) {
            let assetsHTML = data.assets.map(asset =>
                `<tr><td>${asset.account_code}</td><td>${asset.account_name}</td><td class="text-end">$${asset.balance.toFixed(2)}</td></tr>`
            ).join('');

            let liabilitiesHTML = data.liabilities.map(liability =>
                `<tr><td>${liability.account_code}</td><td>${liability.account_name}</td><td class="text-end">$${liability.balance.toFixed(2)}</td></tr>`
            ).join('');

            let equityHTML = data.equity.map(equity =>
                `<tr><td>${equity.account_code}</td><td>${equity.account_name}</td><td class="text-end">$${equity.balance.toFixed(2)}</td></tr>`
            ).join('');

            return `
                <div class="report-header text-center mb-4">
                    <h3>الميزانية العمومية</h3>
                    <p class="text-muted">كما في ${data.as_of_date}</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h5 class="text-primary">الأصول</h5>
                        <table class="table table-sm">
                            <thead><tr><th>الرمز</th><th>اسم الحساب</th><th class="text-end">الرصيد</th></tr></thead>
                            <tbody>
                                ${assetsHTML}
                                <tr class="table-primary"><th colspan="2">إجمالي الأصول</th><th class="text-end">$${data.totals.assets.toFixed(2)}</th></tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="col-md-6">
                        <h5 class="text-warning">الخصوم</h5>
                        <table class="table table-sm">
                            <thead><tr><th>الرمز</th><th>اسم الحساب</th><th class="text-end">الرصيد</th></tr></thead>
                            <tbody>
                                ${liabilitiesHTML}
                                <tr class="table-warning"><th colspan="2">إجمالي الخصوم</th><th class="text-end">$${data.totals.liabilities.toFixed(2)}</th></tr>
                            </tbody>
                        </table>

                        <h5 class="text-success mt-3">حقوق الملكية</h5>
                        <table class="table table-sm">
                            <thead><tr><th>الرمز</th><th>اسم الحساب</th><th class="text-end">الرصيد</th></tr></thead>
                            <tbody>
                                ${equityHTML}
                                <tr class="table-success"><th colspan="2">إجمالي حقوق الملكية</th><th class="text-end">$${data.totals.equity.toFixed(2)}</th></tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="alert ${data.balanced ? 'alert-success' : 'alert-danger'} mt-3">
                    <strong>إجمالي الخصوم وحقوق الملكية: $${data.total_liabilities_equity.toFixed(2)}</strong>
                    <br>
                    ${data.balanced ? 'الميزانية متوازنة ✓' : 'الميزانية غير متوازنة ✗'}
                </div>
            `;
        }

        // دوال أخرى
        function exportReport() {
            if (currentReportData) {
                alert('ميزة التصدير قيد التطوير');
            }
        }

        function printReport() {
            window.print();
        }

        function exportAllReports() {
            alert('ميزة تصدير جميع التقارير قيد التطوير');
        }

        function refreshSavedReports() {
            location.reload();
        }

        function viewReport(reportId) {
            alert('ميزة عرض التقرير المحفوظ قيد التطوير');
        }

        function downloadReport(reportId) {
            alert('ميزة تحميل التقرير قيد التطوير');
        }

        function deleteReport(reportId) {
            if (confirm('هل أنت متأكد من حذف هذا التقرير؟')) {
                alert('ميزة حذف التقرير قيد التطوير');
            }
        }

        function saveSettings() {
            alert('تم حفظ الإعدادات');
        }

        // إنشاء HTML لميزان المراجعة
        function generateTrialBalanceHTML(data) {
            let accountsHTML = data.accounts.map(account =>
                `<tr>
                    <td>${account.account_code}</td>
                    <td>${account.account_name}</td>
                    <td class="text-end">$${account.total_debits.toFixed(2)}</td>
                    <td class="text-end">$${account.total_credits.toFixed(2)}</td>
                    <td class="text-end">$${account.debit_balance.toFixed(2)}</td>
                    <td class="text-end">$${account.credit_balance.toFixed(2)}</td>
                </tr>`
            ).join('');

            return `
                <div class="report-header text-center mb-4">
                    <h3>ميزان المراجعة</h3>
                    <p class="text-muted">كما في ${data.as_of_date}</p>
                </div>

                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>رمز الحساب</th>
                            <th>اسم الحساب</th>
                            <th class="text-end">إجمالي المدين</th>
                            <th class="text-end">إجمالي الدائن</th>
                            <th class="text-end">رصيد مدين</th>
                            <th class="text-end">رصيد دائن</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${accountsHTML}
                    </tbody>
                    <tfoot class="table-secondary">
                        <tr>
                            <th colspan="2">الإجمالي</th>
                            <th class="text-end">$${data.totals.total_debits.toFixed(2)}</th>
                            <th class="text-end">$${data.totals.total_credits.toFixed(2)}</th>
                            <th class="text-end">$${data.totals.debit_balances.toFixed(2)}</th>
                            <th class="text-end">$${data.totals.credit_balances.toFixed(2)}</th>
                        </tr>
                    </tfoot>
                </table>

                <div class="alert ${data.balanced ? 'alert-success' : 'alert-danger'}">
                    ${data.balanced ? 'ميزان المراجعة متوازن ✓' : 'ميزان المراجعة غير متوازن ✗'}
                </div>
            `;
        }

        // إنشاء HTML لتقرير التدفق النقدي
        function generateCashFlowHTML(data) {
            return `
                <div class="report-header text-center mb-4">
                    <h3>تقرير التدفق النقدي</h3>
                    <p class="text-muted">من ${data.period.from} إلى ${data.period.to}</p>
                </div>

                <div class="row">
                    <div class="col-12">
                        <h5 class="text-primary">الأنشطة التشغيلية</h5>
                        <table class="table table-sm">
                            <tr><td>عمليات الصرافة</td><td class="text-end">$${data.operating_activities.exchange_operations.toFixed(2)}</td></tr>
                            <tr><td>عمليات التحويل</td><td class="text-end">$${data.operating_activities.transfer_operations.toFixed(2)}</td></tr>
                            <tr><td>عمليات أخرى</td><td class="text-end">$${data.operating_activities.other_operations.toFixed(2)}</td></tr>
                            <tr class="table-primary"><th>صافي التدفق من الأنشطة التشغيلية</th><th class="text-end">$${data.net_operating_cash_flow.toFixed(2)}</th></tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4>صافي التدفق النقدي: <span class="text-primary">$${data.net_cash_flow.toFixed(2)}</span></h4>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // إنشاء HTML لملخص العمليات
        function generateOperationsSummaryHTML(data) {
            let transactionHTML = data.transaction_summary.map(trans =>
                `<tr>
                    <td>${trans.transaction_type}</td>
                    <td class="text-end">${trans.transaction_count}</td>
                    <td class="text-end">$${parseFloat(trans.total_amount).toFixed(2)}</td>
                    <td class="text-end">$${parseFloat(trans.average_amount).toFixed(2)}</td>
                </tr>`
            ).join('');

            return `
                <div class="report-header text-center mb-4">
                    <h3>ملخص العمليات</h3>
                    <p class="text-muted">من ${data.period.from} إلى ${data.period.to}</p>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4 class="text-primary">${data.customer_summary.unique_customers}</h4>
                                <p class="text-muted">عملاء فريدين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4 class="text-success">${data.customer_summary.total_transactions}</h4>
                                <p class="text-muted">إجمالي المعاملات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <h5>تفصيل المعاملات حسب النوع</h5>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>نوع المعاملة</th>
                                    <th class="text-end">عدد المعاملات</th>
                                    <th class="text-end">إجمالي المبلغ</th>
                                    <th class="text-end">متوسط المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${transactionHTML}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
</div>

<?php include '../includes/footer.php'; ?>
