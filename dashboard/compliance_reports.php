<?php
require_once '../includes/auth.php';

$auth = new Auth();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('reports.compliance')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى تقارير الامتثال');
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'تقارير الامتثال - ' . SYSTEM_NAME;
$page_header = 'تقارير الامتثال';
$page_subtitle = 'تقارير مكافحة غسل الأموال والامتثال';
$page_icon = 'fas fa-shield-alt';
$show_breadcrumb = true;

// جلب البيانات الأساسية
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // إحصائيات سريعة
    $today = date('Y-m-d');
    $month_start = date('Y-m-01');
    
    // فحوصات الامتثال اليوم
    $stmt = $db->prepare("
        SELECT 
            check_result,
            COUNT(*) as count
        FROM compliance_checks 
        WHERE DATE(check_date) = :today
        GROUP BY check_result
    ");
    $stmt->bindParam(':today', $today);
    $stmt->execute();
    $today_compliance = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    // العملاء عالي المخاطر
    $stmt = $db->prepare("
        SELECT COUNT(*) as high_risk_count
        FROM customers 
        WHERE risk_level = 'high' AND kyc_status = 'approved'
    ");
    $stmt->execute();
    $high_risk_stats = $stmt->fetch();
    
    // المعاملات عالية القيمة هذا الشهر
    $stmt = $db->prepare("
        SELECT COUNT(*) as high_value_count
        FROM transactions 
        WHERE DATE(transaction_date) >= :month_start 
        AND total_amount_base_currency >= 10000
    ");
    $stmt->bindParam(':month_start', $month_start);
    $stmt->execute();
    $high_value_stats = $stmt->fetch();
    
    // العملاء المعلقين للمراجعة
    $stmt = $db->prepare("
        SELECT COUNT(*) as pending_kyc_count
        FROM customers 
        WHERE kyc_status IN ('pending', 'under_review')
    ");
    $stmt->execute();
    $pending_kyc_stats = $stmt->fetch();
    
} catch (Exception $e) {
    $today_compliance = [];
    $high_risk_stats = ['high_risk_count' => 0];
    $high_value_stats = ['high_value_count' => 0];
    $pending_kyc_stats = ['pending_kyc_count' => 0];
}

include '../includes/header.php';
?>

<!-- صفحة تقارير الامتثال -->
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">تقارير الامتثال</h2>
                        <p class="text-muted">مراقبة الامتثال وإدارة المخاطر</p>
                    </div>
                    <div>
                        <button class="btn btn-success me-2" onclick="generateDailyComplianceReport()">
                            <i class="fas fa-file-alt me-2"></i>
                            تقرير يومي
                        </button>
                        <a href="reports.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            التقارير المالية
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number text-success"><?php echo $today_compliance['pass'] ?? 0; ?></div>
                    <div class="text-muted">فحوصات ناجحة اليوم</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number text-danger"><?php echo $high_risk_stats['high_risk_count']; ?></div>
                    <div class="text-muted">عملاء عالي المخاطر</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number text-warning"><?php echo $high_value_stats['high_value_count']; ?></div>
                    <div class="text-muted">معاملات عالية القيمة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number text-info"><?php echo $pending_kyc_stats['pending_kyc_count']; ?></div>
                    <div class="text-muted">KYC معلق</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- تقارير الامتثال -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            تقارير الامتثال والمخاطر
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- تقرير KYC -->
                            <div class="col-md-6">
                                <div class="compliance-card" onclick="openComplianceModal('kyc_report')">
                                    <div class="compliance-icon bg-success-gradient mx-auto">
                                        <i class="fas fa-user-check"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">تقرير KYC</h6>
                                        <p class="text-muted small">حالة التحقق من هوية العملاء</p>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير AML -->
                            <div class="col-md-6">
                                <div class="compliance-card" onclick="openComplianceModal('aml_report')">
                                    <div class="compliance-icon bg-warning-gradient mx-auto">
                                        <i class="fas fa-search-dollar"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">تقرير AML</h6>
                                        <p class="text-muted small">مكافحة غسل الأموال</p>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير المخاطر -->
                            <div class="col-md-6">
                                <div class="compliance-card" onclick="openComplianceModal('risk_report')">
                                    <div class="compliance-icon bg-danger-gradient mx-auto">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">تقرير المخاطر</h6>
                                        <p class="text-muted small">تحليل وتقييم المخاطر</p>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير العقوبات -->
                            <div class="col-md-6">
                                <div class="compliance-card" onclick="openComplianceModal('sanctions_report')">
                                    <div class="compliance-icon bg-info-gradient mx-auto">
                                        <i class="fas fa-ban"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">تقرير العقوبات</h6>
                                        <p class="text-muted small">فحص قوائم العقوبات</p>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير المعاملات المشبوهة -->
                            <div class="col-md-6">
                                <div class="compliance-card" onclick="openComplianceModal('suspicious_report')">
                                    <div class="compliance-icon bg-primary-gradient mx-auto">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">المعاملات المشبوهة</h6>
                                        <p class="text-muted small">رصد الأنشطة المشبوهة</p>
                                    </div>
                                </div>
                            </div>

                            <!-- تقرير الأشخاص المعرضين سياسياً -->
                            <div class="col-md-6">
                                <div class="compliance-card" onclick="openComplianceModal('pep_report')">
                                    <div class="compliance-icon bg-secondary-gradient mx-auto">
                                        <i class="fas fa-user-tie"></i>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="fw-bold">تقرير PEP</h6>
                                        <p class="text-muted small">الأشخاص المعرضين سياسياً</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الشريط الجانبي -->
            <div class="col-lg-4">
                <!-- نقاط الامتثال -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            نقاط الامتثال
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="compliance-score score-excellent">95%</div>
                        <p class="text-muted">نقاط الامتثال الإجمالية</p>
                        
                        <div class="progress mb-3" style="height: 10px;">
                            <div class="progress-bar bg-success" style="width: 95%"></div>
                        </div>
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="h5 text-success">98%</div>
                                <small class="text-muted">KYC</small>
                            </div>
                            <div class="col-6">
                                <div class="h5 text-warning">92%</div>
                                <small class="text-muted">AML</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تنبيهات الامتثال -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bell me-2"></i>
                            تنبيهات الامتثال
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>5 عملاء</strong> يحتاجون مراجعة KYC
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>3 معاملات</strong> عالية القيمة اليوم
                        </div>
                        
                        <div class="alert alert-danger">
                            <i class="fas fa-ban me-2"></i>
                            <strong>1 عميل</strong> في قائمة المراقبة
                        </div>
                    </div>
                </div>

                <!-- إجراءات سريعة -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="reviewPendingKYC()">
                                <i class="fas fa-user-check me-2"></i>
                                مراجعة KYC المعلق
                            </button>
                            
                            <button class="btn btn-outline-warning btn-sm" onclick="checkHighValueTransactions()">
                                <i class="fas fa-dollar-sign me-2"></i>
                                المعاملات عالية القيمة
                            </button>
                            
                            <button class="btn btn-outline-danger btn-sm" onclick="reviewHighRiskCustomers()">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                العملاء عالي المخاطر
                            </button>
                            
                            <button class="btn btn-outline-info btn-sm" onclick="generateComplianceAlert()">
                                <i class="fas fa-bell me-2"></i>
                                إنشاء تنبيه امتثال
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function openComplianceModal(reportType) {
            alert('تقرير ' + reportType + ' قيد التطوير');
        }

        function generateDailyComplianceReport() {
            alert('تقرير الامتثال اليومي قيد التطوير');
        }

        function reviewPendingKYC() {
            window.location.href = 'customers.php?filter=pending_kyc';
        }

        function checkHighValueTransactions() {
            alert('عرض المعاملات عالية القيمة قيد التطوير');
        }

        function reviewHighRiskCustomers() {
            window.location.href = 'customers.php?filter=high_risk';
        }

        function generateComplianceAlert() {
            alert('إنشاء تنبيه امتثال قيد التطوير');
        }
    </script>

<?php include '../includes/footer.php'; ?>
