<?php
require_once '../includes/auth.php';
require_once '../includes/customer_manager.php';

header('Content-Type: application/json');

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    echo json_encode([]);
    exit();
}

if (!$auth->hasPermission('customers.view')) {
    echo json_encode([]);
    exit();
}

$search_term = trim($_GET['q'] ?? '');

if (strlen($search_term) < 2) {
    echo json_encode([]);
    exit();
}

try {
    $customerManager = new CustomerManager();
    
    // البحث عن العملاء النشطين فقط
    $filters = ['blacklisted' => false];
    $customers = $customerManager->searchCustomers($search_term, $filters, 10, 0);
    
    // تنسيق النتائج
    $results = [];
    foreach ($customers as $customer) {
        $results[] = [
            'id' => $customer['id'],
            'full_name' => $customer['full_name'],
            'id_number' => $customer['id_number'],
            'phone' => $customer['phone'],
            'email' => $customer['email'],
            'kyc_status' => $customer['kyc_status'],
            'risk_level' => $customer['risk_level']
        ];
    }
    
    echo json_encode($results);
    
} catch (Exception $e) {
    echo json_encode([]);
}
?>
