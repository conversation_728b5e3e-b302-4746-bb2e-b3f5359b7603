<?php
require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/permission_manager.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

// التحقق من الصلاحيات
if (!$auth->hasPermission('permissions.view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى إدارة الصلاحيات';
    header('Location: index.php');
    exit();
}

$current_user = $auth->getCurrentUser();
$permissionManager = new PermissionManager();

// معالجة الطلبات
$action = $_GET['action'] ?? 'list';
$permission_id = $_GET['id'] ?? null;
$message = '';
$error = '';

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!hash_equals($_SESSION['csrf_token'] ?? '', $csrf_token)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        switch ($_POST['action']) {
            case 'add_permission':
                if ($auth->hasPermission('permissions.create')) {
                    $result = $permissionManager->createPermission($_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: permissions.php');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لإضافة صلاحيات';
                }
                break;
                
            case 'edit_permission':
                if ($auth->hasPermission('permissions.edit')) {
                    $result = $permissionManager->updatePermission($_POST['permission_id'], $_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: permissions.php');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لتعديل الصلاحيات';
                }
                break;
        }
    }
}

// إنشاء رمز CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// جلب البيانات حسب الإجراء
$permissions = [];
$permission_data = null;
$modules = [];

try {
    if ($action == 'list' || $action == 'add' || $action == 'edit') {
        $permissions = $permissionManager->getAllPermissions();
        $modules = $permissionManager->getModules();
    }
    
    if ($action == 'edit' && $permission_id) {
        $permission_data = $permissionManager->getPermissionById($permission_id);
        if (!$permission_data) {
            $error = 'الصلاحية غير موجودة';
            $action = 'list';
        }
    }
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
}

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'إدارة الصلاحيات - ' . SYSTEM_NAME;
$page_header = 'إدارة الصلاحيات';
$page_subtitle = 'إدارة صلاحيات النظام والوحدات';
$page_icon = 'fas fa-key';
$show_breadcrumb = true;

// إضافة أزرار الإجراءات
$page_actions = '';
if ($auth->hasPermission('permissions.create') && $action == 'list') {
    $page_actions = '<a href="permissions.php?action=add" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة صلاحية جديدة
    </a>';
}
?>
<?php include '../includes/header.php'; ?>

<div class="container-fluid p-4">
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($action == 'list'): ?>
        <!-- قائمة الصلاحيات -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>
                    قائمة الصلاحيات
                </h5>
            </div>
            <div class="card-body">
                <!-- شريط البحث والفلترة -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="searchPermissions" placeholder="البحث في الصلاحيات...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterModule">
                            <option value="">جميع الوحدات</option>
                            <?php foreach ($modules as $module): ?>
                                <option value="<?php echo htmlspecialchars($module); ?>">
                                    <?php echo ucfirst($module); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <!-- تجميع الصلاحيات حسب الوحدة -->
                <?php 
                $grouped_permissions = [];
                foreach ($permissions as $permission) {
                    $module = $permission['module'];
                    if (!isset($grouped_permissions[$module])) {
                        $grouped_permissions[$module] = [];
                    }
                    $grouped_permissions[$module][] = $permission;
                }
                ?>

                <div class="row">
                    <?php foreach ($grouped_permissions as $module => $module_permissions): ?>
                        <div class="col-md-6 mb-4" data-module="<?php echo htmlspecialchars($module); ?>">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-cog me-2"></i>
                                        <?php echo ucfirst($module); ?>
                                        <span class="badge bg-primary ms-2"><?php echo count($module_permissions); ?></span>
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>اسم الصلاحية</th>
                                                    <th>الوصف</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($module_permissions as $permission): ?>
                                                    <tr class="permission-row">
                                                        <td>
                                                            <code><?php echo htmlspecialchars($permission['permission_name']); ?></code>
                                                        </td>
                                                        <td>
                                                            <small><?php echo htmlspecialchars($permission['description'] ?? ''); ?></small>
                                                        </td>
                                                        <td>
                                                            <?php if ($auth->hasPermission('permissions.edit')): ?>
                                                                <a href="permissions.php?action=edit&id=<?php echo $permission['id']; ?>" 
                                                                   class="btn btn-sm btn-outline-primary" title="تعديل">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

    <?php elseif ($action == 'add'): ?>
        <!-- نموذج إضافة صلاحية جديدة -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة صلاحية جديدة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="permissions.php" id="addPermissionForm">
                    <input type="hidden" name="action" value="add_permission">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="permission_name" class="form-label">اسم الصلاحية <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="permission_name" name="permission_name" required
                                       placeholder="مثال: users.create">
                                <div class="form-text">استخدم النمط: module.action (مثل: users.create)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="module" class="form-label">الوحدة <span class="text-danger">*</span></label>
                                <select class="form-select" id="module" name="module" required>
                                    <option value="">اختر الوحدة</option>
                                    <?php foreach ($modules as $module): ?>
                                        <option value="<?php echo htmlspecialchars($module); ?>">
                                            <?php echo ucfirst($module); ?>
                                        </option>
                                    <?php endforeach; ?>
                                    <option value="new">وحدة جديدة...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row" id="newModuleRow" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_module" class="form-label">اسم الوحدة الجديدة</label>
                                <input type="text" class="form-control" id="new_module" name="new_module"
                                       placeholder="مثال: reports">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="وصف مختصر للصلاحية"></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="permissions.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الصلاحية
                        </button>
                    </div>
                </form>
            </div>
        </div>

    <?php elseif ($action == 'edit' && $permission_data): ?>
        <!-- نموذج تعديل الصلاحية -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الصلاحية: <?php echo htmlspecialchars($permission_data['permission_name']); ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="permissions.php" id="editPermissionForm">
                    <input type="hidden" name="action" value="edit_permission">
                    <input type="hidden" name="permission_id" value="<?php echo $permission_data['id']; ?>">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="permission_name" class="form-label">اسم الصلاحية <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="permission_name" name="permission_name"
                                       value="<?php echo htmlspecialchars($permission_data['permission_name']); ?>" required>
                                <div class="form-text">استخدم النمط: module.action (مثل: users.create)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="module" class="form-label">الوحدة <span class="text-danger">*</span></label>
                                <select class="form-select" id="module" name="module" required>
                                    <option value="">اختر الوحدة</option>
                                    <?php foreach ($modules as $module): ?>
                                        <option value="<?php echo htmlspecialchars($module); ?>"
                                                <?php echo $module == $permission_data['module'] ? 'selected' : ''; ?>>
                                            <?php echo ucfirst($module); ?>
                                        </option>
                                    <?php endforeach; ?>
                                    <option value="new">وحدة جديدة...</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="newModuleRow" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_module" class="form-label">اسم الوحدة الجديدة</label>
                                <input type="text" class="form-control" id="new_module" name="new_module">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف مختصر للصلاحية"><?php echo htmlspecialchars($permission_data['description'] ?? ''); ?></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="permissions.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// البحث والفلترة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchPermissions');
    const moduleFilter = document.getElementById('filterModule');
    const moduleCards = document.querySelectorAll('[data-module]');

    function filterPermissions() {
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        const selectedModule = moduleFilter ? moduleFilter.value : '';

        moduleCards.forEach(card => {
            const module = card.dataset.module;
            const moduleMatch = !selectedModule || module === selectedModule;

            if (moduleMatch) {
                const rows = card.querySelectorAll('.permission-row');
                let visibleRows = 0;

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    const searchMatch = !searchTerm || text.includes(searchTerm);

                    if (searchMatch) {
                        row.style.display = '';
                        visibleRows++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                // إخفاء البطاقة إذا لم تكن هناك صفوف مرئية
                card.style.display = visibleRows > 0 ? '' : 'none';
            } else {
                card.style.display = 'none';
            }
        });
    }

    if (searchInput) searchInput.addEventListener('input', filterPermissions);
    if (moduleFilter) moduleFilter.addEventListener('change', filterPermissions);
});

// إدارة الوحدة الجديدة
document.addEventListener('DOMContentLoaded', function() {
    const moduleSelect = document.getElementById('module');
    const newModuleRow = document.getElementById('newModuleRow');
    const newModuleInput = document.getElementById('new_module');

    if (moduleSelect) {
        moduleSelect.addEventListener('change', function() {
            if (this.value === 'new') {
                newModuleRow.style.display = '';
                newModuleInput.required = true;
            } else {
                newModuleRow.style.display = 'none';
                newModuleInput.required = false;
                newModuleInput.value = '';
            }
        });
    }
});

// التحقق من صحة النماذج
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form[id$="PermissionForm"]');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const permissionName = form.querySelector('input[name="permission_name"]');
            const moduleSelect = form.querySelector('select[name="module"]');
            const newModuleInput = form.querySelector('input[name="new_module"]');

            // التحقق من اسم الصلاحية
            if (permissionName && !/^[a-zA-Z0-9_]+\.[a-zA-Z0-9_]+$/.test(permissionName.value)) {
                e.preventDefault();
                alert('اسم الصلاحية يجب أن يكون بالنمط: module.action');
                permissionName.focus();
                return;
            }

            // التحقق من الوحدة الجديدة
            if (moduleSelect && moduleSelect.value === 'new') {
                if (!newModuleInput || !newModuleInput.value.trim()) {
                    e.preventDefault();
                    alert('يرجى إدخال اسم الوحدة الجديدة');
                    if (newModuleInput) newModuleInput.focus();
                    return;
                }

                if (!/^[a-zA-Z0-9_]+$/.test(newModuleInput.value)) {
                    e.preventDefault();
                    alert('اسم الوحدة يجب أن يحتوي على أحرف وأرقام فقط');
                    newModuleInput.focus();
                    return;
                }
            }
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
