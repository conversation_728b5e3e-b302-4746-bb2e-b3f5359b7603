<?php
require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/role_manager.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

// التحقق من الصلاحيات
if (!$auth->hasPermission('roles.view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى إدارة الأدوار';
    header('Location: index.php');
    exit();
}

$current_user = $auth->getCurrentUser();
$roleManager = new RoleManager();

// معالجة الطلبات
$action = $_GET['action'] ?? 'list';
$role_id = $_GET['id'] ?? null;
$message = '';
$error = '';

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!hash_equals($_SESSION['csrf_token'] ?? '', $csrf_token)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        switch ($_POST['action']) {
            case 'add_role':
                if ($auth->hasPermission('roles.create')) {
                    $result = $roleManager->createRole($_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: roles.php');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لإضافة أدوار';
                }
                break;
                
            case 'edit_role':
                if ($auth->hasPermission('roles.edit')) {
                    $result = $roleManager->updateRole($_POST['role_id'], $_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: roles.php');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لتعديل الأدوار';
                }
                break;
                
            case 'toggle_status':
                if ($auth->hasPermission('roles.edit')) {
                    $result = $roleManager->toggleRoleStatus($_POST['role_id']);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                    } else {
                        $_SESSION['error_message'] = $result['message'];
                    }
                    header('Location: roles.php');
                    exit();
                } else {
                    $error = 'ليس لديك صلاحية لتغيير حالة الأدوار';
                }
                break;
                
            case 'update_permissions':
                if ($auth->hasPermission('roles.edit')) {
                    $permissions = $_POST['permissions'] ?? [];
                    $result = $roleManager->updateRolePermissions($_POST['role_id'], $permissions);
                    if ($result['success']) {
                        $_SESSION['success_message'] = $result['message'];
                        header('Location: roles.php?action=permissions&id=' . $_POST['role_id']);
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                } else {
                    $error = 'ليس لديك صلاحية لتعديل صلاحيات الأدوار';
                }
                break;
        }
    }
}

// إنشاء رمز CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// جلب البيانات حسب الإجراء
$roles = [];
$permissions = [];
$role_data = null;
$role_permissions = [];

try {
    if ($action == 'list' || $action == 'add' || $action == 'edit' || $action == 'permissions') {
        $roles = $roleManager->getAllRoles();
    }
    
    if ($action == 'edit' && $role_id) {
        $role_data = $roleManager->getRoleById($role_id);
        if (!$role_data) {
            $error = 'الدور غير موجود';
            $action = 'list';
        }
    }
    
    if ($action == 'permissions' && $role_id) {
        $role_data = $roleManager->getRoleById($role_id);
        if (!$role_data) {
            $error = 'الدور غير موجود';
            $action = 'list';
        } else {
            $permissions = $roleManager->getAllPermissions();
            $role_permissions = $roleManager->getRolePermissions($role_id);
        }
    }
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
}

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'إدارة الأدوار - ' . SYSTEM_NAME;
$page_header = 'إدارة الأدوار';
$page_subtitle = 'إدارة أدوار المستخدمين والصلاحيات';
$page_icon = 'fas fa-user-tag';
$show_breadcrumb = true;

// إضافة أزرار الإجراءات
$page_actions = '';
if ($auth->hasPermission('roles.create') && $action == 'list') {
    $page_actions = '<a href="roles.php?action=add" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة دور جديد
    </a>';
}
?>
<?php include '../includes/header.php'; ?>

<div class="container-fluid p-4">
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($action == 'list'): ?>
        <!-- قائمة الأدوار -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-tag me-2"></i>
                    قائمة الأدوار
                </h5>
            </div>
            <div class="card-body">
                <!-- شريط البحث -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="searchRoles" placeholder="البحث في الأدوار...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterStatus">
                            <option value="">جميع الحالات</option>
                            <option value="1">نشط</option>
                            <option value="0">غير نشط</option>
                        </select>
                    </div>
                </div>

                <!-- جدول الأدوار -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="rolesTable">
                        <thead class="table-dark">
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الدور</th>
                                <th>الوصف</th>
                                <th>عدد المستخدمين</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($roles as $role): ?>
                                <tr data-status="<?php echo $role['is_active']; ?>">
                                    <td><?php echo $role['id']; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($role['role_name']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($role['description'] ?? ''); ?></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $role['users_count'] ?? 0; ?> مستخدم
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($role['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d', strtotime($role['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <?php if ($auth->hasPermission('roles.edit')): ?>
                                                <a href="roles.php?action=edit&id=<?php echo $role['id']; ?>" 
                                                   class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                <a href="roles.php?action=permissions&id=<?php echo $role['id']; ?>" 
                                                   class="btn btn-outline-info" title="إدارة الصلاحيات">
                                                    <i class="fas fa-key"></i>
                                                </a>
                                                
                                                <?php if ($role['id'] != 1): // منع تعديل دور المدير ?>
                                                    <button type="button" class="btn btn-outline-warning" 
                                                            onclick="toggleRoleStatus(<?php echo $role['id']; ?>, <?php echo $role['is_active'] ? 0 : 1; ?>)"
                                                            title="<?php echo $role['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="fas fa-<?php echo $role['is_active'] ? 'ban' : 'check'; ?>"></i>
                                                    </button>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    <?php elseif ($action == 'add'): ?>
        <!-- نموذج إضافة دور جديد -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة دور جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="roles.php" id="addRoleForm">
                    <input type="hidden" name="action" value="add_role">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role_name" class="form-label">اسم الدور <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="role_name" name="role_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="is_active" class="form-label">الحالة</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="1" selected>نشط</option>
                                    <option value="0">غير نشط</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف مختصر للدور وصلاحياته"></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="roles.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ الدور
                        </button>
                    </div>
                </form>
            </div>
        </div>

    <?php elseif ($action == 'edit' && $role_data): ?>
        <!-- نموذج تعديل الدور -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الدور: <?php echo htmlspecialchars($role_data['role_name']); ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="roles.php" id="editRoleForm">
                    <input type="hidden" name="action" value="edit_role">
                    <input type="hidden" name="role_id" value="<?php echo $role_data['id']; ?>">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role_name" class="form-label">اسم الدور <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="role_name" name="role_name"
                                       value="<?php echo htmlspecialchars($role_data['role_name']); ?>"
                                       <?php echo $role_data['id'] == 1 ? 'readonly' : ''; ?> required>
                                <?php if ($role_data['id'] == 1): ?>
                                    <div class="form-text text-warning">لا يمكن تعديل اسم دور المدير</div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="is_active" class="form-label">الحالة</label>
                                <select class="form-select" id="is_active" name="is_active"
                                        <?php echo $role_data['id'] == 1 ? 'disabled' : ''; ?>>
                                    <option value="1" <?php echo $role_data['is_active'] ? 'selected' : ''; ?>>نشط</option>
                                    <option value="0" <?php echo !$role_data['is_active'] ? 'selected' : ''; ?>>غير نشط</option>
                                </select>
                                <?php if ($role_data['id'] == 1): ?>
                                    <input type="hidden" name="is_active" value="1">
                                    <div class="form-text text-warning">لا يمكن إلغاء تفعيل دور المدير</div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف مختصر للدور وصلاحياته"><?php echo htmlspecialchars($role_data['description'] ?? ''); ?></textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="roles.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>

    <?php elseif ($action == 'permissions' && $role_data): ?>
        <!-- إدارة صلاحيات الدور -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>
                    إدارة صلاحيات الدور: <?php echo htmlspecialchars($role_data['role_name']); ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="roles.php" id="permissionsForm">
                    <input type="hidden" name="action" value="update_permissions">
                    <input type="hidden" name="role_id" value="<?php echo $role_data['id']; ?>">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                    <?php if ($role_data['id'] == 1): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            دور المدير يحصل على جميع الصلاحيات تلقائياً
                        </div>
                    <?php endif; ?>

                    <!-- تجميع الصلاحيات حسب الوحدة -->
                    <?php
                    $grouped_permissions = [];
                    foreach ($permissions as $permission) {
                        $module = $permission['module'];
                        if (!isset($grouped_permissions[$module])) {
                            $grouped_permissions[$module] = [];
                        }
                        $grouped_permissions[$module][] = $permission;
                    }
                    ?>

                    <div class="row">
                        <?php foreach ($grouped_permissions as $module => $module_permissions): ?>
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-cog me-2"></i>
                                            <?php echo ucfirst($module); ?>
                                            <div class="form-check form-switch float-end">
                                                <input class="form-check-input module-toggle" type="checkbox"
                                                       data-module="<?php echo $module; ?>"
                                                       <?php echo $role_data['id'] == 1 ? 'checked disabled' : ''; ?>>
                                                <label class="form-check-label">تحديد الكل</label>
                                            </div>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <?php foreach ($module_permissions as $permission): ?>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input permission-checkbox"
                                                       type="checkbox"
                                                       name="permissions[]"
                                                       value="<?php echo $permission['id']; ?>"
                                                       id="perm_<?php echo $permission['id']; ?>"
                                                       data-module="<?php echo $module; ?>"
                                                       <?php echo in_array($permission['id'], $role_permissions) ? 'checked' : ''; ?>
                                                       <?php echo $role_data['id'] == 1 ? 'checked disabled' : ''; ?>>
                                                <label class="form-check-label" for="perm_<?php echo $permission['id']; ?>">
                                                    <strong><?php echo htmlspecialchars($permission['permission_name']); ?></strong>
                                                    <?php if ($permission['description']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($permission['description']); ?></small>
                                                    <?php endif; ?>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="roles.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة
                        </a>
                        <?php if ($role_data['id'] != 1): ?>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الصلاحيات
                            </button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- نماذج منبثقة -->
<!-- نموذج تأكيد تغيير الحالة -->
<div class="modal fade" id="toggleStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد تغيير الحالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="statusMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="roles.php" style="display: inline;">
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="role_id" id="toggleRoleId">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    <button type="submit" class="btn btn-warning">تأكيد</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// البحث والفلترة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchRoles');
    const statusFilter = document.getElementById('filterStatus');
    const table = document.getElementById('rolesTable');

    if (table) {
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

        function filterTable() {
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const selectedStatus = statusFilter ? statusFilter.value : '';

            for (let row of rows) {
                const text = row.textContent.toLowerCase();
                const statusMatch = !selectedStatus || row.dataset.status === selectedStatus;
                const searchMatch = !searchTerm || text.includes(searchTerm);

                if (statusMatch && searchMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        if (searchInput) searchInput.addEventListener('input', filterTable);
        if (statusFilter) statusFilter.addEventListener('change', filterTable);
    }
});

// تغيير حالة الدور
function toggleRoleStatus(roleId, newStatus) {
    const message = newStatus === 1 ?
        'هل أنت متأكد من تفعيل هذا الدور؟' :
        'هل أنت متأكد من إلغاء تفعيل هذا الدور؟';

    document.getElementById('statusMessage').textContent = message;
    document.getElementById('toggleRoleId').value = roleId;

    const modal = new bootstrap.Modal(document.getElementById('toggleStatusModal'));
    modal.show();
}

// إدارة الصلاحيات
document.addEventListener('DOMContentLoaded', function() {
    // تبديل جميع صلاحيات الوحدة
    const moduleToggles = document.querySelectorAll('.module-toggle');
    moduleToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const module = this.dataset.module;
            const checkboxes = document.querySelectorAll(`input[data-module="${module}"].permission-checkbox`);

            checkboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = this.checked;
                }
            });
        });
    });

    // تحديث حالة تبديل الوحدة عند تغيير الصلاحيات الفردية
    const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');
    permissionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const module = this.dataset.module;
            const moduleToggle = document.querySelector(`.module-toggle[data-module="${module}"]`);
            const moduleCheckboxes = document.querySelectorAll(`input[data-module="${module}"].permission-checkbox`);

            if (moduleToggle && !moduleToggle.disabled) {
                const checkedCount = Array.from(moduleCheckboxes).filter(cb => cb.checked && !cb.disabled).length;
                const enabledCount = Array.from(moduleCheckboxes).filter(cb => !cb.disabled).length;

                moduleToggle.checked = checkedCount === enabledCount;
                moduleToggle.indeterminate = checkedCount > 0 && checkedCount < enabledCount;
            }
        });
    });

    // تحديث حالة تبديل الوحدات عند تحميل الصفحة
    const modules = [...new Set(Array.from(permissionCheckboxes).map(cb => cb.dataset.module))];
    modules.forEach(module => {
        const moduleToggle = document.querySelector(`.module-toggle[data-module="${module}"]`);
        const moduleCheckboxes = document.querySelectorAll(`input[data-module="${module}"].permission-checkbox`);

        if (moduleToggle && !moduleToggle.disabled) {
            const checkedCount = Array.from(moduleCheckboxes).filter(cb => cb.checked && !cb.disabled).length;
            const enabledCount = Array.from(moduleCheckboxes).filter(cb => !cb.disabled).length;

            moduleToggle.checked = checkedCount === enabledCount;
            moduleToggle.indeterminate = checkedCount > 0 && checkedCount < enabledCount;
        }
    });
});

// التحقق من صحة النماذج
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form[id$="RoleForm"]');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const roleName = form.querySelector('input[name="role_name"]');

            // التحقق من اسم الدور
            if (roleName && roleName.value.trim().length < 2) {
                e.preventDefault();
                alert('اسم الدور يجب أن يكون حرفين على الأقل');
                roleName.focus();
                return;
            }
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
