<?php
require_once '../config.php';
require_once '../includes/auth.php';
require_once '../includes/user_manager.php';

// بدء الجلسة إذا لم تكن نشطة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$auth = new Auth();

// التحقق من صحة الجلسة
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php?message=' . urlencode('يرجى تسجيل الدخول أولاً'));
    exit();
}

$current_user = $auth->getCurrentUser();
$userManager = new UserManager();

// معالجة الطلبات
$action = $_GET['action'] ?? 'view';
$message = '';
$error = '';

// معالجة طلبات POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!hash_equals($_SESSION['csrf_token'] ?? '', $csrf_token)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        switch ($_POST['action']) {
            case 'update_profile':
                // تحديث البيانات الشخصية
                $profile_data = [
                    'username' => $_POST['username'],
                    'email' => $_POST['email'],
                    'full_name' => $_POST['full_name'],
                    'role_id' => $current_user['role_id'], // لا يمكن تغيير الدور
                    'branch_id' => $current_user['branch_id'], // لا يمكن تغيير الفرع
                    'is_active' => $current_user['is_active'] // لا يمكن تغيير الحالة
                ];
                
                $result = $userManager->updateUser($current_user['id'], $profile_data);
                if ($result['success']) {
                    $_SESSION['success_message'] = $result['message'];
                    // تحديث بيانات الجلسة
                    $_SESSION['user_data'] = $userManager->getUserById($current_user['id']);
                    header('Location: profile.php');
                    exit();
                } else {
                    $error = $result['message'];
                }
                break;
                
            case 'change_password':
                // تغيير كلمة المرور
                $current_password = $_POST['current_password'];
                $new_password = $_POST['new_password'];
                $confirm_password = $_POST['confirm_password'];
                
                // التحقق من كلمة المرور الحالية
                if (!password_verify($current_password, $current_user['password_hash'])) {
                    $error = 'كلمة المرور الحالية غير صحيحة';
                } elseif ($new_password !== $confirm_password) {
                    $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
                } elseif (strlen($new_password) < 8) {
                    $error = 'كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل';
                } else {
                    // تحديث كلمة المرور
                    $password_data = [
                        'username' => $current_user['username'],
                        'email' => $current_user['email'],
                        'full_name' => $current_user['full_name'],
                        'role_id' => $current_user['role_id'],
                        'branch_id' => $current_user['branch_id'],
                        'is_active' => $current_user['is_active'],
                        'new_password' => $new_password
                    ];
                    
                    $result = $userManager->updateUser($current_user['id'], $password_data);
                    if ($result['success']) {
                        $_SESSION['success_message'] = 'تم تغيير كلمة المرور بنجاح';
                        header('Location: profile.php');
                        exit();
                    } else {
                        $error = $result['message'];
                    }
                }
                break;
        }
    }
}

// إنشاء رمز CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// جلب البيانات المحدثة للمستخدم
try {
    $user_data = $userManager->getUserById($current_user['id']);
    if (!$user_data) {
        $error = 'خطأ في جلب بيانات المستخدم';
        $user_data = $current_user;
    }
    
    // جلب إحصائيات المستخدم
    $database = new Database();
    $db = $database->getConnection();
    
    // عدد العمليات التي قام بها المستخدم
    $stmt = $db->prepare("SELECT COUNT(*) as total_operations FROM audit_logs WHERE user_id = ?");
    $stmt->execute([$current_user['id']]);
    $user_operations = $stmt->fetch()['total_operations'] ?? 0;
    
    // آخر عملية قام بها
    $stmt = $db->prepare("SELECT description, created_at FROM audit_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$current_user['id']]);
    $last_operation = $stmt->fetch();
    
    // عدد أيام العضوية
    $membership_days = ceil((time() - strtotime($user_data['created_at'])) / (60 * 60 * 24));
    
} catch (Exception $e) {
    $error = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $user_data = $current_user;
    $user_operations = 0;
    $last_operation = null;
    $membership_days = 0;
}

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'الملف الشخصي - ' . SYSTEM_NAME;
$page_header = 'الملف الشخصي';
$page_subtitle = 'إدارة البيانات الشخصية والإعدادات';
$page_icon = 'fas fa-user';
$show_breadcrumb = true;
?>
<?php include '../includes/header.php'; ?>

<div class="container-fluid p-4">
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- معلومات المستخدم الأساسية -->
        <div class="col-lg-4">
            <div class="card profile-header">
                <div class="card-body text-center">
                    <!-- صورة المستخدم -->
                    <div class="profile-avatar-container mb-3">
                        <img src="https://ui-avatars.com/api/?name=<?php echo urlencode($user_data['full_name']); ?>&size=120&background=4361ee&color=ffffff&font-size=0.5" 
                             alt="صورة المستخدم" class="profile-avatar">
                    </div>
                    
                    <!-- اسم المستخدم -->
                    <h4 class="profile-name"><?php echo htmlspecialchars($user_data['full_name']); ?></h4>
                    <p class="profile-role text-muted">
                        <i class="fas fa-user-tag me-1"></i>
                        <?php echo htmlspecialchars($user_data['role_name'] ?? 'غير محدد'); ?>
                    </p>
                    
                    <!-- معلومات إضافية -->
                    <div class="profile-info">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="profile-stat">
                                    <div class="profile-stat-value"><?php echo $membership_days; ?></div>
                                    <div class="profile-stat-label">يوم عضوية</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="profile-stat">
                                    <div class="profile-stat-value"><?php echo $user_operations; ?></div>
                                    <div class="profile-stat-label">عملية</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="profile-stat">
                                    <div class="profile-stat-value">
                                        <?php echo $user_data['is_active'] ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'; ?>
                                    </div>
                                    <div class="profile-stat-label">الحالة</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات الجلسة -->
                    <div class="mt-3 pt-3 border-top">
                        <small class="text-muted d-block">
                            <i class="fas fa-clock me-1"></i>
                            آخر دخول: <?php echo $user_data['last_login'] ? date('Y-m-d H:i', strtotime($user_data['last_login'])) : 'لم يسجل دخول'; ?>
                        </small>
                        <small class="text-muted d-block">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            الفرع: <?php echo htmlspecialchars($user_data['branch_name'] ?? 'غير محدد'); ?>
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- آخر نشاط -->
            <?php if ($last_operation): ?>
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>
                            آخر نشاط
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-1"><?php echo htmlspecialchars($last_operation['description']); ?></p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo date('Y-m-d H:i', strtotime($last_operation['created_at'])); ?>
                        </small>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- تبويبات الإعدادات -->
        <div class="col-lg-8">
            <!-- تبويبات التنقل -->
            <ul class="nav nav-tabs mb-4" id="profileTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link <?php echo $action == 'view' ? 'active' : ''; ?>" 
                            id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" 
                            type="button" role="tab">
                        <i class="fas fa-user me-2"></i>
                        البيانات الشخصية
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link <?php echo $action == 'password' ? 'active' : ''; ?>" 
                            id="password-tab" data-bs-toggle="tab" data-bs-target="#password" 
                            type="button" role="tab">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="activity-tab" data-bs-toggle="tab" 
                            data-bs-target="#activity" type="button" role="tab">
                        <i class="fas fa-list me-2"></i>
                        سجل النشاط
                    </button>
                </li>
            </ul>
            
            <!-- محتوى التبويبات -->
            <div class="tab-content" id="profileTabsContent">
                <!-- تبويب البيانات الشخصية -->
                <div class="tab-pane fade <?php echo $action == 'view' ? 'show active' : ''; ?>" 
                     id="profile" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>
                                تعديل البيانات الشخصية
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="profile.php" id="profileForm">
                                <input type="hidden" name="action" value="update_profile">
                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="username" name="username" 
                                                   value="<?php echo htmlspecialchars($user_data['username']); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($user_data['email']); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($user_data['full_name']); ?>" required>
                                </div>
                                
                                <!-- معلومات للقراءة فقط -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الدور</label>
                                            <input type="text" class="form-control" 
                                                   value="<?php echo htmlspecialchars($user_data['role_name'] ?? 'غير محدد'); ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">الفرع</label>
                                            <input type="text" class="form-control" 
                                                   value="<?php echo htmlspecialchars($user_data['branch_name'] ?? 'غير محدد'); ?>" readonly>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ الإنشاء</label>
                                            <input type="text" class="form-control" 
                                                   value="<?php echo date('Y-m-d H:i', strtotime($user_data['created_at'])); ?>" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">آخر تحديث</label>
                                            <input type="text" class="form-control" 
                                                   value="<?php echo date('Y-m-d H:i', strtotime($user_data['updated_at'])); ?>" readonly>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ التغييرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- تبويب تغيير كلمة المرور -->
                <div class="tab-pane fade <?php echo $action == 'password' ? 'show active' : ''; ?>"
                     id="password" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-key me-2"></i>
                                تغيير كلمة المرور
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>متطلبات كلمة المرور:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>يجب أن تكون 8 أحرف على الأقل</li>
                                    <li>يُفضل استخدام مزيج من الأحرف والأرقام والرموز</li>
                                    <li>تجنب استخدام معلومات شخصية واضحة</li>
                                </ul>
                            </div>

                            <form method="POST" action="profile.php" id="passwordForm">
                                <input type="hidden" name="action" value="change_password">
                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                                <div class="mb-3">
                                    <label for="current_password" class="form-label">كلمة المرور الحالية <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                            <i class="fas fa-eye" id="current_password_icon"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="new_password" class="form-label">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="new_password" name="new_password"
                                               minlength="8" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                            <i class="fas fa-eye" id="new_password_icon"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        <div id="password_strength" class="mt-2"></div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                               minlength="8" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye" id="confirm_password_icon"></i>
                                        </button>
                                    </div>
                                    <div id="password_match" class="form-text"></div>
                                </div>

                                <div class="d-flex justify-content-end">
                                    <button type="submit" class="btn btn-warning" id="changePasswordBtn">
                                        <i class="fas fa-key me-2"></i>
                                        تغيير كلمة المرور
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- تبويب سجل النشاط -->
                <div class="tab-pane fade" id="activity" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>
                                سجل النشاط الأخير
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="activityLog">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <p class="mt-2">جاري تحميل سجل النشاط...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        معلومات الأمان
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>جلسة آمنة</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-lock text-success me-2"></i>
                                <span>كلمة مرور محمية</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-user-shield text-success me-2"></i>
                                <span>صلاحيات محددة</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-history text-info me-2"></i>
                                <span>سجل مراجعة</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="change_password.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-key me-2"></i>
                            تغيير كلمة المرور (صفحة منفصلة)
                        </a>
                        <a href="preferences.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-sliders-h me-2"></i>
                            التفضيلات
                        </a>
                        <a href="../auth/logout.php" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تبديل إظهار/إخفاء كلمة المرور
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');

    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// فحص قوة كلمة المرور
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];

    if (password.length >= 8) {
        strength += 1;
    } else {
        feedback.push('يجب أن تكون 8 أحرف على الأقل');
    }

    if (/[a-z]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('يجب أن تحتوي على أحرف صغيرة');
    }

    if (/[A-Z]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('يُفضل أن تحتوي على أحرف كبيرة');
    }

    if (/[0-9]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('يُفضل أن تحتوي على أرقام');
    }

    if (/[^A-Za-z0-9]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('يُفضل أن تحتوي على رموز خاصة');
    }

    return { strength, feedback };
}

// عرض قوة كلمة المرور
document.addEventListener('DOMContentLoaded', function() {
    const newPasswordField = document.getElementById('new_password');
    if (newPasswordField) {
        newPasswordField.addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('password_strength');

            if (password.length === 0) {
                strengthDiv.innerHTML = '';
                return;
            }

            const result = checkPasswordStrength(password);
            let strengthText = '';
            let strengthClass = '';

            switch (result.strength) {
                case 0:
                case 1:
                    strengthText = 'ضعيفة جداً';
                    strengthClass = 'text-danger';
                    break;
                case 2:
                    strengthText = 'ضعيفة';
                    strengthClass = 'text-warning';
                    break;
                case 3:
                    strengthText = 'متوسطة';
                    strengthClass = 'text-info';
                    break;
                case 4:
                    strengthText = 'قوية';
                    strengthClass = 'text-success';
                    break;
                case 5:
                    strengthText = 'قوية جداً';
                    strengthClass = 'text-success';
                    break;
            }

            strengthDiv.innerHTML = `<span class="${strengthClass}">قوة كلمة المرور: ${strengthText}</span>`;
        });
    }

    // فحص تطابق كلمة المرور
    const confirmPasswordField = document.getElementById('confirm_password');
    if (confirmPasswordField) {
        confirmPasswordField.addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            const matchDiv = document.getElementById('password_match');

            if (confirmPassword.length === 0) {
                matchDiv.innerHTML = '';
                return;
            }

            if (newPassword === confirmPassword) {
                matchDiv.innerHTML = '<span class="text-success"><i class="fas fa-check me-1"></i>كلمات المرور متطابقة</span>';
            } else {
                matchDiv.innerHTML = '<span class="text-danger"><i class="fas fa-times me-1"></i>كلمات المرور غير متطابقة</span>';
            }
        });
    }

    // تحميل سجل النشاط عند النقر على التبويب
    const activityTab = document.getElementById('activity-tab');
    if (activityTab) {
        activityTab.addEventListener('click', function() {
            loadActivityLog();
        });
    }
});

function loadActivityLog() {
    const activityDiv = document.getElementById('activityLog');

    // محاكاة تحميل سجل النشاط (يمكن استبداله بـ API حقيقي)
    setTimeout(() => {
        const activities = [
            {
                description: 'تسجيل دخول إلى النظام',
                table_name: 'users',
                created_at: new Date().toISOString()
            },
            {
                description: 'عرض قائمة العملاء',
                table_name: 'customers',
                created_at: new Date(Date.now() - 3600000).toISOString()
            },
            {
                description: 'إضافة عملية صرافة جديدة',
                table_name: 'exchange_operations',
                created_at: new Date(Date.now() - 7200000).toISOString()
            }
        ];

        let html = '';
        if (activities.length === 0) {
            html = '<div class="text-center text-muted"><i class="fas fa-inbox fa-3x mb-3"></i><p>لا توجد أنشطة مسجلة</p></div>';
        } else {
            html = '<div class="timeline">';
            activities.forEach(activity => {
                const date = new Date(activity.created_at);
                html += `
                    <div class="timeline-item mb-3 p-3 border-start border-3 border-primary">
                        <div class="timeline-content">
                            <h6 class="timeline-title mb-1">${activity.description}</h6>
                            <p class="timeline-text text-muted mb-1">
                                <i class="fas fa-table me-1"></i>
                                ${activity.table_name}
                            </p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                ${date.toLocaleString('ar-EG')}
                            </small>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        }
        activityDiv.innerHTML = html;
    }, 1000);
}

// التحقق من صحة النماذج
document.addEventListener('DOMContentLoaded', function() {
    const profileForm = document.getElementById('profileForm');
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();
            const fullName = document.getElementById('full_name').value.trim();

            if (!username || !email || !fullName) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            if (username.length < 3) {
                e.preventDefault();
                alert('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
                return;
            }

            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('يرجى إدخال بريد إلكتروني صحيح');
                return;
            }
        });
    }

    const passwordForm = document.getElementById('passwordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (!currentPassword || !newPassword || !confirmPassword) {
                e.preventDefault();
                alert('يرجى ملء جميع حقول كلمة المرور');
                return;
            }

            if (newPassword.length < 8) {
                e.preventDefault();
                alert('كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل');
                return;
            }

            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور الجديدة وتأكيدها غير متطابقين');
                return;
            }
        });
    }
});

// تفعيل التبويبات حسب المعامل في الرابط
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');

    if (action === 'password') {
        const passwordTab = document.getElementById('password-tab');
        if (passwordTab) {
            passwordTab.click();
        }
    }
});
</script>

<?php include '../includes/footer.php'; ?>
