<?php
require_once '../includes/auth.php';
require_once '../includes/exchange_manager.php';
require_once '../includes/customer_manager.php';

$auth = new Auth();
$exchangeManager = new ExchangeManager();
$customerManager = new CustomerManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('exchange.view')) {
    header('Location: index.php?error=ليس لديك صلاحية للوصول إلى هذه الصفحة');
    exit();
}

$current_user = $auth->getCurrentUser();

// إعدادات الصفحة
$page_type = 'dashboard';
$page_title = 'عمليات الصرافة - ' . SYSTEM_NAME;
$page_header = 'عمليات الصرافة';
$page_subtitle = 'إدارة عمليات تبديل العملات وحساب الأرباح';
$page_icon = 'fas fa-exchange-alt';
$show_breadcrumb = true;

$error_message = '';
$success_message = '';

// جلب البيانات الأساسية
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب العملات النشطة
    $stmt = $db->prepare("SELECT * FROM currencies WHERE is_active = 1 ORDER BY is_base_currency DESC, name ASC");
    $stmt->execute();
    $currencies = $stmt->fetchAll();
    
    // جلب عمليات الصرافة الأخيرة
    $stmt = $db->prepare("
        SELECT t.*, cet.*,
               CASE
                   WHEN c.id_number LIKE 'ANON_%' THEN CONCAT('👤 ', c.full_name)
                   ELSE c.full_name
               END as customer_name,
               c.id_number as customer_id_number,
               c1.code as sell_currency_code, c1.symbol as sell_currency_symbol,
               c2.code as buy_currency_code, c2.symbol as buy_currency_symbol,
               u.full_name as user_name
        FROM transactions t
        JOIN currency_exchange_transactions cet ON t.id = cet.transaction_id
        JOIN customers c ON t.customer_id = c.id
        JOIN currencies c1 ON cet.sell_currency_id = c1.id
        JOIN currencies c2 ON cet.buy_currency_id = c2.id
        JOIN users u ON t.user_id = u.id
        WHERE t.transaction_type = 'exchange'
        ORDER BY t.created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $recent_exchanges = $stmt->fetchAll();
    
    // جلب إحصائيات اليوم
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as today_transactions,
            SUM(cet.sell_amount) as today_volume,
            SUM(cet.commission_amount) as today_commission
        FROM transactions t
        JOIN currency_exchange_transactions cet ON t.id = cet.transaction_id
        WHERE t.transaction_type = 'exchange' 
          AND DATE(t.transaction_date) = CURDATE()
          AND t.branch_id = :branch_id
    ");
    $stmt->bindParam(':branch_id', $current_user['branch_id']);
    $stmt->execute();
    $today_stats = $stmt->fetch();
    
} catch (Exception $e) {
    $currencies = [];
    $recent_exchanges = [];
    $today_stats = ['today_transactions' => 0, 'today_volume' => 0, 'today_commission' => 0];
}

// معالجة البحث عن العملاء
$customer_search = '';
$customers = [];
if (isset($_GET['search_customer']) && !empty($_GET['search_customer'])) {
    $customer_search = trim($_GET['search_customer']);
    $customers = $customerManager->searchCustomers($customer_search, [], 10, 0);
}

include '../includes/header.php';
?>
<!-- صفحة عمليات الصرافة -->
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">عمليات الصرافة</h2>
                        <p class="text-muted">إدارة عمليات صرف العملات وحساب الأرباح</p>
                    </div>
                    <div>
                        <?php if ($auth->hasPermission('exchange.rates')): ?>
                        <a href="exchange_rates.php" class="btn btn-outline-primary me-2">
                            <i class="fas fa-chart-line me-2"></i>
                            إدارة أسعار الصرف
                        </a>
                        <?php endif; ?>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($error_message): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <!-- إحصائيات اليوم -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-icon bg-success-gradient mx-auto">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0"><?php echo number_format($today_stats['today_transactions']); ?></h3>
                        <p class="text-muted mb-0">عمليات اليوم</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-icon bg-warning-gradient mx-auto">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0">$<?php echo number_format($today_stats['today_volume'], 2); ?></h3>
                        <p class="text-muted mb-0">حجم التداول اليوم</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="stats-icon bg-info-gradient mx-auto">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0">$<?php echo number_format($today_stats['today_commission'], 2); ?></h3>
                        <p class="text-muted mb-0">العمولات اليوم</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- آلة حاسبة الصرافة -->
            <div class="col-lg-6">
                <?php if ($auth->hasPermission('exchange.create')): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            عملية صرافة جديدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="exchangeForm" action="process_exchange.php" method="POST" autocomplete="off">
                            <!-- البحث عن العميل -->
                            <div class="mb-3">
                                <label class="form-label">العميل</label>
                                <div class="input-group">
                                    <input type="text"
                                           class="form-control"
                                           id="customerSearch"
                                           placeholder="ابحث عن العميل بالاسم أو رقم الهوية (اتركه فارغاً للعميل المجهول)"
                                           autocomplete="off">
                                    <button type="button" class="btn btn-outline-secondary" id="searchBtn">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <input type="hidden" id="selectedCustomerId" name="customer_id" value="">
                                <div id="customerResults" class="mt-2"></div>
                                <div id="selectedCustomer" class="mt-2" style="display: none;">
                                    <div class="alert alert-info">
                                        <strong>العميل المختار:</strong> <span id="selectedCustomerName"></span>
                                        <button type="button" class="btn btn-sm btn-outline-secondary float-end" id="changeCustomer">
                                            تغيير
                                        </button>
                                    </div>
                                </div>
                                <div id="anonymousCustomer" class="mt-2">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-user-secret me-2"></i>
                                        <strong>عميل مجهول:</strong> لم يتم اختيار عميل محدد - ستتم العملية كعميل مجهول
                                        <div class="form-check mt-2">
                                            <input class="form-check-input" type="checkbox" id="confirmAnonymous" checked>
                                            <label class="form-check-label" for="confirmAnonymous">
                                                أؤكد إجراء العملية كعميل مجهول
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- عملة البيع -->
                            <div class="mb-3">
                                <label class="form-label">العملة المباعة (من)</label>
                                <select class="form-control" id="sellCurrency" name="sell_currency_id" required>
                                    <option value="">اختر العملة</option>
                                    <?php foreach ($currencies as $currency): ?>
                                        <option value="<?php echo $currency['id']; ?>" 
                                                data-symbol="<?php echo htmlspecialchars($currency['symbol']); ?>"
                                                data-code="<?php echo htmlspecialchars($currency['code']); ?>">
                                            <?php echo htmlspecialchars($currency['name']) . ' (' . htmlspecialchars($currency['code']) . ')'; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- مبلغ البيع -->
                            <div class="mb-3">
                                <label class="form-label">المبلغ المباع</label>
                                <div class="currency-input">
                                    <input type="number" 
                                           class="form-control" 
                                           id="sellAmount" 
                                           name="sell_amount" 
                                           step="0.01" 
                                           min="0.01"
                                           placeholder="0.00"
                                           required>
                                    <span class="currency-symbol" id="sellSymbol"></span>
                                </div>
                            </div>

                            <!-- سهم التحويل -->
                            <div class="exchange-arrow">
                                <button type="button" class="btn btn-outline-primary" id="swapCurrencies">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>
                            </div>

                            <!-- عملة الشراء -->
                            <div class="mb-3">
                                <label class="form-label">العملة المشتراة (إلى)</label>
                                <select class="form-control" id="buyCurrency" name="buy_currency_id" required>
                                    <option value="">اختر العملة</option>
                                    <?php foreach ($currencies as $currency): ?>
                                        <option value="<?php echo $currency['id']; ?>" 
                                                data-symbol="<?php echo htmlspecialchars($currency['symbol']); ?>"
                                                data-code="<?php echo htmlspecialchars($currency['code']); ?>">
                                            <?php echo htmlspecialchars($currency['name']) . ' (' . htmlspecialchars($currency['code']) . ')'; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- مبلغ الشراء -->
                            <div class="mb-3">
                                <label class="form-label">المبلغ المستلم</label>
                                <div class="currency-input">
                                    <input type="number" 
                                           class="form-control" 
                                           id="buyAmount" 
                                           name="buy_amount" 
                                           step="0.01" 
                                           min="0.01"
                                           placeholder="0.00"
                                           readonly>
                                    <span class="currency-symbol" id="buySymbol"></span>
                                </div>
                            </div>

                            <!-- عرض سعر الصرف -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <label class="form-label">سعر الصرف</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="exchangeRate" readonly>
                                            <span class="input-group-text" id="rateSource"></span>
                                        </div>
                                        <small class="form-text text-muted" id="rateLastUpdated"></small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-2">
                                        <label class="form-label">العمولة</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="commission" readonly>
                                            <span class="input-group-text" id="sellCurrencyCode2"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" name="branch_id" value="<?php echo $current_user['branch_id']; ?>">

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitExchange">
                                    <i class="fas fa-check me-2"></i>
                                    تنفيذ عملية الصرافة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <?php else: ?>
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-lock fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">ليس لديك صلاحية لإنشاء عمليات صرافة</h5>
                        <p class="text-muted">تواصل مع مدير النظام للحصول على الصلاحيات المطلوبة</p>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- العمليات الأخيرة -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            العمليات الأخيرة
                        </h5>
                        <a href="exchange_history.php" class="btn btn-sm btn-light">
                            عرض الكل
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_exchanges)): ?>
                            <div class="text-center p-4">
                                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد عمليات صرافة</h6>
                                <p class="text-muted">لم يتم تنفيذ أي عمليات صرافة بعد</p>
                            </div>
                        <?php else: ?>
                            <div style="max-height: 500px; overflow-y: auto;">
                                <?php foreach ($recent_exchanges as $exchange): ?>
                                <div class="transaction-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($exchange['customer_name']); ?></h6>
                                            <div class="text-muted small">
                                                <?php echo htmlspecialchars($exchange['reference_number']); ?>
                                            </div>
                                            <div class="mt-1">
                                                <span class="badge bg-primary">
                                                    <?php echo number_format($exchange['sell_amount'], 2); ?> 
                                                    <?php echo htmlspecialchars($exchange['sell_currency_code']); ?>
                                                    →
                                                    <?php echo number_format($exchange['buy_amount'], 2); ?> 
                                                    <?php echo htmlspecialchars($exchange['buy_currency_code']); ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <div class="text-success fw-bold">
                                                +<?php echo htmlspecialchars($exchange['sell_currency_symbol']); ?><?php echo number_format($exchange['commission_amount'], 2); ?>
                                            </div>
                                            <div class="text-muted small">
                                                <?php echo date('H:i', strtotime($exchange['created_at'])); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // متغيرات عامة
        let exchangeRates = {};
        let selectedCustomer = null;

        // تحديث رموز العملات
        function updateCurrencySymbols() {
            const sellCurrency = document.getElementById('sellCurrency');
            const buyCurrency = document.getElementById('buyCurrency');
            
            if (sellCurrency.value) {
                const sellOption = sellCurrency.options[sellCurrency.selectedIndex];
                document.getElementById('sellSymbol').textContent = sellOption.dataset.symbol || '';
            }
            
            if (buyCurrency.value) {
                const buyOption = buyCurrency.options[buyCurrency.selectedIndex];
                document.getElementById('buySymbol').textContent = buyOption.dataset.symbol || '';
            }
        }

        // دالة لعرض رسائل الخطأ
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger alert-dismissible fade show';
            errorDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
            `;
            
            // إضافة رسالة الخطأ في أعلى النموذج
            const form = document.getElementById('exchangeForm');
            form.parentNode.insertBefore(errorDiv, form);
            
            // إزالة الرسالة تلقائياً بعد 5 ثوان
            setTimeout(() => {
                errorDiv.classList.remove('show');
                setTimeout(() => errorDiv.remove(), 300);
            }, 5000);
        }

        // حساب مبلغ الصرافة
        function calculateExchange() {
            const sellCurrencyId = document.getElementById('sellCurrency').value;
            const buyCurrencyId = document.getElementById('buyCurrency').value;
            const sellAmount = parseFloat(document.getElementById('sellAmount').value) || 0;
            
            if (!sellCurrencyId || !buyCurrencyId || sellAmount <= 0) {
                document.getElementById('buyAmount').value = '';
                document.getElementById('rateDisplay').style.display = 'none';
                return;
            }
            
            // تعطيل زر الإرسال حتى يتم الحساب
            document.getElementById('submitExchange').disabled = true;
            
            // استدعاء API للحصول على سعر الصرف
            fetch('get_exchange_rate.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    from_currency_id: sellCurrencyId,
                    to_currency_id: buyCurrencyId,
                    amount: sellAmount
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('buyAmount').value = data.buy_amount.toFixed(2);
                    document.getElementById('exchangeRate').value = data.exchange_rate.toFixed(6);
                    document.getElementById('commission').value = data.commission_amount.toFixed(2);
                    
                    // عرض مصدر الأسعار
                    const sourceText = data.source === 'API' ? 'API' : 'يدوي';
                    document.getElementById('rateSource').textContent = sourceText;
                    
                    // عرض تاريخ آخر تحديث
                    document.getElementById('rateLastUpdated').textContent = 'آخر تحديث: ' + data.last_updated;
                    
                    // تحديث رموز العملات
                    document.getElementById('sellCurrencyCode2').textContent = data.sell_currency_code;
                    
                    // تمكين زر الإرسال بعد الحساب
                    document.getElementById('submitExchange').disabled = false;
                } else {
                    // عرض رسالة الخطأ
                    showError('خطأ في حساب سعر الصرف: ' + data.message);
                    document.getElementById('submitExchange').disabled = true;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('خطأ في الاتصال بالخادم');
                document.getElementById('submitExchange').disabled = true;
            });
        }

        // البحث عن العملاء
        function searchCustomers() {
            const searchTerm = document.getElementById('customerSearch').value.trim();
            
            if (searchTerm.length < 2) {
                document.getElementById('customerResults').innerHTML = '';
                return;
            }
            
            fetch('search_customers.php?q=' + encodeURIComponent(searchTerm))
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('customerResults');
                resultsDiv.innerHTML = '';
                
                if (data.length > 0) {
                    data.forEach(customer => {
                        const customerDiv = document.createElement('div');
                        customerDiv.className = 'customer-search-result';
                        customerDiv.innerHTML = `
                            <div class="fw-bold">${customer.full_name}</div>
                            <div class="text-muted small">${customer.id_number} | ${customer.phone || 'لا يوجد هاتف'}</div>
                        `;
                        customerDiv.onclick = () => selectCustomer(customer);
                        resultsDiv.appendChild(customerDiv);
                    });
                } else {
                    resultsDiv.innerHTML = '<div class="text-muted text-center p-2">لا توجد نتائج</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        // اختيار عميل
        function selectCustomer(customer) {
            selectedCustomer = customer;
            document.getElementById('selectedCustomerId').value = customer.id;
            document.getElementById('selectedCustomerName').textContent = customer.full_name;
            document.getElementById('customerSearch').value = customer.full_name;
            document.getElementById('customerResults').innerHTML = '';
            document.getElementById('selectedCustomer').style.display = 'block';
            document.getElementById('anonymousCustomer').style.display = 'none';
        }

        // إظهار/إخفاء تنبيه العميل المجهول
        function toggleAnonymousAlert() {
            const customerSearch = document.getElementById('customerSearch').value.trim();
            const selectedCustomer = document.getElementById('selectedCustomer').style.display;

            if (!customerSearch && selectedCustomer === 'none') {
                document.getElementById('anonymousCustomer').style.display = 'block';
            } else if (customerSearch && selectedCustomer === 'none') {
                document.getElementById('anonymousCustomer').style.display = 'none';
            }
        }

        // تبديل العملات
        function swapCurrencies() {
            const sellCurrency = document.getElementById('sellCurrency');
            const buyCurrency = document.getElementById('buyCurrency');
            
            const tempValue = sellCurrency.value;
            sellCurrency.value = buyCurrency.value;
            buyCurrency.value = tempValue;
            
            updateCurrencySymbols();
            calculateExchange();
        }

        // إعداد الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // أحداث العملات
            document.getElementById('sellCurrency').addEventListener('change', function() {
                updateCurrencySymbols();
                calculateExchange();
            });
            
            document.getElementById('buyCurrency').addEventListener('change', function() {
                updateCurrencySymbols();
                calculateExchange();
            });
            
            document.getElementById('sellAmount').addEventListener('input', calculateExchange);
            
            // أحداث البحث عن العملاء
            document.getElementById('customerSearch').addEventListener('input', searchCustomers);
            document.getElementById('searchBtn').addEventListener('click', searchCustomers);
            
            // تبديل العملات
            document.getElementById('swapCurrencies').addEventListener('click', swapCurrencies);
            
            // تغيير العميل
            document.getElementById('changeCustomer').addEventListener('click', function() {
                selectedCustomer = null;
                document.getElementById('selectedCustomerId').value = '';
                document.getElementById('customerSearch').value = '';
                document.getElementById('selectedCustomer').style.display = 'none';
                document.getElementById('anonymousCustomer').style.display = 'block';
            });

            // مراقبة تغيير حقل البحث
            document.getElementById('customerSearch').addEventListener('input', function() {
                toggleAnonymousAlert();
                searchCustomers();
            });

            // التحقق من النموذج قبل الإرسال
            document.getElementById('exchangeForm').addEventListener('submit', function(e) {
                const customerId = document.getElementById('selectedCustomerId').value;
                const confirmAnonymous = document.getElementById('confirmAnonymous').checked;
                const sellAmount = parseFloat(document.getElementById('sellAmount').value);
                const buyAmount = parseFloat(document.getElementById('buyAmount').value);
                const sellCurrency = document.getElementById('sellCurrency').value;
                const buyCurrency = document.getElementById('buyCurrency').value;

                // إذا لم يتم اختيار عميل ولم يتم تأكيد العميل المجهول
                if (!customerId && !confirmAnonymous) {
                    e.preventDefault();
                    alert('يرجى اختيار عميل أو تأكيد إجراء العملية كعميل مجهول');
                    return false;
                }

                // التحقق من المبلغ
                if (!sellAmount || sellAmount <= 0) {
                    e.preventDefault();
                    alert('يرجى إدخال مبلغ صحيح للبيع');
                    return false;
                }
                
                // التأكد من اختيار العملات
                if (!sellCurrency) {
                    e.preventDefault();
                    alert('يرجى اختيار العملة المباعة');
                    return false;
                }
                
                if (!buyCurrency) {
                    e.preventDefault();
                    alert('يرجى اختيار العملة المشتراة');
                    return false;
                }
                
                // التأكد من أن العملتين مختلفتين
                if (sellCurrency === buyCurrency) {
                    e.preventDefault();
                    alert('لا يمكن اختيار نفس العملة للبيع والشراء');
                    return false;
                }
                
                // التأكد من حساب المبلغ المستلم
                if (!buyAmount || buyAmount <= 0) {
                    e.preventDefault();
                    alert('يرجى حساب المبلغ المستلم أولاً');
                    return false;
                }
                
                // إذا وصلنا إلى هنا، فالنموذج صحيح
                return true;
            });
            
            // عرض أو إخفاء معلومات العميل المجهول عند التحميل
            toggleAnonymousAlert();
        });

        // إخفاء رسائل التنبيه تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-success, .alert-danger');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>

<?php
// إضافة CSS خاص بصفحة الصرافة
$additional_head = '
<style>
    .currency-input {
        position: relative;
    }

    .currency-symbol {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-weight: bold;
    }

    .exchange-arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 1rem 0;
    }

    .rate-display {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        margin: 1rem 0;
    }

    .customer-search-result {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .customer-search-result:hover {
        background: #f8f9fa;
        border-color: #667eea;
    }

    .transaction-item {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #e9ecef;
    }
</style>
';

include '../includes/footer.php';
?>
