<div class="card">
    <div class="card-header">
        <h4 class="card-title mb-0">
            <i class="fas fa-question-circle me-2"></i>
            الأسئلة الشائعة
        </h4>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            هنا ستجد إجابات للأسئلة الأكثر شيوعاً حول استخدام نظام Trust Plus.
        </div>

        <!-- فئات الأسئلة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary active" data-category="general">عام</button>
                    <button type="button" class="btn btn-outline-primary" data-category="users">المستخدمين</button>
                    <button type="button" class="btn btn-outline-primary" data-category="customers">العملاء</button>
                    <button type="button" class="btn btn-outline-primary" data-category="exchange">الصرافة</button>
                    <button type="button" class="btn btn-outline-primary" data-category="transfers">التحويلات</button>
                    <button type="button" class="btn btn-outline-primary" data-category="reports">التقارير</button>
                    <button type="button" class="btn btn-outline-primary" data-category="technical">تقني</button>
                </div>
            </div>
        </div>

        <!-- الأسئلة العامة -->
        <div class="faq-category" data-category="general">
            <h5 class="text-primary mb-3">الأسئلة العامة</h5>
            
            <div class="accordion" id="generalFAQ">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#general1">
                            ما هو نظام Trust Plus؟
                        </button>
                    </h2>
                    <div id="general1" class="accordion-collapse collapse show" data-bs-parent="#generalFAQ">
                        <div class="accordion-body">
                            Trust Plus هو نظام شامل لإدارة العمليات المالية والمحاسبية لشركات الصرافة والتحويلات المالية. 
                            يتضمن النظام وحدات لإدارة المستخدمين، العملاء، عمليات الصرافة، التحويلات المالية، والتقارير المحاسبية.
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#general2">
                            ما هي متطلبات تشغيل النظام؟
                        </button>
                    </h2>
                    <div id="general2" class="accordion-collapse collapse" data-bs-parent="#generalFAQ">
                        <div class="accordion-body">
                            <ul>
                                <li><strong>الخادم:</strong> PHP 7.4 أو أحدث</li>
                                <li><strong>قاعدة البيانات:</strong> MySQL 5.7 أو أحدث</li>
                                <li><strong>المتصفح:</strong> Chrome, Firefox, Safari, Edge (أحدث إصدار)</li>
                                <li><strong>الذاكرة:</strong> 512 MB RAM كحد أدنى</li>
                                <li><strong>التخزين:</strong> 1 GB مساحة فارغة</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#general3">
                            هل النظام آمن؟
                        </button>
                    </h2>
                    <div id="general3" class="accordion-collapse collapse" data-bs-parent="#generalFAQ">
                        <div class="accordion-body">
                            نعم، النظام مصمم بأعلى معايير الأمان:
                            <ul>
                                <li>تشفير كلمات المرور باستخدام خوارزميات متقدمة</li>
                                <li>نظام صلاحيات متعدد المستويات</li>
                                <li>حماية من هجمات SQL Injection</li>
                                <li>سجلات تدقيق شاملة لجميع العمليات</li>
                                <li>جلسات آمنة مع انتهاء صلاحية تلقائي</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أسئلة المستخدمين -->
        <div class="faq-category d-none" data-category="users">
            <h5 class="text-primary mb-3">إدارة المستخدمين</h5>
            
            <div class="accordion" id="usersFAQ">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#users1">
                            كيف أضيف مستخدم جديد؟
                        </button>
                    </h2>
                    <div id="users1" class="accordion-collapse collapse show" data-bs-parent="#usersFAQ">
                        <div class="accordion-body">
                            <ol>
                                <li>اذهب إلى <strong>إدارة المستخدمين</strong> من الشريط الجانبي</li>
                                <li>انقر على <strong>إضافة مستخدم جديد</strong></li>
                                <li>املأ البيانات المطلوبة (الاسم، البريد الإلكتروني، كلمة المرور)</li>
                                <li>اختر الدور المناسب للمستخدم</li>
                                <li>انقر على <strong>حفظ</strong></li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#users2">
                            كيف أغير صلاحيات مستخدم؟
                        </button>
                    </h2>
                    <div id="users2" class="accordion-collapse collapse" data-bs-parent="#usersFAQ">
                        <div class="accordion-body">
                            يمكنك تغيير صلاحيات المستخدم بطريقتين:
                            <ul>
                                <li><strong>تغيير الدور:</strong> من صفحة تعديل المستخدم، اختر دور جديد</li>
                                <li><strong>تعديل الدور نفسه:</strong> من صفحة إدارة الأدوار، عدل صلاحيات الدور</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أسئلة العملاء -->
        <div class="faq-category d-none" data-category="customers">
            <h5 class="text-primary mb-3">إدارة العملاء</h5>
            
            <div class="accordion" id="customersFAQ">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#customers1">
                            ما هو نظام KYC وما أهميته؟
                        </button>
                    </h2>
                    <div id="customers1" class="accordion-collapse collapse show" data-bs-parent="#customersFAQ">
                        <div class="accordion-body">
                            KYC (Know Your Customer) هو نظام التحقق من هوية العميل وهو مطلوب قانونياً:
                            <ul>
                                <li><strong>التحقق من الهوية:</strong> التأكد من صحة بيانات العميل</li>
                                <li><strong>مكافحة غسيل الأموال:</strong> منع الأنشطة المشبوهة</li>
                                <li><strong>الامتثال القانوني:</strong> الالتزام بالقوانين المحلية والدولية</li>
                                <li><strong>إدارة المخاطر:</strong> تقييم مستوى مخاطر كل عميل</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#customers2">
                            كيف أرفع مستندات العميل؟
                        </button>
                    </h2>
                    <div id="customers2" class="accordion-collapse collapse" data-bs-parent="#customersFAQ">
                        <div class="accordion-body">
                            <ol>
                                <li>اذهب إلى صفحة تفاصيل العميل</li>
                                <li>انقر على تبويب <strong>المستندات</strong></li>
                                <li>انقر على <strong>رفع مستند جديد</strong></li>
                                <li>اختر نوع المستند (هوية، جواز سفر، إثبات عنوان، إلخ)</li>
                                <li>اختر الملف وأضف ملاحظات إذا لزم الأمر</li>
                                <li>انقر على <strong>رفع</strong></li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أسئلة الصرافة -->
        <div class="faq-category d-none" data-category="exchange">
            <h5 class="text-primary mb-3">عمليات الصرافة</h5>
            
            <div class="accordion" id="exchangeFAQ">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#exchange1">
                            كيف أحدث أسعار الصرف؟
                        </button>
                    </h2>
                    <div id="exchange1" class="accordion-collapse collapse show" data-bs-parent="#exchangeFAQ">
                        <div class="accordion-body">
                            يمكنك تحديث أسعار الصرف بعدة طرق:
                            <ul>
                                <li><strong>يدوياً:</strong> من صفحة إدارة أسعار الصرف</li>
                                <li><strong>تلقائياً:</strong> من خلال API خارجي (إذا كان متاحاً)</li>
                                <li><strong>مجدولة:</strong> تحديث تلقائي في أوقات محددة</li>
                            </ul>
                            <div class="alert alert-warning mt-2">
                                <small><i class="fas fa-exclamation-triangle me-1"></i>
                                تأكد من مراجعة الأسعار قبل تطبيقها</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أسئلة التحويلات -->
        <div class="faq-category d-none" data-category="transfers">
            <h5 class="text-primary mb-3">التحويلات المالية</h5>
            
            <div class="accordion" id="transfersFAQ">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#transfers1">
                            ما الفرق بين التحويل المحلي والدولي؟
                        </button>
                    </h2>
                    <div id="transfers1" class="accordion-collapse collapse show" data-bs-parent="#transfersFAQ">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>التحويل المحلي:</h6>
                                    <ul>
                                        <li>داخل نفس البلد</li>
                                        <li>نفس العملة عادة</li>
                                        <li>رسوم أقل</li>
                                        <li>وقت تنفيذ أسرع</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>التحويل الدولي:</h6>
                                    <ul>
                                        <li>بين دول مختلفة</li>
                                        <li>قد يتطلب تحويل عملة</li>
                                        <li>رسوم أعلى</li>
                                        <li>وقت تنفيذ أطول</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أسئلة التقارير -->
        <div class="faq-category d-none" data-category="reports">
            <h5 class="text-primary mb-3">التقارير</h5>
            
            <div class="accordion" id="reportsFAQ">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#reports1">
                            كيف أصدر تقرير يومي؟
                        </button>
                    </h2>
                    <div id="reports1" class="accordion-collapse collapse show" data-bs-parent="#reportsFAQ">
                        <div class="accordion-body">
                            <ol>
                                <li>اذهب إلى <strong>التقارير المالية</strong></li>
                                <li>اختر <strong>التقرير اليومي</strong></li>
                                <li>حدد التاريخ المطلوب</li>
                                <li>اختر نوع التقرير (مبيعات، أرباح، حركة نقدية)</li>
                                <li>انقر على <strong>إنشاء التقرير</strong></li>
                                <li>يمكنك طباعة أو تصدير التقرير</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أسئلة تقنية -->
        <div class="faq-category d-none" data-category="technical">
            <h5 class="text-primary mb-3">الأسئلة التقنية</h5>
            
            <div class="accordion" id="technicalFAQ">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#technical1">
                            ماذا أفعل إذا نسيت كلمة المرور؟
                        </button>
                    </h2>
                    <div id="technical1" class="accordion-collapse collapse show" data-bs-parent="#technicalFAQ">
                        <div class="accordion-body">
                            <ol>
                                <li>اتصل بمدير النظام</li>
                                <li>سيقوم بإعادة تعيين كلمة المرور لك</li>
                                <li>ستحصل على كلمة مرور مؤقتة</li>
                                <li>قم بتغيير كلمة المرور فور تسجيل الدخول</li>
                            </ol>
                            <div class="alert alert-info mt-2">
                                <small><i class="fas fa-info-circle me-1"></i>
                                لأسباب أمنية، لا يمكن استرداد كلمة المرور القديمة</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#technical2">
                            النظام بطيء، ما الحل؟
                        </button>
                    </h2>
                    <div id="technical2" class="accordion-collapse collapse" data-bs-parent="#technicalFAQ">
                        <div class="accordion-body">
                            جرب هذه الحلول:
                            <ul>
                                <li>تحديث المتصفح إلى أحدث إصدار</li>
                                <li>مسح ذاكرة التخزين المؤقت (Cache)</li>
                                <li>التأكد من سرعة الإنترنت</li>
                                <li>إغلاق التبويبات غير المستخدمة</li>
                                <li>إعادة تشغيل المتصفح</li>
                                <li>الاتصال بالدعم الفني إذا استمرت المشكلة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مربع البحث في الأسئلة -->
        <div class="mt-4">
            <div class="card bg-light">
                <div class="card-body text-center">
                    <h5>لم تجد إجابة لسؤالك؟</h5>
                    <p class="text-muted">ابحث في قاعدة المعرفة أو اتصل بفريق الدعم</p>
                    <div class="row">
                        <div class="col-md-8 mx-auto">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="ابحث عن سؤالك هنا...">
                                <button class="btn btn-primary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="help.php?section=contact" class="btn btn-success">
                            <i class="fas fa-phone me-2"></i>
                            اتصل بالدعم الفني
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تبديل فئات الأسئلة
document.addEventListener('DOMContentLoaded', function() {
    const categoryButtons = document.querySelectorAll('[data-category]');
    const faqCategories = document.querySelectorAll('.faq-category');
    
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // تحديث الأزرار
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // إظهار/إخفاء الفئات
            faqCategories.forEach(cat => {
                if (cat.dataset.category === category) {
                    cat.classList.remove('d-none');
                } else {
                    cat.classList.add('d-none');
                }
            });
        });
    });
});
</script>
