<div class="card">
    <div class="card-header">
        <h4 class="card-title mb-0">
            <i class="fas fa-tools me-2"></i>
            استكشاف الأخطاء وحلها
        </h4>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            هذا القسم يساعدك في حل المشاكل الشائعة التي قد تواجهها أثناء استخدام نظام Trust Plus.
        </div>

        <!-- مشاكل تسجيل الدخول -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2 text-primary"></i>
                    مشاكل تسجيل الدخول
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="loginIssues">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#login1">
                                لا أستطيع تسجيل الدخول - كلمة المرور خاطئة
                            </button>
                        </h2>
                        <div id="login1" class="accordion-collapse collapse show" data-bs-parent="#loginIssues">
                            <div class="accordion-body">
                                <h6>الحلول المقترحة:</h6>
                                <ol>
                                    <li><strong>تأكد من كتابة كلمة المرور بشكل صحيح</strong>
                                        <ul>
                                            <li>تحقق من حالة الأحرف (كبيرة/صغيرة)</li>
                                            <li>تأكد من عدم تفعيل Caps Lock</li>
                                            <li>جرب كتابة كلمة المرور في مكان آخر أولاً</li>
                                        </ul>
                                    </li>
                                    <li><strong>مسح ذاكرة المتصفح</strong>
                                        <ul>
                                            <li>امسح الكوكيز والبيانات المحفوظة</li>
                                            <li>جرب في نافذة تصفح خاصة</li>
                                        </ul>
                                    </li>
                                    <li><strong>اتصل بمدير النظام</strong> لإعادة تعيين كلمة المرور</li>
                                </ol>
                                <div class="alert alert-warning">
                                    <small><i class="fas fa-exclamation-triangle me-1"></i>
                                    إذا فشلت المحاولات 5 مرات، سيتم قفل الحساب مؤقتاً لمدة 15 دقيقة</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#login2">
                                الحساب مقفل أو معطل
                            </button>
                        </h2>
                        <div id="login2" class="accordion-collapse collapse" data-bs-parent="#loginIssues">
                            <div class="accordion-body">
                                <p>إذا ظهرت رسالة أن الحساب مقفل أو معطل:</p>
                                <ul>
                                    <li><strong>القفل المؤقت:</strong> انتظر 15 دقيقة ثم حاول مرة أخرى</li>
                                    <li><strong>تعطيل الحساب:</strong> اتصل بمدير النظام فوراً</li>
                                    <li><strong>انتهاء صلاحية الحساب:</strong> قد تحتاج لتجديد الحساب</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مشاكل الأداء -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt me-2 text-warning"></i>
                    مشاكل الأداء والسرعة
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="performanceIssues">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#perf1">
                                النظام بطيء جداً
                            </button>
                        </h2>
                        <div id="perf1" class="accordion-collapse collapse show" data-bs-parent="#performanceIssues">
                            <div class="accordion-body">
                                <h6>خطوات التشخيص والحل:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary">مشاكل المتصفح:</h6>
                                        <ul>
                                            <li>أغلق التبويبات غير المستخدمة</li>
                                            <li>امسح ذاكرة التخزين المؤقت</li>
                                            <li>حدث المتصفح لأحدث إصدار</li>
                                            <li>أعد تشغيل المتصفح</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-info">مشاكل الشبكة:</h6>
                                        <ul>
                                            <li>تحقق من سرعة الإنترنت</li>
                                            <li>جرب شبكة أخرى</li>
                                            <li>أعد تشغيل الراوتر</li>
                                            <li>اتصل بمزود الخدمة</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="alert alert-info mt-3">
                                    <strong>اختبار سريع:</strong> جرب فتح موقع آخر للتأكد من أن المشكلة في النظام وليس في الإنترنت
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#perf2">
                                الصفحات لا تحمل أو تظهر خطأ
                            </button>
                        </h2>
                        <div id="perf2" class="accordion-collapse collapse" data-bs-parent="#performanceIssues">
                            <div class="accordion-body">
                                <ol>
                                    <li><strong>تحديث الصفحة:</strong> اضغط F5 أو Ctrl+R</li>
                                    <li><strong>تحديث قوي:</strong> اضغط Ctrl+Shift+R</li>
                                    <li><strong>تحقق من الرابط:</strong> تأكد من صحة عنوان URL</li>
                                    <li><strong>جرب متصفح آخر:</strong> للتأكد من أن المشكلة ليست في المتصفح</li>
                                    <li><strong>تحقق من حالة الخادم:</strong> قد يكون هناك صيانة</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مشاكل البيانات -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database me-2 text-danger"></i>
                    مشاكل البيانات والحفظ
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="dataIssues">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#data1">
                                البيانات لا تحفظ أو تختفي
                            </button>
                        </h2>
                        <div id="data1" class="accordion-collapse collapse show" data-bs-parent="#dataIssues">
                            <div class="accordion-body">
                                <h6>الأسباب المحتملة والحلول:</h6>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>السبب</th>
                                                <th>الحل</th>
                                                <th>الوقاية</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>انتهاء الجلسة</td>
                                                <td>سجل دخول مرة أخرى</td>
                                                <td>احفظ العمل بانتظام</td>
                                            </tr>
                                            <tr>
                                                <td>انقطاع الإنترنت</td>
                                                <td>تحقق من الاتصال وأعد المحاولة</td>
                                                <td>استخدم اتصال مستقر</td>
                                            </tr>
                                            <tr>
                                                <td>خطأ في النموذج</td>
                                                <td>تحقق من الحقول المطلوبة</td>
                                                <td>املأ جميع البيانات المطلوبة</td>
                                            </tr>
                                            <tr>
                                                <td>مشكلة في الخادم</td>
                                                <td>انتظر قليلاً وأعد المحاولة</td>
                                                <td>تواصل مع الدعم الفني</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#data2">
                                رسائل خطأ عند الحفظ
                            </button>
                        </h2>
                        <div id="data2" class="accordion-collapse collapse" data-bs-parent="#dataIssues">
                            <div class="accordion-body">
                                <h6>رسائل الخطأ الشائعة:</h6>
                                <div class="alert alert-danger">
                                    <strong>"البيانات مطلوبة"</strong><br>
                                    <small>تأكد من ملء جميع الحقول المطلوبة (المميزة بـ *)</small>
                                </div>
                                <div class="alert alert-warning">
                                    <strong>"البيانات موجودة مسبقاً"</strong><br>
                                    <small>غير البيانات المكررة (مثل رقم الهوية أو البريد الإلكتروني)</small>
                                </div>
                                <div class="alert alert-info">
                                    <strong>"صيغة البيانات غير صحيحة"</strong><br>
                                    <small>تحقق من صيغة البريد الإلكتروني، الأرقام، والتواريخ</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مشاكل الطباعة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-print me-2 text-success"></i>
                    مشاكل الطباعة والتقارير
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="printIssues">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#print1">
                                التقارير لا تطبع بشكل صحيح
                            </button>
                        </h2>
                        <div id="print1" class="accordion-collapse collapse show" data-bs-parent="#printIssues">
                            <div class="accordion-body">
                                <h6>خطوات حل مشاكل الطباعة:</h6>
                                <ol>
                                    <li><strong>تحقق من إعدادات الطباعة:</strong>
                                        <ul>
                                            <li>اختر الطابعة الصحيحة</li>
                                            <li>تأكد من حجم الورق (A4)</li>
                                            <li>اختر الاتجاه المناسب (عمودي/أفقي)</li>
                                        </ul>
                                    </li>
                                    <li><strong>معاينة قبل الطباعة:</strong>
                                        <ul>
                                            <li>استخدم معاينة الطباعة</li>
                                            <li>تحقق من أن جميع البيانات ظاهرة</li>
                                        </ul>
                                    </li>
                                    <li><strong>جرب متصفح آخر:</strong> بعض المتصفحات تطبع بشكل أفضل</li>
                                    <li><strong>تصدير كـ PDF:</strong> ثم اطبع من PDF</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات التشخيص -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-stethoscope me-2 text-info"></i>
                    أدوات التشخيص السريع
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-wifi fa-2x text-primary mb-2"></i>
                                <h6>اختبار الاتصال</h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="testConnection()">
                                    اختبار الآن
                                </button>
                                <div id="connectionResult" class="mt-2"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-browser fa-2x text-info mb-2"></i>
                                <h6>معلومات المتصفح</h6>
                                <button class="btn btn-outline-info btn-sm" onclick="showBrowserInfo()">
                                    عرض المعلومات
                                </button>
                                <div id="browserInfo" class="mt-2"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-memory fa-2x text-warning mb-2"></i>
                                <h6>مسح الذاكرة المؤقتة</h6>
                                <button class="btn btn-outline-warning btn-sm" onclick="clearCache()">
                                    مسح الآن
                                </button>
                                <div id="cacheResult" class="mt-2"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-download fa-2x text-success mb-2"></i>
                                <h6>تحميل تقرير التشخيص</h6>
                                <button class="btn btn-outline-success btn-sm" onclick="downloadDiagnosticReport()">
                                    تحميل التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testConnection() {
    const resultDiv = document.getElementById('connectionResult');
    resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';
    
    fetch(window.location.href)
        .then(response => {
            if (response.ok) {
                resultDiv.innerHTML = '<span class="text-success"><i class="fas fa-check"></i> الاتصال جيد</span>';
            } else {
                resultDiv.innerHTML = '<span class="text-warning"><i class="fas fa-exclamation-triangle"></i> اتصال ضعيف</span>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<span class="text-danger"><i class="fas fa-times"></i> لا يوجد اتصال</span>';
        });
}

function showBrowserInfo() {
    const resultDiv = document.getElementById('browserInfo');
    const info = `
        <small>
            ${navigator.userAgent.split(' ')[0]}<br>
            الإصدار: ${navigator.appVersion.split(' ')[0]}
        </small>
    `;
    resultDiv.innerHTML = info;
}

function clearCache() {
    const resultDiv = document.getElementById('cacheResult');
    resultDiv.innerHTML = '<span class="text-info"><i class="fas fa-info-circle"></i> استخدم Ctrl+Shift+R</span>';
}

function downloadDiagnosticReport() {
    const report = {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        screenResolution: `${screen.width}x${screen.height}`,
        language: navigator.language
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'diagnostic-report.json';
    a.click();
    URL.revokeObjectURL(url);
}
</script>
