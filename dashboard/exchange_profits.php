<?php
require_once '../includes/auth.php';

$auth = new Auth();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('reports.financial')) {
    header('Location: index.php?error=ليس لديك صلاحية لعرض التقارير المالية');
    exit();
}

$current_user = $auth->getCurrentUser();

// معالجة الفلاتر
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // بداية الشهر الحالي
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // اليوم الحالي
$branch_id = $_GET['branch_id'] ?? $current_user['branch_id'];

// إعدادات الصفحة
$page_type = 'dashboard';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // جلب الفروع للفلترة
    $stmt = $db->prepare("SELECT * FROM branches ORDER BY name");
    $stmt->execute();
    $branches = $stmt->fetchAll();
    
    // جلب أرباح الصرافة اليومية
    $where_conditions = ['dep.profit_date BETWEEN :date_from AND :date_to'];
    $params = [':date_from' => $date_from, ':date_to' => $date_to];
    
    if ($branch_id) {
        $where_conditions[] = 'dep.branch_id = :branch_id';
        $params[':branch_id'] = $branch_id;
    }
    
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    $stmt = $db->prepare("
        SELECT dep.*, b.name as branch_name
        FROM daily_exchange_profits dep
        JOIN branches b ON dep.branch_id = b.id
        $where_clause
        ORDER BY dep.profit_date DESC, dep.currency_pair
    ");
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $daily_profits = $stmt->fetchAll();
    
    // حساب الإجماليات
    $stmt = $db->prepare("
        SELECT 
            SUM(total_transactions) as total_transactions,
            SUM(total_volume_from) as total_volume,
            SUM(total_commission) as total_commission,
            SUM(total_spread_profit) as total_spread_profit,
            SUM(total_profit) as total_profit,
            COUNT(DISTINCT profit_date) as trading_days,
            COUNT(DISTINCT currency_pair) as currency_pairs
        FROM daily_exchange_profits dep
        $where_clause
    ");
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $totals = $stmt->fetch();
    
    // أرباح حسب زوج العملة
    $stmt = $db->prepare("
        SELECT 
            currency_pair,
            SUM(total_transactions) as transactions,
            SUM(total_volume_from) as volume,
            SUM(total_commission) as commission,
            SUM(total_spread_profit) as spread_profit,
            SUM(total_profit) as profit,
            AVG(total_profit / total_transactions) as avg_profit_per_transaction
        FROM daily_exchange_profits dep
        $where_clause
        GROUP BY currency_pair
        ORDER BY profit DESC
    ");
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $profits_by_pair = $stmt->fetchAll();
    
    // أرباح يومية للرسم البياني
    $stmt = $db->prepare("
        SELECT 
            profit_date,
            SUM(total_profit) as daily_profit,
            SUM(total_transactions) as daily_transactions
        FROM daily_exchange_profits dep
        $where_clause
        GROUP BY profit_date
        ORDER BY profit_date
    ");
    
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $daily_chart_data = $stmt->fetchAll();
    
} catch (Exception $e) {
    $daily_profits = [];
    $totals = ['total_transactions' => 0, 'total_volume' => 0, 'total_commission' => 0, 'total_spread_profit' => 0, 'total_profit' => 0, 'trading_days' => 0, 'currency_pairs' => 0];
    $profits_by_pair = [];
    $daily_chart_data = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير أرباح الصرافة - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            margin-bottom: 1.5rem;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .bg-success-gradient {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .bg-warning-gradient {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .bg-info-gradient {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .bg-primary-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .profit-positive {
            color: #28a745;
            font-weight: bold;
        }
        
        .profit-negative {
            color: #dc3545;
            font-weight: bold;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-0">تقرير أرباح الصرافة</h2>
                        <p class="text-muted">تحليل مفصل لأرباح عمليات الصرافة والعمولات</p>
                    </div>
                    <div>
                        <button class="btn btn-success me-2" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-2"></i>
                            تصدير Excel
                        </button>
                        <button class="btn btn-info me-2" onclick="printReport()">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                        <a href="exchange.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للصرافة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر التقرير -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر التقرير
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="date_from" value="<?php echo $date_from; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="date_to" value="<?php echo $date_to; ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الفرع</label>
                            <select class="form-control" name="branch_id">
                                <option value="">جميع الفروع</option>
                                <?php foreach ($branches as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>" <?php echo $branch_id == $branch['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($branch['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>
                                    تطبيق الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- إحصائيات عامة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon bg-success-gradient mx-auto">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0 profit-positive">$<?php echo number_format($totals['total_profit'], 2); ?></h3>
                        <p class="text-muted mb-0">إجمالي الأرباح</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon bg-warning-gradient mx-auto">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0">$<?php echo number_format($totals['total_commission'], 2); ?></h3>
                        <p class="text-muted mb-0">إجمالي العمولات</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon bg-info-gradient mx-auto">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0"><?php echo number_format($totals['total_transactions']); ?></h3>
                        <p class="text-muted mb-0">إجمالي المعاملات</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon bg-primary-gradient mx-auto">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="text-center">
                        <h3 class="mb-0">$<?php echo $totals['total_transactions'] > 0 ? number_format($totals['total_profit'] / $totals['total_transactions'], 2) : '0.00'; ?></h3>
                        <p class="text-muted mb-0">متوسط الربح لكل معاملة</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الرسم البياني -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            الأرباح اليومية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="profitsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أرباح حسب زوج العملة -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-coins me-2"></i>
                            الأرباح حسب زوج العملة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($profits_by_pair)): ?>
                            <div class="text-center p-4">
                                <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">لا توجد بيانات</h6>
                                <p class="text-muted">لا توجد أرباح في الفترة المحددة</p>
                            </div>
                        <?php else: ?>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <?php foreach ($profits_by_pair as $pair): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3 p-2 bg-light rounded">
                                    <div>
                                        <div class="fw-bold"><?php echo htmlspecialchars($pair['currency_pair']); ?></div>
                                        <small class="text-muted"><?php echo number_format($pair['transactions']); ?> معاملة</small>
                                    </div>
                                    <div class="text-end">
                                        <div class="profit-positive">$<?php echo number_format($pair['profit'], 2); ?></div>
                                        <small class="text-muted">$<?php echo number_format($pair['avg_profit_per_transaction'], 2); ?> متوسط</small>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول تفصيلي -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    تفاصيل الأرباح اليومية
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($daily_profits)): ?>
                    <div class="text-center p-5">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد بيانات أرباح</h5>
                        <p class="text-muted">لا توجد عمليات صرافة في الفترة المحددة</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>زوج العملة</th>
                                    <th>عدد المعاملات</th>
                                    <th>حجم التداول</th>
                                    <th>العمولات</th>
                                    <th>ربح الفارق</th>
                                    <th>إجمالي الربح</th>
                                    <th>الفرع</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($daily_profits as $profit): ?>
                                <tr>
                                    <td><?php echo date('Y-m-d', strtotime($profit['profit_date'])); ?></td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($profit['currency_pair']); ?></span>
                                    </td>
                                    <td><?php echo number_format($profit['total_transactions']); ?></td>
                                    <td>$<?php echo number_format($profit['total_volume_from'], 2); ?></td>
                                    <td class="profit-positive">$<?php echo number_format($profit['total_commission'], 2); ?></td>
                                    <td class="profit-positive">$<?php echo number_format($profit['total_spread_profit'], 2); ?></td>
                                    <td class="profit-positive fw-bold">$<?php echo number_format($profit['total_profit'], 2); ?></td>
                                    <td><?php echo htmlspecialchars($profit['branch_name']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="2">الإجمالي</th>
                                    <th><?php echo number_format($totals['total_transactions']); ?></th>
                                    <th>$<?php echo number_format($totals['total_volume'], 2); ?></th>
                                    <th class="profit-positive">$<?php echo number_format($totals['total_commission'], 2); ?></th>
                                    <th class="profit-positive">$<?php echo number_format($totals['total_spread_profit'], 2); ?></th>
                                    <th class="profit-positive fw-bold">$<?php echo number_format($totals['total_profit'], 2); ?></th>
                                    <th>-</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // بيانات الرسم البياني
        const chartData = <?php echo json_encode($daily_chart_data); ?>;
        
        // إعداد الرسم البياني
        const ctx = document.getElementById('profitsChart').getContext('2d');
        const profitsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.map(item => item.profit_date),
                datasets: [{
                    label: 'الأرباح اليومية ($)',
                    data: chartData.map(item => parseFloat(item.daily_profit)),
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'عدد المعاملات',
                    data: chartData.map(item => parseInt(item.daily_transactions)),
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'تطور الأرباح اليومية'
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'الأرباح ($)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'عدد المعاملات'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

        // دوال التصدير والطباعة
        function exportToExcel() {
            // يمكن تطوير هذه الدالة لتصدير البيانات إلى Excel
            alert('ميزة التصدير إلى Excel قيد التطوير');
        }

        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
