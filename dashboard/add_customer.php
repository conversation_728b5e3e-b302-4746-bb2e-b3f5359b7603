<?php
require_once '../includes/auth.php';
require_once '../includes/customer_manager.php';

$auth = new Auth();
$customerManager = new CustomerManager();

// التحقق من صحة الجلسة والصلاحيات
if (!$auth->checkSession()) {
    header('Location: ../auth/login.php');
    exit();
}

if (!$auth->hasPermission('customers.create')) {
    header('Location: customers.php?error=ليس لديك صلاحية لإضافة عملاء جدد');
    exit();
}

$current_user = $auth->getCurrentUser();
$error_message = '';
$success_message = '';

// معالجة إضافة العميل
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $data = [
        'full_name' => trim($_POST['full_name'] ?? ''),
        'id_type' => $_POST['id_type'] ?? '',
        'id_number' => trim($_POST['id_number'] ?? ''),
        'date_of_birth' => $_POST['date_of_birth'] ?? '',
        'address' => trim($_POST['address'] ?? ''),
        'phone' => trim($_POST['phone'] ?? ''),
        'email' => trim($_POST['email'] ?? ''),
        'nationality' => trim($_POST['nationality'] ?? '')
    ];
    
    // التحقق من صحة البيانات
    $validation_errors = [];
    
    if (empty($data['full_name'])) {
        $validation_errors[] = 'الاسم الكامل مطلوب';
    }
    
    if (empty($data['id_type'])) {
        $validation_errors[] = 'نوع الهوية مطلوب';
    }
    
    if (empty($data['id_number'])) {
        $validation_errors[] = 'رقم الهوية مطلوب';
    }
    
    if (empty($data['date_of_birth'])) {
        $validation_errors[] = 'تاريخ الميلاد مطلوب';
    } else {
        // التحقق من أن العميل بالغ (18 سنة على الأقل)
        $birth_date = new DateTime($data['date_of_birth']);
        $today = new DateTime();
        $age = $today->diff($birth_date)->y;
        
        if ($age < 18) {
            $validation_errors[] = 'يجب أن يكون عمر العميل 18 سنة على الأقل';
        }
    }
    
    if (empty($data['phone'])) {
        $validation_errors[] = 'رقم الهاتف مطلوب';
    }
    
    if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $validation_errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (empty($validation_errors)) {
        $result = $customerManager->addCustomer($data, $current_user['id']);
        
        if ($result['success']) {
            $success_message = $result['message'];
            // إعادة تعيين النموذج
            $data = array_fill_keys(array_keys($data), '');
        } else {
            $error_message = $result['message'];
        }
    } else {
        $error_message = implode('<br>', $validation_errors);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عميل جديد - <?php echo SYSTEM_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .section-title {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة عميل جديد
                        </h4>
                    </div>
                    
                    <div class="card-body p-4">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success_message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" id="customerForm">
                            <!-- المعلومات الأساسية -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-user me-2"></i>
                                    المعلومات الأساسية
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-12 mb-3">
                                        <label for="full_name" class="form-label">
                                            الاسم الكامل <span class="required">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="full_name" 
                                               name="full_name" 
                                               value="<?php echo htmlspecialchars($data['full_name'] ?? ''); ?>"
                                               required>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="id_type" class="form-label">
                                            نوع الهوية <span class="required">*</span>
                                        </label>
                                        <select class="form-control" id="id_type" name="id_type" required>
                                            <option value="">اختر نوع الهوية</option>
                                            <option value="national_id" <?php echo ($data['id_type'] ?? '') === 'national_id' ? 'selected' : ''; ?>>
                                                بطاقة هوية وطنية
                                            </option>
                                            <option value="passport" <?php echo ($data['id_type'] ?? '') === 'passport' ? 'selected' : ''; ?>>
                                                جواز سفر
                                            </option>
                                            <option value="driving_license" <?php echo ($data['id_type'] ?? '') === 'driving_license' ? 'selected' : ''; ?>>
                                                رخصة قيادة
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="id_number" class="form-label">
                                            رقم الهوية <span class="required">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="id_number" 
                                               name="id_number" 
                                               value="<?php echo htmlspecialchars($data['id_number'] ?? ''); ?>"
                                               required>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="date_of_birth" class="form-label">
                                            تاريخ الميلاد <span class="required">*</span>
                                        </label>
                                        <input type="date" 
                                               class="form-control" 
                                               id="date_of_birth" 
                                               name="date_of_birth" 
                                               value="<?php echo htmlspecialchars($data['date_of_birth'] ?? ''); ?>"
                                               max="<?php echo date('Y-m-d', strtotime('-18 years')); ?>"
                                               required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="nationality" class="form-label">الجنسية</label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="nationality" 
                                               name="nationality" 
                                               value="<?php echo htmlspecialchars($data['nationality'] ?? ''); ?>"
                                               placeholder="مثال: سعودي، مصري، أردني">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- معلومات الاتصال -->
                            <div class="form-section">
                                <h5 class="section-title">
                                    <i class="fas fa-address-book me-2"></i>
                                    معلومات الاتصال
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            رقم الهاتف <span class="required">*</span>
                                        </label>
                                        <input type="tel" 
                                               class="form-control" 
                                               id="phone" 
                                               name="phone" 
                                               value="<?php echo htmlspecialchars($data['phone'] ?? ''); ?>"
                                               placeholder="+966501234567"
                                               required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" 
                                               class="form-control" 
                                               id="email" 
                                               name="email" 
                                               value="<?php echo htmlspecialchars($data['email'] ?? ''); ?>"
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" 
                                              id="address" 
                                              name="address" 
                                              rows="3"
                                              placeholder="أدخل العنوان الكامل"><?php echo htmlspecialchars($data['address'] ?? ''); ?></textarea>
                                </div>
                            </div>
                            
                            <!-- ملاحظات KYC -->
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:</h6>
                                <ul class="mb-0">
                                    <li>سيتم إنشاء ملف KYC للعميل تلقائياً بحالة "قيد المراجعة"</li>
                                    <li>يجب رفع المستندات المطلوبة وإكمال عملية التحقق قبل السماح بالمعاملات الكبيرة</li>
                                    <li>سيتم تقييم مستوى المخاطر تلقائياً كـ "منخفض" ويمكن تعديله لاحقاً</li>
                                </ul>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="customers.php" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ العميل
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من صحة النموذج
        document.getElementById('customerForm').addEventListener('submit', function(e) {
            const fullName = document.getElementById('full_name').value.trim();
            const idType = document.getElementById('id_type').value;
            const idNumber = document.getElementById('id_number').value.trim();
            const dateOfBirth = document.getElementById('date_of_birth').value;
            const phone = document.getElementById('phone').value.trim();
            
            let errors = [];
            
            if (!fullName) errors.push('الاسم الكامل مطلوب');
            if (!idType) errors.push('نوع الهوية مطلوب');
            if (!idNumber) errors.push('رقم الهوية مطلوب');
            if (!dateOfBirth) errors.push('تاريخ الميلاد مطلوب');
            if (!phone) errors.push('رقم الهاتف مطلوب');
            
            // التحقق من العمر
            if (dateOfBirth) {
                const birthDate = new Date(dateOfBirth);
                const today = new Date();
                const age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();
                
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }
                
                if (age < 18) {
                    errors.push('يجب أن يكون عمر العميل 18 سنة على الأقل');
                }
            }
            
            if (errors.length > 0) {
                e.preventDefault();
                alert('يرجى تصحيح الأخطاء التالية:\n\n' + errors.join('\n'));
            }
        });
        
        // تنسيق رقم الهاتف
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.startsWith('966')) {
                value = '+' + value;
            } else if (value.startsWith('5') && value.length === 9) {
                value = '+966' + value;
            }
            e.target.value = value;
        });
        
        // إخفاء رسائل التنبيه تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-success, .alert-danger');
            alerts.forEach(function(alert) {
                if (!alert.classList.contains('alert-info')) {
                    alert.style.transition = 'opacity 0.5s ease';
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                }
            });
        }, 5000);
    </script>
</body>
</html>
