# Trust Plus Security Configuration

# منع الوصول إلى ملفات الإعدادات الحساسة
<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

# منع عرض محتويات المجلدات
Options -Indexes

# حماية من الهجمات الشائعة
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # منع الوصول إلى ملفات النظام
    RewriteRule ^(config|includes|database)/ - [F,L]
    
    # إعادة توجيه إلى HTTPS (اختياري)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تشغيل السكريبت في المتصفح
    Header set X-XSS-Protection "1; mode=block"
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
</IfModule>
