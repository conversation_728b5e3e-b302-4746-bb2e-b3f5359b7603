# دليل وحدة التحويلات المالية - Trust Plus

## نظرة عامة

وحدة التحويلات المالية في Trust Plus هي نظام متكامل لإدارة التحويلات المالية الدولية والمحلية مع تتبع شامل للحالات وحساب الأرباح التلقائي.

## الميزات الرئيسية

### 1. إدارة التحويلات المالية

#### إنشاء تحويل جديد - نظام الخطوات المتعددة
- **الخطوة 1**: بيانات المرسل مع البحث السريع
- **الخطوة 2**: تفاصيل المستفيد والبلد المقصود
- **الخطوة 3**: تفاصيل التحويل مع حساب تلقائي للمبالغ
- **الخطوة 4**: مراجعة شاملة وتأكيد العملية

#### خصائص متقدمة
- **حساب تلقائي**: حساب فوري للمبالغ والرسوم وأسعار الصرف
- **تحقق من KYC**: فحص تلقائي لحالة العميل والامتثال
- **إعدادات مرنة**: إعدادات مختلفة لكل ممر (corridor) وبلد
- **تتبع الأرباح**: حساب تلقائي لأرباح الصرف والرسوم

### 2. نظام تتبع الحالات المتقدم

#### حالات التحويل
- **معلق (Pending)**: تم إنشاء التحويل وفي انتظار المعالجة
- **مرسل (Sent)**: تم إرسال التحويل للشريك أو البنك
- **مستلم (Received)**: تم استلام التحويل من قبل الوكيل
- **مدفوع (Paid)**: تم دفع المبلغ للمستفيد
- **ملغي (Cancelled)**: تم إلغاء التحويل

#### تتبع مفصل
- **تاريخ كامل**: سجل زمني لجميع تغييرات الحالة
- **ملاحظات**: إمكانية إضافة ملاحظات لكل تحديث
- **الموقع**: تتبع الموقع الجغرافي للتحديثات
- **التوقيت المتوقع**: حساب تلقائي للوقت المتوقع للإنجاز
- **المسؤول**: تسجيل الموظف المسؤول عن كل تحديث

### 3. حساب الأرباح المتطور

#### مكونات الربح
- **رسوم التحويل**: رسوم ثابتة أو نسبة مئوية
- **ربح الصرف**: الفرق بين سعر الشراء والبيع
- **عمولة الشريك**: خصم عمولة الشركاء والوكلاء
- **إجمالي الربح**: مجموع جميع مصادر الربح

#### تتبع الأرباح
- **أرباح يومية**: تجميع تلقائي حسب التاريخ والممر
- **أرباح حسب الممر**: تفصيل لكل زوج عملة/بلد
- **أرباح حسب الفرع**: توزيع الأرباح على الفروع
- **إحصائيات متقدمة**: متوسط الربح لكل تحويل

### 4. إدارة الشركاء والممرات

#### شركاء التحويل
- **البنوك**: البنوك المحلية والدولية
- **الوكلاء**: وكلاء الصرافة والتحويل
- **الخدمات الإلكترونية**: منصات التحويل الرقمية
- **بيوت الصرافة**: شركاء الصرافة المحليين

#### إعدادات الممرات
- **الحدود**: حد أدنى وأقصى لكل ممر
- **الرسوم**: رسوم مختلفة حسب المبلغ والوجهة
- **أوقات المعالجة**: توقيتات مختلفة حسب الشريك
- **العملات المدعومة**: قائمة العملات لكل شريك

## الهيكل التقني

### قاعدة البيانات

#### الجداول الرئيسية
```sql
-- التحويلات المالية
financial_transfers (
    id, transaction_id, sender_customer_id,
    recipient_name, recipient_country, recipient_bank,
    sending_currency_id, sending_amount,
    receiving_currency_id, receiving_amount,
    exchange_rate_used, transfer_fee_amount, status
)

-- تتبع حالات التحويلات
transfer_status_history (
    id, transfer_id, old_status, new_status,
    status_date, changed_by_user_id, notes,
    location, estimated_completion_date
)

-- شركاء التحويل
transfer_partners (
    id, partner_name, partner_type, country,
    commission_rate, processing_time_hours,
    supported_currencies, is_active
)

-- إعدادات التحويلات
transfer_settings (
    id, corridor, from_currency_id, to_currency_id,
    from_country, to_country, min_amount, max_amount,
    base_fee, percentage_fee, exchange_margin,
    processing_time_hours, partner_id
)

-- أرباح التحويلات اليومية
daily_transfer_profits (
    id, profit_date, corridor,
    total_transfers, total_volume_sent, total_volume_received,
    total_fees, total_exchange_profit, total_profit
)

-- إشعارات التحويلات
transfer_notifications (
    id, transfer_id, notification_type,
    recipient, message, status, sent_at
)
```

### الفئات البرمجية

#### TransferManager
```php
class TransferManager {
    // إنشاء تحويل مالي جديد
    public function createTransfer($data, $user_id)
    
    // تحديث حالة التحويل
    public function updateTransferStatus($transfer_id, $new_status, $user_id, $notes, $location)
    
    // حساب مبالغ التحويل والرسوم
    public function calculateTransferAmounts($data, $settings)
    
    // الحصول على تفاصيل التحويل مع تاريخ الحالات
    public function getTransferDetails($transfer_id)
    
    // البحث عن التحويلات مع فلاتر متقدمة
    public function searchTransfers($filters, $limit, $offset)
}
```

### الملفات الرئيسية

#### واجهات المستخدم
- **dashboard/transfers.php** - الصفحة الرئيسية للتحويلات
- **dashboard/transfer_details.php** - تفاصيل التحويل وتحديث الحالة
- **dashboard/transfer_receipt.php** - إيصال التحويل
- **dashboard/transfer_history.php** - سجل التحويلات

#### معالجة البيانات
- **dashboard/process_transfer.php** - معالجة التحويلات الجديدة
- **dashboard/calculate_transfer.php** - API حساب التحويل
- **dashboard/search_customers.php** - البحث عن العملاء

#### الفئات المساعدة
- **includes/transfer_manager.php** - فئة إدارة التحويلات

## الصلاحيات والأدوار

### صلاحيات التحويلات
- **transfers.view** - عرض التحويلات المالية
- **transfers.create** - إنشاء تحويلات مالية
- **transfers.edit** - تعديل وتحديث حالة التحويلات
- **transfers.approve** - اعتماد التحويلات المالية
- **transfers.history** - عرض تاريخ التحويلات

### توزيع الصلاحيات
- **مدير النظام**: جميع الصلاحيات
- **مدير الفرع**: جميع الصلاحيات عدا الاعتماد النهائي
- **المحاسب**: عرض التقارير والتحويلات
- **أمين الصندوق**: إنشاء وتحديث التحويلات
- **الموظف التشغيلي**: عرض التحويلات فقط

## خوارزميات الحساب

### حساب مبلغ التحويل
```
المبلغ المستلم = المبلغ المرسل × (سعر الصرف - هامش الربح)
```

### حساب رسوم التحويل
```
الرسوم النسبية = المبلغ المرسل × نسبة الرسوم
رسوم التحويل = max(الرسوم النسبية, الرسوم الثابتة)
```

### حساب ربح الصرف
```
ربح الصرف = المبلغ المرسل × سعر الصرف × هامش الربح
```

### حساب عمولة الشريك
```
عمولة الشريك = رسوم التحويل × نسبة عمولة الشريك
```

### إجمالي الربح
```
إجمالي الربح = ربح الصرف + (رسوم التحويل - عمولة الشريك)
```

## نظام الإشعارات

### أنواع الإشعارات
- **SMS**: رسائل نصية للعملاء
- **Email**: رسائل بريد إلكتروني
- **System**: إشعارات داخل النظام

### أحداث الإشعارات
- **إنشاء التحويل**: إشعار بإنشاء التحويل
- **تحديث الحالة**: إشعار بكل تغيير في الحالة
- **اكتمال التحويل**: إشعار بوصول المبلغ للمستفيد
- **إلغاء التحويل**: إشعار بإلغاء العملية

## الأمان والامتثال

### فحوصات الأمان
- **التحقق من KYC**: فحص حالة العميل قبل التحويل
- **حدود المعاملات**: فحص الحدود اليومية والشهرية
- **القائمة السوداء**: منع التحويل للعملاء المحظورين
- **مكافحة غسل الأموال**: فحوصات AML تلقائية

### حماية البيانات
- **تشفير البيانات**: حماية معلومات التحويل الحساسة
- **سجلات التدقيق**: تتبع جميع العمليات والتغييرات
- **التوقيعات الرقمية**: ضمان سلامة البيانات
- **النسخ الاحتياطية**: حفظ تلقائي للبيانات المهمة

## التقارير والإحصائيات

### تقارير التحويلات
- **تقرير يومي**: ملخص تحويلات اليوم
- **تقرير الأرباح**: تفصيل الأرباح حسب الممر
- **تقرير الشركاء**: أداء كل شريك
- **تقرير الامتثال**: فحوصات AML وKYC

### إحصائيات فورية
- **عدد التحويلات**: إحصائيات حية للتحويلات
- **حجم التداول**: إجمالي المبالغ المحولة
- **معدل الإنجاز**: نسبة التحويلات المكتملة
- **متوسط وقت المعالجة**: الوقت المتوسط لكل حالة

## استكشاف الأخطاء

### مشاكل شائعة
1. **فشل حساب التحويل**: تحقق من أسعار الصرف وإعدادات الممر
2. **رفض العميل**: راجع حالة KYC والقائمة السوداء
3. **تجاوز الحدود**: تحقق من حدود الممر والعميل
4. **خطأ في الشريك**: راجع إعدادات الشريك والاتصال

### حلول سريعة
- **إعادة حساب المبالغ**: من صفحة تفاصيل التحويل
- **تحديث حالة العميل**: من صفحة إدارة العملاء
- **مراجعة إعدادات الممر**: من صفحة إعدادات التحويلات
- **اختبار اتصال الشريك**: من صفحة إدارة الشركاء

## التطوير المستقبلي

### ميزات مخططة
- **تكامل مع البنوك**: ربط مباشر مع أنظمة البنوك
- **تحويلات فورية**: معالجة فورية للتحويلات المحلية
- **ذكاء اصطناعي**: تحليل المخاطر وكشف الاحتيال
- **تطبيق موبايل**: واجهة محمولة للعملاء والموظفين
- **بلوك تشين**: استخدام تقنية البلوك تشين للشفافية

### تحسينات مقترحة
- **واجهة API**: واجهة برمجية للتكامل مع أنظمة خارجية
- **تقارير متقدمة**: تقارير تفاعلية مع رسوم بيانية
- **إشعارات ذكية**: إشعارات مخصصة حسب تفضيلات العميل
- **نظام نقاط**: برنامج ولاء للعملاء المتكررين

---

**ملاحظة**: هذا النظام مصمم للعمل كتطبيق مكتبي مستقل مع إمكانيات توسع مستقبلية للتكامل مع أنظمة خارجية.
