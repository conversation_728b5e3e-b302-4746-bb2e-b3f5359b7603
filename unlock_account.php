<?php
/**
 * Trust Plus - Account Unlocker
 * إلغاء قفل الحساب
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔓 إلغاء قفل حساب admin</h1>";
echo "<hr>";

try {
    // تضمين الملفات
    if (file_exists('config.php')) {
        require_once 'config.php';
    }
    
    require_once 'includes/database.php';
    
    // الاتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ فشل الاتصال بقاعدة البيانات";
        echo "</div>";
        exit();
    }
    
    echo "✅ الاتصال بقاعدة البيانات ناجح<br><br>";
    
    // فحص حالة الحساب
    echo "<h2>🔍 فحص حالة حساب admin...</h2>";
    
    $stmt = $db->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin_user = $stmt->fetch();
    
    if (!$admin_user) {
        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ المستخدم admin غير موجود!";
        echo "</div>";
        exit();
    }
    
    // عرض حالة الحساب
    echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📊 حالة الحساب الحالية:</h3>";
    echo "<p><strong>اسم المستخدم:</strong> " . htmlspecialchars($admin_user['username']) . "</p>";
    echo "<p><strong>نشط:</strong> " . ($admin_user['is_active'] ? '✅ نعم' : '❌ لا') . "</p>";
    echo "<p><strong>محاولات فاشلة:</strong> " . $admin_user['failed_login_attempts'] . "</p>";
    echo "<p><strong>مقفل حتى:</strong> " . ($admin_user['locked_until'] ? '🔒 ' . $admin_user['locked_until'] : '🔓 غير مقفل') . "</p>";
    echo "<p><strong>آخر تسجيل دخول:</strong> " . ($admin_user['last_login'] ? $admin_user['last_login'] : 'لم يسجل دخول من قبل') . "</p>";
    echo "</div>";
    
    // التحقق من حاجة الحساب للإصلاح
    $needs_fix = false;
    $issues = [];
    
    if (!$admin_user['is_active']) {
        $needs_fix = true;
        $issues[] = "الحساب غير نشط";
    }
    
    if ($admin_user['failed_login_attempts'] > 0) {
        $needs_fix = true;
        $issues[] = "يوجد محاولات فاشلة: " . $admin_user['failed_login_attempts'];
    }
    
    if ($admin_user['locked_until'] && strtotime($admin_user['locked_until']) > time()) {
        $needs_fix = true;
        $issues[] = "الحساب مقفل حتى: " . $admin_user['locked_until'];
    }
    
    if ($needs_fix) {
        echo "<div style='color: orange; background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>⚠️ مشاكل مكتشفة:</h3>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        echo "<p>سأقوم بإصلاح هذه المشاكل الآن...</p>";
        echo "</div>";
        
        // إصلاح المشاكل
        $stmt = $db->prepare("
            UPDATE users 
            SET is_active = 1, 
                failed_login_attempts = 0, 
                locked_until = NULL,
                updated_at = NOW()
            WHERE username = 'admin'
        ");
        
        if ($stmt->execute()) {
            echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>✅ تم إصلاح جميع المشاكل!</h3>";
            echo "<ul>";
            echo "<li>✅ تم تفعيل الحساب</li>";
            echo "<li>✅ تم إعادة تعيين محاولات الفشل إلى 0</li>";
            echo "<li>✅ تم إلغاء قفل الحساب</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "❌ فشل في إصلاح المشاكل";
            echo "</div>";
        }
    } else {
        echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ الحساب في حالة جيدة!</h3>";
        echo "<p>لا توجد مشاكل تحتاج إلى إصلاح</p>";
        echo "</div>";
    }
    
    // إعادة تعيين كلمة المرور للتأكد
    echo "<h2>🔐 إعادة تعيين كلمة المرور...</h2>";
    
    $new_password = 'admin123';
    $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
    
    $stmt = $db->prepare("UPDATE users SET password_hash = :hash WHERE username = 'admin'");
    $stmt->bindParam(':hash', $new_hash);
    
    if ($stmt->execute()) {
        echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ تم إعادة تعيين كلمة المرور!</h3>";
        echo "<p><strong>كلمة المرور الجديدة:</strong> admin123</p>";
        echo "</div>";
    } else {
        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ فشل في إعادة تعيين كلمة المرور";
        echo "</div>";
    }
    
    // اختبار تسجيل الدخول النهائي
    echo "<h2>🧪 اختبار تسجيل الدخول النهائي...</h2>";
    
    require_once 'includes/auth.php';
    $auth = new Auth();
    
    $login_result = $auth->login('admin', 'admin123');
    
    if ($login_result['success']) {
        echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🎉 نجح تسجيل الدخول!</h3>";
        echo "<p>الحساب يعمل بشكل مثالي الآن</p>";
        echo "</div>";
        
        // الحصول على معلومات المستخدم
        $user = $auth->getCurrentUser();
        if ($user) {
            echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>👤 معلومات الجلسة:</h4>";
            echo "<p><strong>ID:</strong> " . $user['id'] . "</p>";
            echo "<p><strong>اسم المستخدم:</strong> " . htmlspecialchars($user['username']) . "</p>";
            echo "<p><strong>الاسم الكامل:</strong> " . htmlspecialchars($user['full_name']) . "</p>";
            echo "<p><strong>البريد الإلكتروني:</strong> " . htmlspecialchars($user['email']) . "</p>";
            echo "</div>";
        }
        
        $auth->logout();
        
    } else {
        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ فشل تسجيل الدخول</h3>";
        echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($login_result['message']) . "</p>";
        echo "</div>";
    }
    
    // معلومات نهائية
    echo "<div style='color: blue; background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎯 معلومات تسجيل الدخول:</h3>";
    echo "<p><strong>اسم المستخدم:</strong> admin</p>";
    echo "<p><strong>كلمة المرور:</strong> admin123</p>";
    echo "<p><strong>حالة الحساب:</strong> نشط ومفتوح</p>";
    echo "<p><strong>رابط تسجيل الدخول:</strong> <a href='auth/login.php' target='_blank'>auth/login.php</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ خطأ في النظام</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🔗 الخطوات التالية</h2>";
echo "<p>1. <a href='auth/login.php' target='_blank'>🔑 اذهب لصفحة تسجيل الدخول</a></p>";
echo "<p>2. استخدم: <strong>admin</strong> / <strong>admin123</strong></p>";
echo "<p>3. إذا لم يعمل، <a href='check_password.php' target='_blank'>🔍 افحص كلمة المرور</a></p>";
echo "<p>4. أو <a href='debug.php' target='_blank'>🐛 شخص النظام</a></p>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
