<?php
/**
 * Trust Plus - Test Exchange Fixes
 * اختبار إصلاحات نظام الصرافة
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 اختبار إصلاحات نظام الصرافة</h1>";
echo "<hr>";

try {
    // تحميل الملفات
    require_once 'config.php';
    require_once 'includes/database.php';
    require_once 'includes/auth.php';
    
    // بدء الجلسة
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    echo "<h2>📋 فحص الملفات والإعدادات</h2>";
    
    // فحص الملفات المطلوبة
    $files_to_check = [
        'dashboard/exchange.php' => 'صفحة عمليات الصرافة',
        'dashboard/get_exchange_rate.php' => 'API أسعار الصرف',
        'dashboard/search_customers.php' => 'API البحث عن العملاء',
        'includes/exchange_manager.php' => 'مدير عمليات الصرافة'
    ];
    
    foreach ($files_to_check as $file => $description) {
        if (file_exists($file)) {
            echo "<p>✅ $description: <code>$file</code></p>";
        } else {
            echo "<p>❌ $description: <code>$file</code> - مفقود</p>";
        }
    }
    
    echo "<hr>";
    echo "<h2>🔐 اختبار تسجيل الدخول</h2>";
    
    $auth = new Auth();
    $login_result = $auth->login('admin', 'admin123');
    
    if ($login_result['success']) {
        echo "<p>✅ تسجيل الدخول نجح</p>";
        
        $current_user = $auth->getCurrentUser();
        if ($current_user) {
            echo "<p>✅ تم الحصول على معلومات المستخدم</p>";
            echo "<p>👤 المستخدم: " . htmlspecialchars($current_user['full_name']) . "</p>";
        }
        
        echo "<hr>";
        echo "<h2>💰 فحص العملات المتاحة</h2>";
        
        $database = new Database();
        $db = $database->getConnection();
        
        if ($db) {
            // فحص العملات
            $stmt = $db->prepare("SELECT * FROM currencies WHERE is_active = 1 ORDER BY is_base_currency DESC, name ASC");
            $stmt->execute();
            $currencies = $stmt->fetchAll();
            
            echo "<p><strong>العملات المتاحة:</strong></p>";
            echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 15px 0;'>";
            
            $shekel_found = false;
            foreach ($currencies as $currency) {
                echo "<div style='background: white; padding: 10px; border-radius: 8px; border: 1px solid #dee2e6; text-align: center;'>";
                echo "<div style='font-size: 1.5em; margin-bottom: 5px;'>" . htmlspecialchars($currency['symbol']) . "</div>";
                echo "<div style='font-weight: bold;'>" . htmlspecialchars($currency['name']) . "</div>";
                echo "<div style='color: #6c757d; font-size: 0.9em;'>" . htmlspecialchars($currency['code']) . "</div>";
                if ($currency['is_base_currency']) {
                    echo "<div style='background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em; margin-top: 5px; display: inline-block;'>عملة أساسية</div>";
                }
                echo "</div>";
                
                if ($currency['code'] === 'ILS') {
                    $shekel_found = true;
                }
            }
            echo "</div>";
            
            if ($shekel_found) {
                echo "<p>✅ عملة الشيكل الإسرائيلي (ILS) متاحة</p>";
            } else {
                echo "<p>❌ عملة الشيكل الإسرائيلي غير موجودة</p>";
                echo "<p><a href='add_shekel_currency.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;'>إضافة عملة الشيكل</a></p>";
            }
            
            echo "<hr>";
            echo "<h2>💱 اختبار API أسعار الصرف</h2>";
            
            // اختبار API أسعار الصرف
            if (count($currencies) >= 2) {
                $currency1 = $currencies[0];
                $currency2 = $currencies[1];
                
                echo "<p>اختبار سعر الصرف من {$currency1['name']} إلى {$currency2['name']}...</p>";
                
                // محاكاة طلب API
                $api_data = [
                    'from_currency_id' => $currency1['id'],
                    'to_currency_id' => $currency2['id'],
                    'amount' => 100
                ];
                
                // اختبار get_exchange_rate.php
                $context = stream_context_create([
                    'http' => [
                        'method' => 'POST',
                        'header' => [
                            'Content-Type: application/json',
                            'Cookie: ' . session_name() . '=' . session_id()
                        ],
                        'content' => json_encode($api_data)
                    ]
                ]);
                
                $api_url = "http://localhost/Trust%20Plus/dashboard/get_exchange_rate.php";
                $response = @file_get_contents($api_url, false, $context);
                
                if ($response) {
                    $data = json_decode($response, true);
                    if ($data && isset($data['success'])) {
                        if ($data['success']) {
                            echo "<p>✅ API أسعار الصرف يعمل بشكل صحيح</p>";
                            echo "<ul>";
                            echo "<li>المبلغ المستلم: " . number_format($data['buy_amount'], 2) . "</li>";
                            echo "<li>سعر الصرف: " . number_format($data['exchange_rate'], 6) . "</li>";
                            echo "<li>العمولة: " . number_format($data['commission_amount'], 2) . "</li>";
                            echo "</ul>";
                        } else {
                            echo "<p>⚠️ API يعمل لكن هناك مشكلة: " . htmlspecialchars($data['message']) . "</p>";
                        }
                    } else {
                        echo "<p>❌ استجابة API غير صحيحة</p>";
                        echo "<pre>" . htmlspecialchars(substr($response, 0, 200)) . "...</pre>";
                    }
                } else {
                    echo "<p>❌ فشل في الوصول لـ API أسعار الصرف</p>";
                }
            }
            
            echo "<hr>";
            echo "<h2>🔍 اختبار البحث عن العملاء</h2>";
            
            // اختبار API البحث عن العملاء
            $search_url = "http://localhost/Trust%20Plus/dashboard/search_customers.php?q=test";
            $search_context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => 'Cookie: ' . session_name() . '=' . session_id()
                ]
            ]);
            
            $search_response = @file_get_contents($search_url, false, $search_context);
            if ($search_response) {
                $search_data = json_decode($search_response, true);
                if (is_array($search_data)) {
                    echo "<p>✅ API البحث عن العملاء يعمل</p>";
                    echo "<p>عدد النتائج: " . count($search_data) . "</p>";
                } else {
                    echo "<p>⚠️ API البحث يعمل لكن الاستجابة غير متوقعة</p>";
                }
            } else {
                echo "<p>❌ فشل في الوصول لـ API البحث عن العملاء</p>";
            }
            
        } else {
            echo "<p>❌ فشل اتصال قاعدة البيانات</p>";
        }
        
        // تسجيل الخروج
        $auth->logout();
        echo "<p>✅ تم تسجيل الخروج</p>";
        
    } else {
        echo "<p>❌ فشل تسجيل الدخول: " . htmlspecialchars($login_result['message']) . "</p>";
    }
    
    echo "<hr>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #28a745;'>";
    echo "<h2>🎉 ملخص الإصلاحات</h2>";
    echo "<h3>✅ المشاكل التي تم حلها:</h3>";
    echo "<ul>";
    echo "<li>✅ إصلاح خطأ JavaScript في exchange.php (مشكلة JSON response)</li>";
    echo "<li>✅ إصلاح API أسعار الصرف (get_exchange_rate.php)</li>";
    echo "<li>✅ منع عرض PHP errors في JSON output</li>";
    echo "<li>✅ إضافة عملة الشيكل الإسرائيلي (ILS) ₪</li>";
    echo "<li>✅ إضافة أسعار صرف الشيكل مقابل الدولار</li>";
    echo "<li>✅ تحسين معالجة الأخطاء في APIs</li>";
    echo "</ul>";
    
    echo "<h3>🚀 الميزات الجديدة:</h3>";
    echo "<ul>";
    echo "<li>💰 عملة الشيكل الإسرائيلي متاحة في جميع العمليات</li>";
    echo "<li>💱 أسعار صرف محدثة للشيكل</li>";
    echo "<li>🔧 APIs محسنة ومستقرة</li>";
    echo "<li>📊 معالجة أخطاء شاملة</li>";
    echo "</ul>";
    
    echo "<p><strong>🔗 اختبر النظام الآن:</strong></p>";
    echo "<p><a href='auth/login.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔑 تسجيل الدخول</a></p>";
    echo "<p><a href='dashboard/exchange.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>💱 عمليات الصرافة</a></p>";
    echo "<p><a href='dashboard/settings.php' target='_blank' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>⚙️ إدارة العملات</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في الاختبار</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>📝 تعليمات الاستخدام:</h3>";
echo "<ol>";
echo "<li><strong>تسجيل الدخول:</strong> استخدم admin / admin123</li>";
echo "<li><strong>عمليات الصرافة:</strong> اذهب لصفحة عمليات الصرافة</li>";
echo "<li><strong>اختيار العملات:</strong> الشيكل متاح الآن في قائمة العملات</li>";
echo "<li><strong>حساب الأسعار:</strong> سيتم حساب الأسعار تلقائياً</li>";
echo "<li><strong>إدارة العملات:</strong> يمكن إضافة المزيد من العملات من الإعدادات</li>";
echo "</ol>";

echo "<h3>💰 العملات المتاحة الآن:</h3>";
echo "<ul>";
echo "<li>🇺🇸 الدولار الأمريكي (USD) $</li>";
echo "<li>🇪🇺 اليورو (EUR) €</li>";
echo "<li>🇬🇧 الجنيه الإسترليني (GBP) £</li>";
echo "<li>🇸🇦 الريال السعودي (SAR) ر.س</li>";
echo "<li>🇦🇪 الدرهم الإماراتي (AED) د.إ</li>";
echo "<li>🇰🇼 الدينار الكويتي (KWD) د.ك</li>";
echo "<li>🇶🇦 الريال القطري (QAR) ر.ق</li>";
echo "<li>🇧🇭 الدينار البحريني (BHD) د.ب</li>";
echo "<li>🇮🇱 الشيكل الإسرائيلي (ILS) ₪ <span style='background: #28a745; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;'>جديد</span></li>";
echo "</ul>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    overflow-x: auto;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul, ol {
    line-height: 1.6;
}
</style>
