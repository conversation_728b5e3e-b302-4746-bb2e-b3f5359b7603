<?php
/**
 * Trust Plus - Password Checker & Fixer
 * فحص وإصلاح كلمة المرور
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 فحص كلمة مرور المستخدم admin</h1>";
echo "<hr>";

try {
    // تضمين الملفات
    if (file_exists('config.php')) {
        require_once 'config.php';
        echo "✅ تم تحميل config.php<br>";
    }
    
    require_once 'includes/database.php';
    echo "✅ تم تحميل database.php<br>";
    
    // الاتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ فشل الاتصال بقاعدة البيانات<br>";
        echo "تأكد من تشغيل MySQL وصحة إعدادات قاعدة البيانات";
        echo "</div>";
        exit();
    }
    
    echo "✅ الاتصال بقاعدة البيانات ناجح<br><br>";
    
    // البحث عن المستخدم admin
    echo "<h2>👤 البحث عن المستخدم admin...</h2>";
    
    $stmt = $db->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin_user = $stmt->fetch();
    
    if (!$admin_user) {
        echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ المستخدم admin غير موجود!</h3>";
        echo "<p>سأقوم بإنشاء المستخدم الآن...</p>";
        echo "</div>";
        
        // إنشاء المستخدم admin
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $db->prepare("
            INSERT INTO users (username, email, password_hash, full_name, role_id, branch_id, is_active) 
            VALUES ('admin', '<EMAIL>', :password_hash, 'مدير النظام', 1, 1, 1)
        ");
        $stmt->bindParam(':password_hash', $password_hash);
        
        if ($stmt->execute()) {
            echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>✅ تم إنشاء المستخدم admin بنجاح!</h3>";
            echo "<p><strong>اسم المستخدم:</strong> admin</p>";
            echo "<p><strong>كلمة المرور:</strong> admin123</p>";
            echo "</div>";
            
            // إعادة جلب المستخدم
            $stmt = $db->prepare("SELECT * FROM users WHERE username = 'admin'");
            $stmt->execute();
            $admin_user = $stmt->fetch();
        } else {
            echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "❌ فشل في إنشاء المستخدم admin";
            echo "</div>";
            exit();
        }
    }
    
    // عرض معلومات المستخدم
    echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📋 معلومات المستخدم admin:</h3>";
    echo "<p><strong>ID:</strong> " . $admin_user['id'] . "</p>";
    echo "<p><strong>اسم المستخدم:</strong> " . htmlspecialchars($admin_user['username']) . "</p>";
    echo "<p><strong>البريد الإلكتروني:</strong> " . htmlspecialchars($admin_user['email']) . "</p>";
    echo "<p><strong>الاسم الكامل:</strong> " . htmlspecialchars($admin_user['full_name']) . "</p>";
    echo "<p><strong>نشط:</strong> " . ($admin_user['is_active'] ? 'نعم' : 'لا') . "</p>";
    echo "<p><strong>محاولات فاشلة:</strong> " . $admin_user['failed_login_attempts'] . "</p>";
    echo "<p><strong>مقفل حتى:</strong> " . ($admin_user['locked_until'] ? $admin_user['locked_until'] : 'غير مقفل') . "</p>";
    echo "<p><strong>تاريخ الإنشاء:</strong> " . $admin_user['created_at'] . "</p>";
    echo "</div>";
    
    // اختبار كلمات المرور المختلفة
    echo "<h2>🔐 اختبار كلمات المرور...</h2>";
    
    $test_passwords = ['admin123', 'admin', '123456', 'password', 'trustplus'];
    $current_hash = $admin_user['password_hash'];
    
    echo "<p><strong>Hash الحالي:</strong> <code style='background: #f8f9fa; padding: 2px 5px; border-radius: 3px;'>" . substr($current_hash, 0, 50) . "...</code></p>";
    
    $correct_password = null;
    
    foreach ($test_passwords as $test_password) {
        if (password_verify($test_password, $current_hash)) {
            echo "<div style='color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ كلمة المرور الصحيحة: <strong>$test_password</strong>";
            echo "</div>";
            $correct_password = $test_password;
            break;
        } else {
            echo "<div style='color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ كلمة المرور خاطئة: <strong>$test_password</strong>";
            echo "</div>";
        }
    }
    
    if (!$correct_password) {
        echo "<div style='color: orange; background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>⚠️ لم يتم العثور على كلمة المرور الصحيحة!</h3>";
        echo "<p>سأقوم بإعادة تعيين كلمة المرور إلى: <strong>admin123</strong></p>";
        echo "</div>";
        
        // إعادة تعيين كلمة المرور
        $new_password = 'admin123';
        $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
        
        $stmt = $db->prepare("UPDATE users SET password_hash = :hash, failed_login_attempts = 0, locked_until = NULL WHERE username = 'admin'");
        $stmt->bindParam(':hash', $new_hash);
        
        if ($stmt->execute()) {
            echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>✅ تم إعادة تعيين كلمة المرور بنجاح!</h3>";
            echo "<p><strong>اسم المستخدم:</strong> admin</p>";
            echo "<p><strong>كلمة المرور الجديدة:</strong> admin123</p>";
            echo "</div>";
            $correct_password = $new_password;
        } else {
            echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "❌ فشل في إعادة تعيين كلمة المرور";
            echo "</div>";
        }
    }
    
    // اختبار تسجيل الدخول
    if ($correct_password) {
        echo "<h2>🧪 اختبار تسجيل الدخول...</h2>";
        
        require_once 'includes/auth.php';
        $auth = new Auth();
        
        $login_result = $auth->login('admin', $correct_password);
        
        if ($login_result['success']) {
            echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>🎉 تسجيل الدخول ناجح!</h3>";
            echo "<p>النظام يعمل بشكل صحيح الآن</p>";
            echo "</div>";
            
            $auth->logout();
        } else {
            echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>❌ فشل تسجيل الدخول</h3>";
            echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($login_result['message']) . "</p>";
            echo "</div>";
        }
    }
    
    // معلومات نهائية
    echo "<div style='color: blue; background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📝 معلومات تسجيل الدخول النهائية:</h3>";
    echo "<p><strong>اسم المستخدم:</strong> admin</p>";
    echo "<p><strong>كلمة المرور:</strong> " . ($correct_password ? $correct_password : 'admin123') . "</p>";
    echo "<p><strong>رابط تسجيل الدخول:</strong> <a href='auth/login.php' target='_blank'>auth/login.php</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ خطأ في النظام</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🔗 روابط مفيدة</h2>";
echo "<p><a href='auth/login.php' target='_blank'>🔑 صفحة تسجيل الدخول</a></p>";
echo "<p><a href='setup_database.php' target='_blank'>🗄️ إعداد قاعدة البيانات</a></p>";
echo "<p><a href='debug.php' target='_blank'>🐛 تشخيص النظام</a></p>";
echo "<p><a href='test_login_simple.php' target='_blank'>🧪 اختبار تسجيل الدخول</a></p>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

code {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}
</style>
