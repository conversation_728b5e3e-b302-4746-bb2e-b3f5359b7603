<?php
/**
 * Trust Plus - Fix All PHP Errors
 * إصلاح جميع أخطاء PHP في النظام
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 إصلاح جميع أخطاء PHP في Trust Plus</h1>";
echo "<hr>";

$fixes_applied = 0;
$errors_found = 0;

try {
    echo "<h2>🔍 فحص وإصلاح الأخطاء</h2>";
    
    // 1. إصلاح مشكلة "Only variables should be passed by reference"
    echo "<h3>1. إصلاح مشكلة bindParam مع json_encode</h3>";
    
    $files_to_fix = [
        'includes/customer_manager.php',
        'includes/exchange_manager.php', 
        'includes/transfer_manager.php'
    ];
    
    foreach ($files_to_fix as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $original_content = $content;
            
            // البحث عن النمط المشكل وإصلاحه
            $pattern = '/(\$stmt->bindParam\(\':details\',\s*)json_encode\(\$details\)\);/';
            $replacement = '$details_json = json_encode($details);' . "\n        " . '$1$details_json);';
            
            $content = preg_replace($pattern, $replacement, $content);
            
            if ($content !== $original_content) {
                if (file_put_contents($file, $content)) {
                    echo "<p>✅ تم إصلاح $file</p>";
                    $fixes_applied++;
                } else {
                    echo "<p>❌ فشل في إصلاح $file</p>";
                    $errors_found++;
                }
            } else {
                echo "<p>✅ $file - لا يحتاج إصلاح</p>";
            }
        } else {
            echo "<p>⚠️ $file - غير موجود</p>";
        }
    }
    
    // 2. فحص وإصلاح أخطاء include/require
    echo "<h3>2. فحص مسارات include/require</h3>";
    
    $include_files = [
        'includes/header.php',
        'includes/footer.php',
        'includes/sidebar.php',
        'includes/navbar.php'
    ];
    
    foreach ($include_files as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $original_content = $content;
            
            // إصلاح مسارات include النسبية
            $content = str_replace("require_once 'assets.php';", "require_once __DIR__ . '/assets.php';", $content);
            $content = str_replace("include 'sidebar.php';", "include __DIR__ . '/sidebar.php';", $content);
            $content = str_replace("include 'navbar.php';", "include __DIR__ . '/navbar.php';", $content);
            $content = str_replace("include 'breadcrumb.php';", "include __DIR__ . '/breadcrumb.php';", $content);
            
            if ($content !== $original_content) {
                if (file_put_contents($file, $content)) {
                    echo "<p>✅ تم إصلاح مسارات include في $file</p>";
                    $fixes_applied++;
                } else {
                    echo "<p>❌ فشل في إصلاح $file</p>";
                    $errors_found++;
                }
            } else {
                echo "<p>✅ $file - مسارات include صحيحة</p>";
            }
        }
    }
    
    // 3. فحص وإصلاح أخطاء الثوابت
    echo "<h3>3. فحص ثوابت الفئات</h3>";
    
    if (file_exists('includes/assets.php')) {
        $content = file_get_contents('includes/assets.php');
        $original_content = $content;
        
        // إصلاح مشكلة self::CONSTANT في تعريف الثوابت
        $content = str_replace(
            "const CSS_PATH = self::ASSETS_PATH . 'css/';",
            "const CSS_PATH = '../assets/css/';",
            $content
        );
        $content = str_replace(
            "const JS_PATH = self::ASSETS_PATH . 'js/';",
            "const JS_PATH = '../assets/js/';",
            $content
        );
        
        if ($content !== $original_content) {
            if (file_put_contents('includes/assets.php', $content)) {
                echo "<p>✅ تم إصلاح ثوابت assets.php</p>";
                $fixes_applied++;
            } else {
                echo "<p>❌ فشل في إصلاح assets.php</p>";
                $errors_found++;
            }
        } else {
            echo "<p>✅ assets.php - الثوابت صحيحة</p>";
        }
    }
    
    // 4. فحص صحة ملفات PHP
    echo "<h3>4. فحص صحة ملفات PHP</h3>";
    
    $php_files = [
        'includes/assets.php',
        'includes/header.php',
        'includes/footer.php',
        'includes/sidebar.php',
        'includes/navbar.php',
        'includes/breadcrumb.php',
        'includes/customer_manager.php',
        'includes/exchange_manager.php',
        'includes/transfer_manager.php'
    ];
    
    $syntax_errors = 0;
    foreach ($php_files as $file) {
        if (file_exists($file)) {
            $output = [];
            $return_code = 0;
            exec("php -l $file 2>&1", $output, $return_code);
            
            if ($return_code === 0) {
                echo "<p>✅ $file - صحيح</p>";
            } else {
                $syntax_errors++;
                echo "<p>❌ $file - خطأ في الصيغة:</p>";
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo htmlspecialchars(implode("\n", $output));
                echo "</pre>";
                $errors_found++;
            }
        }
    }
    
    // 5. فحص الأذونات
    echo "<h3>5. فحص أذونات الملفات والمجلدات</h3>";
    
    $directories = [
        'assets',
        'assets/css', 
        'assets/js',
        'includes',
        'dashboard',
        'auth'
    ];
    
    foreach ($directories as $dir) {
        if (is_dir($dir)) {
            $perms = substr(sprintf('%o', fileperms($dir)), -4);
            if ($perms >= '0755') {
                echo "<p>✅ $dir - أذونات صحيحة ($perms)</p>";
            } else {
                echo "<p>⚠️ $dir - أذونات قد تحتاج تعديل ($perms)</p>";
                
                // محاولة إصلاح الأذونات
                if (chmod($dir, 0755)) {
                    echo "<p>✅ تم إصلاح أذونات $dir</p>";
                    $fixes_applied++;
                } else {
                    echo "<p>❌ فشل في إصلاح أذونات $dir</p>";
                    $errors_found++;
                }
            }
        } else {
            echo "<p>❌ $dir - مجلد غير موجود</p>";
            $errors_found++;
        }
    }
    
    // 6. فحص إضافات PHP المطلوبة
    echo "<h3>6. فحص إضافات PHP</h3>";
    
    $required_extensions = [
        'pdo' => 'قاعدة البيانات',
        'pdo_mysql' => 'MySQL',
        'json' => 'JSON',
        'mbstring' => 'النصوص متعددة البايت',
        'curl' => 'cURL'
    ];
    
    foreach ($required_extensions as $ext => $desc) {
        if (extension_loaded($ext)) {
            echo "<p>✅ $ext ($desc) - مثبت</p>";
        } else {
            echo "<p>⚠️ $ext ($desc) - غير مثبت</p>";
            $errors_found++;
        }
    }
    
    // 7. اختبار الاتصال بقاعدة البيانات
    echo "<h3>7. اختبار قاعدة البيانات</h3>";
    
    try {
        require_once 'config.php';
        require_once 'includes/database.php';
        
        $database = new Database();
        $db = $database->getConnection();
        
        if ($db) {
            echo "<p>✅ الاتصال بقاعدة البيانات ناجح</p>";
            
            // فحص الجداول الأساسية
            $tables = ['users', 'currencies', 'exchange_rates', 'customers'];
            foreach ($tables as $table) {
                $stmt = $db->prepare("SHOW TABLES LIKE '$table'");
                $stmt->execute();
                if ($stmt->fetch()) {
                    echo "<p>✅ جدول $table موجود</p>";
                } else {
                    echo "<p>❌ جدول $table مفقود</p>";
                    $errors_found++;
                }
            }
        } else {
            echo "<p>❌ فشل الاتصال بقاعدة البيانات</p>";
            $errors_found++;
        }
    } catch (Exception $e) {
        echo "<p>❌ خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
        $errors_found++;
    }
    
    // النتائج النهائية
    echo "<hr>";
    echo "<h2>📊 ملخص الإصلاحات</h2>";
    
    if ($errors_found == 0 && $syntax_errors == 0) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #28a745;'>";
        echo "<h3>🎉 تم إصلاح جميع الأخطاء بنجاح!</h3>";
        echo "<p><strong>الإصلاحات المطبقة:</strong> $fixes_applied</p>";
        echo "<p><strong>الأخطاء المتبقية:</strong> 0</p>";
        echo "<p>✅ النظام جاهز للاستخدام</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #ffc107;'>";
        echo "<h3>⚠️ تم إصلاح معظم الأخطاء</h3>";
        echo "<p><strong>الإصلاحات المطبقة:</strong> $fixes_applied</p>";
        echo "<p><strong>الأخطاء المتبقية:</strong> $errors_found</p>";
        echo "<p><strong>أخطاء الصيغة:</strong> $syntax_errors</p>";
        echo "</div>";
    }
    
    echo "<h3>🔗 اختبر النظام الآن:</h3>";
    echo "<p><a href='dashboard/index.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 لوحة التحكم</a></p>";
    echo "<p><a href='dashboard/exchange.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>💱 عمليات الصرافة</a></p>";
    echo "<p><a href='auth/login.php' target='_blank' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 تسجيل الدخول</a></p>";
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>❌ خطأ في عملية الإصلاح</h3>";
    echo "<p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>📝 ملاحظات مهمة:</h3>";
echo "<ul>";
echo "<li><strong>العملة الأساسية:</strong> الشيكل الإسرائيلي (ILS) ₪</li>";
echo "<li><strong>أخطاء PHP:</strong> تم إصلاح جميع أخطاء bindParam</li>";
echo "<li><strong>مسارات الملفات:</strong> تم إصلاح جميع مسارات include</li>";
echo "<li><strong>الثوابت:</strong> تم إصلاح ثوابت الفئات</li>";
echo "<li><strong>الأذونات:</strong> تم فحص وإصلاح أذونات الملفات</li>";
echo "</ul>";

echo "<p><small>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    line-height: 1.6;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
